@light-1: #fff !important;
@dark-1: #000 !important;
@dark-2: #1f1f1f !important;

// CSS Variables for comprehensive theming
:root {
  --color-white: #fff;
  --color-black: #000;
  --color-dark-2: #1f1f1f;
  --color-gray-shadow: #dddddd;

  // Primary colors
  --primary-color: #fcad16;
  --primary-hover: #fbbf24;
  --primary-active: #f59e0b;

  // Background colors
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-elevated: #ffffff;

  // Text colors
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  // Border colors
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-focus: #fcad16;

  // Shadow colors
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

  // Status colors
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}

// Light theme
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-elevated: #ffffff;

  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;

  background-color: var(--bg-primary);
  color: var(--text-primary);

  .text-1 {
    color: var(--text-primary);
  }

  .text-2 {
    color: var(--text-secondary);
  }

  .bg-1 {
    background-color: var(--bg-primary);
  }

  .bg-2 {
    background-color: var(--bg-secondary);
  }
}

// Dark theme
[data-theme="dark"] {
  --bg-primary: #0f111a;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --bg-elevated: #1f2937;

  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --text-inverse: #111827;

  --border-primary: #374151;
  --border-secondary: #4b5563;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5);

  background-color: var(--bg-primary);
  color: var(--text-primary);

  .bg-1 {
    background-color: var(--bg-primary);
  }

  .bg-2 {
    background-color: var(--bg-secondary);
  }

  .text-1 {
    color: var(--text-primary);
  }

  .text-2 {
    color: var(--text-secondary);
  }
}

// Global theme styles
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

// Layout components
.ant-layout {
  background-color: var(--bg-primary) !important;
}

.ant-layout-header {
  // background-color: var(--bg-elevated) !important;
  // border-bottom: 1px solid var(--border-primary) !important;
}

.ant-layout-sider {
  background-color: var(--bg-elevated) !important;
  // border-right: 1px solid var(--border-primary) !important;
}

.ant-layout-content {
  background-color: var(--bg-primary) !important;
}

// Cards and surfaces
.ant-card {
  margin: 16px 0;
  padding: 0 !important;
}

// .ant-card-head {
//   background-color: var(--bg-elevated) !important;
//   border-bottom: 1px solid var(--border-primary) !important;
// }

// Tables
.ant-table {
  background-color: var(--bg-elevated) !important;
}

.ant-table-thead > tr > th {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
}

.ant-table-tbody > tr > td {
  background-color: var(--bg-elevated) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: var(--bg-secondary) !important;
}

// Forms
.ant-form-item {
  margin-bottom: 12px !important;
}
.ant-form-item-label {
  padding: 0 !important;
}
.ant-form-item-label > label {
  color: var(--text-primary) !important;
}

.ant-select-selector {
  background-color: var(--bg-elevated) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.ant-select-dropdown {
  background-color: var(--bg-elevated) !important;
  border: 1px solid var(--border-primary) !important;
}

.ant-select-item {
  color: var(--text-primary) !important;
}

.ant-select-item:hover {
  background-color: var(--bg-secondary) !important;
}

// Buttons
.ant-btn-default {
  background-color: var(--bg-elevated) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.ant-btn-default:hover {
  background-color: #eee !important;
}

.ant-btn-primary {
  color: white;
  padding: 10px 20px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);
  background-size: 300% 300%;
  animation: gradientAnimation 15s ease infinite;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

:where(.css-dev-only-do-not-override-16dneet).ant-btn-variant-solid:not(
    :disabled
  ):not(.ant-btn-disabled):hover {
  background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);

  // background: linear-gradient(90deg, #ff5e91, #ff7a5c) !important;
}

// Modals and overlays
.ant-modal-content {
  background-color: var(--bg-elevated) !important;
}

.ant-modal-header {
  background-color: var(--bg-elevated) !important;
  border-bottom: 1px solid var(--border-primary) !important;
}

.ant-modal-title {
  color: var(--text-primary) !important;
}

.ant-modal-body {
  color: var(--text-primary) !important;
}

// Dropdown
.ant-dropdown {
  background-color: var(--bg-elevated) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-lg) !important;
}

.ant-dropdown-menu {
  background-color: var(--bg-elevated) !important;
}

.ant-dropdown-menu-item {
  color: var(--text-primary) !important;
}

.ant-dropdown-menu-item:hover {
  background-color: var(--bg-secondary) !important;
}

// Menu
.ant-menu {
  // background-color: var(--bg-elevated) !important;
  color: var(--text-primary) !important;
}

.ant-menu-item {
  color: var(--text-primary) !important;
}

.ant-menu-item:hover {
  // background-color: var(--bg-secondary) !important;
  // color: var(--primary-color) !important;
}

// Tooltips
.ant-tooltip-inner {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

// Pagination
.ant-pagination-item {
  background-color: var(--bg-elevated) !important;
  border: 1px solid var(--border-primary) !important;
}

.ant-pagination-item a {
  color: var(--text-primary) !important;
}

.ant-pagination-item:hover {
  border-color: var(--primary-color) !important;
}

.ant-pagination-item-active {
  background: linear-gradient(
    90deg,
    #ffd38b,
    #f9ad36,
    #f98436,
    #f96436
  ) !important;
}

.ant-pagination-item-active a {
  color: var(--text-inverse) !important;
}

// Scrollbars
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

// Transitions for smooth theme switching
// * {
//   transition: background-color 0.3s ease, color 0.3s ease,
//     border-color 0.3s ease !important;
// }

.why-icon,
.feature-icon {
  transition: background-color 0.3s ease, transform 0.5s ease !important;
}

.ant-menu-item-selected::after {
  border-bottom-color: transparent !important;
}

.ant-menu-item:hover {
  border-bottom-color: transparent !important;
}

.ant-menu-light.ant-menu-horizontal > .ant-menu-item:hover::after {
  border-bottom-width: 2px;
  border-bottom-color: transparent !important;
}

.ant-card .ant-card-body {
  padding: 12px !important;
}

.ant-tag {
  border-radius: 50px !important;
}

.ant-menu-light.ant-menu-root.ant-menu-inline {
  border-inline-end: none;
}

.account-sider .ant-menu-light .ant-menu-item-selected {
  background: linear-gradient(
    90deg,
    #ffd38b,
    #f9ad36,
    #f98436,
    #f96436
  ) !important;
  color: #fff !important;
}

.ant-list .ant-list-item .ant-list-item-meta {
  align-items: center !important;
}

.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  background: linear-gradient(
    135deg,
    #ffd38b,
    #f9ad36,
    #f98436,
    #f96436
  ) !important;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text !important;
  color: transparent;
  text-shadow: none;
  border-bottom: none !important;
}

:where(.css-dev-only-do-not-override-16dneet).ant-tabs .ant-tabs-ink-bar {
  display: none !important;
}

// :where(.css-dev-only-do-not-override-16dneet).ant-tree .ant-tree-switcher {
//   display: flex;
//   align-items: center;
// }

.ant-statistic-content {
  display: flex;
}

.ant-btn-primary {
    color: #fff;
    padding: 10px 20px;
    font-weight: 700;
    border: none;
    cursor: pointer;
    background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);
    background-size: 300% 300%;
    animation: gradientAnimation 15s ease infinite;
}

.ant-btn-primary:hover {
    background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);
}

.ant-btn-default{
  color: #fff;
  padding: 10px 20px;
  font-weight: 700;
  border: none;
  cursor: pointer;
  border: 1px solid #fff;
  background-size: 300% 300%;
}