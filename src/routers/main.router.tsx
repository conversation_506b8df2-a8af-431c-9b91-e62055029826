import {
  DollarCircleFilled,
  FileOutlined,
  HomeOutlined,
  ProjectOutlined,
} from "@ant-design/icons";
import { IRouter } from "~/routers";
import { LoginView, RegisterView } from "~/views/auth";
import {
  HomeView,
  PrivacyPolicyView,
  TermsRegisterView,
  TermsServiceView,
} from "~/views/main";
import { ProtectedRoute } from "./ProtectedRoute";

const createRoute = (
  path: string,
  element: JSX.Element,
  title?: string,
  icon?: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children,
});

export const mainRouter: IRouter[] = [
  createRoute("login", <LoginView />, "Đăng nhập", <FileOutlined />, false),
  createRoute("", <HomeView />, "Trang chủ", <FileOutlined />, false),
  createRoute("register", <RegisterView />, "Đăng ký", <FileOutlined />, false),

  // createRoute(
  //   "account",
  //   <ProtectedRoute>
  //     <AccountView />
  //   </ProtectedRoute>,
  //   "Tài khoản",
  //   <FileOutlined />,
  //   false
  // ),
  createRoute(
    "privacy-policy",
    <PrivacyPolicyView />,
    "Chính sách bảo mật",
    <FileOutlined />,
    false
  ),
  createRoute(
    "terms-service",
    <TermsServiceView />,
    "Điều khoản dịch vụ",
    <FileOutlined />,
    false
  ),
  createRoute(
    "terms-register",
    <TermsRegisterView />,
    "Điều khoản và điều kiện",
    <FileOutlined />,
    false
  ),
];
