"use client";

import { useEffect, useRef } from "react";

type AuroraBackgroundProps = {
  blobCount?: number;
  colors?: string[]; // ví dụ: ["#8A2BE2","#FF1493","#FFA500","#00FFFF"]
  speed?: number; // 0.2-1.5 là hợp lý
  blur?: number; // px: 30-120
  opacity?: number; // 0-1
  zIndexClassName?: string; // tuỳ chỉnh z-index
};

export default function AuroraBackground({
  blobCount = 5,
  colors = ["#FF4500", "#FF6347", "#FFA500", "#FFD700"],
  speed = 0.6,
  blur = 80,
  opacity = 0.75,
  zIndexClassName = "z-0",
}: AuroraBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const rafRef = useRef<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const parent = canvas.parentElement;
    const ctx = canvas.getContext("2d", { alpha: true });
    if (!ctx) return;

    // DPR scaling để nét hơn trên màn hình retina
    const dpr = Math.max(1, Math.min(2, window.devicePixelRatio || 1));

    function resize() {
      const w = window.innerWidth;
      const h = parent?.clientHeight || window.innerHeight;
      canvas.style.width = `${w}px`;
      canvas.style.height = `${h}px`;
      canvas.width = Math.floor(w * dpr);
      canvas.height = Math.floor(h * dpr);
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    }

    resize();
    const onResize = () => resize();
    window.addEventListener("resize", onResize);

    // Blob logic
    type Blob = {
      x: number;
      y: number;
      r: number;
      hue: string;
      angle: number; // hướng ban đầu
      amp: number; // biên độ lắc
      freq: number; // tần số lắc
      vx: number; // tốc độ cơ sở theo trục x
      vy: number; // tốc độ cơ sở theo trục y
    };

    const blobs: Blob[] = [];
    const W = () => canvas.clientWidth;
    const H = () => canvas.clientHeight;

    for (let i = 0; i < blobCount; i++) {
      const baseR = Math.min(W(), H());
      blobs.push({
        x: Math.random() * W(),
        y: Math.random() * H(),
        r: baseR * (0.18 + Math.random() * 0.22), // bán kính blob
        hue: colors[i % colors.length],
        angle: Math.random() * Math.PI * 2,
        amp: 20 + Math.random() * 50,
        freq: 0.5 + Math.random() * 1.2,
        vx: (Math.random() - 0.5) * speed, // vận tốc chậm
        vy: (Math.random() - 0.5) * speed,
      });
    }

    // Dùng filter blur để mềm mại (nếu trình duyệt hỗ trợ)
    const supportsFilter =
      typeof (ctx as any).filter === "string" ||
      typeof (ctx as any).filter === "object";

    let t = 0;
    function animate() {
      const w = W();
      const h = H();
      t += 0.016; // ~60fps

      // nền tối mờ để lộ các blob
      ctx.clearRect(0, 0, w, h);
      ctx.globalAlpha = opacity;

      if (supportsFilter) {
        (ctx as any).filter = `blur(${blur}px)`;
      }

      // Chế độ hoà trộn nhẹ tạo cảm giác “phát sáng”
      const prevComp = ctx.globalCompositeOperation;
      ctx.globalCompositeOperation = "lighter";

      for (const b of blobs) {
        // dao động nhỏ theo sin/cos
        b.x += b.vx + Math.cos(b.angle + t * b.freq) * 0.3;
        b.y += b.vy + Math.sin(b.angle + t * b.freq) * 0.3;

        // bounce viền
        if (b.x < -b.r) b.x = w + b.r;
        if (b.x > w + b.r) b.x = -b.r;
        if (b.y < -b.r) b.y = h + b.r;
        if (b.y > h + b.r) b.y = -b.r;

        const grad = ctx.createRadialGradient(b.x, b.y, 0, b.x, b.y, b.r);
        // tâm sáng, viền tắt dần
        grad.addColorStop(0, b.hue);
        grad.addColorStop(0.5, `${b.hue}AA`); // hơi trong suốt
        grad.addColorStop(1, "#0000");

        ctx.fillStyle = grad;
        ctx.beginPath();
        ctx.arc(b.x, b.y, b.r, 0, Math.PI * 2);
        ctx.fill();
      }

      // khôi phục
      ctx.globalCompositeOperation = prevComp;
      if (supportsFilter) {
        (ctx as any).filter = "none";
      }
      ctx.globalAlpha = 1;

      rafRef.current = requestAnimationFrame(animate);
    }

    animate();

    return () => {
      window.removeEventListener("resize", onResize);
      cancelAnimationFrame(rafRef.current);
    };
  }, [blobCount, colors, speed, blur, opacity]);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 w-full h-full ${zIndexClassName}`}
      aria-hidden="true"
    />
  );
}
