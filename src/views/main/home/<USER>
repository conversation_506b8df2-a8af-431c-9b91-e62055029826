/* =============HeroSection============= */
.hero-section {
    height: 600px;
    position: relative;
    width: 100%;
    padding: 90px 0;
    overflow: hidden;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.animated-bg {
    position: absolute;
    inset: 0;
    z-index: 0;
}

.hero-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    max-width: 1200px;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.hero-title {
    font-size: 56px;
    font-weight: bold;
    margin-bottom: 24px;
    text-align: center;
    line-height: 1.2;
    max-width: 900px;
}

.hero-subtitle {
    font-size: 18px;
    text-align: center;
    line-height: 1.6;
    margin-bottom: 32px;
    max-width: 800px;
}

.btn {
    margin-top: 16px;
    padding: 12px 32px;
    height: auto;
    font-size: 16px;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 36px;
        margin-bottom: 16px;
    }

    .hero-subtitle {
        font-size: 16px;
        margin-bottom: 24px;
    }

    .hero-container {
        padding: 0 16px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 28px;
        margin-bottom: 12px;
    }

    .hero-subtitle {
        font-size: 14px;
        margin-bottom: 20px;
    }
}