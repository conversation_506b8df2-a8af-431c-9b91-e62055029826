/* =============HeroSection============= */
.banner-wrapper {
    position: relative;
    height: 600px;
    overflow: hidden;
}

.hero-section {
    height: 600px;
    position: relative;
    width: 100%;
    padding: 90px 0;
    overflow: hidden;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.animated-bg {
    position: absolute;
    inset: 0;
    z-index: 0;
}

.hero-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    max-width: 1200px;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.hero-content {
    animation: slideInUp 0.8s ease-out;
    opacity: 0;
    animation-fill-mode: forwards;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.hero-title {
    font-size: 56px;
    font-weight: 700;
    margin-bottom: 24px;
    text-align: center;
    line-height: 1.2;
    max-width: 900px;
    background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436) !important;
    -webkit-background-clip: text;
    color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 18px;
    text-align: center;
    line-height: 1.6;
    margin-bottom: 32px;
    max-width: 800px;
}

.btn {
    margin-top: 16px;
    padding: 12px 32px;
    height: auto;
    font-size: 16px;
    font-weight: 600;
}

.free-btn {
    background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 69, 0, 0.3);
    transition: all 0.3s ease;
    animation: buttonPulse 2s infinite;
}

.free-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 69, 0, 0.4);
    background: linear-gradient(135deg, #FF6347, #FFA500, #FFD700);
}

.btn-title {
    font-weight: 700;
    background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);
    -webkit-background-clip: text;
    color: transparent;
}

.contact-btn {
    border: none;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 69, 0, 0.4);
    background: linear-gradient(135deg, #FF6347, #FFA500, #FFD700);
}

.carousel-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 20;
    width: 50px !important;
    height: 50px !important;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 69, 0, 0.3);
    color: #FF4500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    opacity: 0.8;
}

.carousel-nav-btn:hover {
    background: rgba(255, 69, 0, 0.9);
    color: white;
    transform: translateY(-50%) scale(1.1);
    opacity: 1;
    box-shadow: 0 4px 15px rgba(255, 69, 0, 0.3);
}

.carousel-prev-btn {
    left: 30px;
}

.carousel-next-btn {
    right: 30px;
}

.custom-dots {
    bottom: 30px !important;
}

.custom-dots li button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 69, 0, 0.3);
    transition: all 0.3s ease;
}

.custom-dots li.slick-active button {
    background: #FF4500;
    border-color: #FF4500;
    transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 36px;
        margin-bottom: 16px;
    }

    .hero-subtitle {
        font-size: 16px;
        margin-bottom: 24px;
    }

    .hero-container {
        padding: 0 16px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 28px;
        margin-bottom: 12px;
    }

    .hero-subtitle {
        font-size: 14px;
        margin-bottom: 20px;
    }

    .carousel-nav-btn {
        width: 40px;
        height: 40px;
    }

    .carousel-prev-btn {
        left: 15px;
    }

    .carousel-next-btn {
        right: 15px;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes buttonPulse {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(255, 69, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(255, 69, 0, 0.5);
    }
}

.ant-carousel .slick-slide {
    transition: opacity 0.5s ease-in-out;
}

.ant-carousel .slick-slide.slick-active {
    opacity: 1;
}

.ant-carousel .slick-slide:not(.slick-active) {
    opacity: 0;
}

.animated-bg {
    transition: all 0.5s ease-in-out;
}