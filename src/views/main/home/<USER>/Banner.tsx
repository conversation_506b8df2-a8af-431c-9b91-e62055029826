import { Button, Carousel, Flex } from "antd";
import AuroraBackground from "~/components/BannerBg";
import "../style.css";
import { Typography } from "antd";
const { Title, Text } = Typography;

const sliderContents = [
    {
        title: "ApexLink - Tích hợp dữ liệu của bạn với các nền tảng blockchain",
        desc: "ApexLink là nền tảng chuyên biệt cho Web3, cho phép hệ thống Web2 tích hợp blockchain dễ dàng mà không cần phát triển smart contracts. Như BNB, Ethereum,... tiết kiệm nguồn lực kỹ thuật và rút ngắn thời gian triển khai.",
        color: "#FFF8E7",
    },
    {
        title: "AI Procurement - Nền tảng mua hàng B2B SME 360",
        desc: "<PERSON>ết nối doanh nghiệp của bạn với các nhà cung cấp phù hợp nhất.",
        color: "#FFEAC2",
    },
    {
        title: "CRM System - Quản lý khách hàng thông minh, tăng trưởng doanh số bền vững",
        desc: "Hệ thống CRM toàn diện giúp doanh nghiệp tối ưu hóa quy trình bán hàng, chăm sóc khách hàng và tăng trưởng doanh thu một cách bền vững.",
        color: "#FFDFA8",
    },
    {
        title: "HRM System - Quản lý nhân sự thông minh cho doanh nghiệp hiện đại",
        desc: "Giải pháp quản lý nhân sự toàn diện: từ chấm công, tính lương đến tuyển dụng và đánh giá hiệu suất.",
        color: "#FFD27F",
    },
  
];

const BannerSection: React.FC = () => {
    return (
        <Carousel autoplay={{ dotDuration: true }} autoplaySpeed={5000} style={{height: '600px'}}>
            {sliderContents.map((item, index) => (
                <div key={index} className="hero-section">
                    <div className="animated-bg" style={{  background: item.color }}>
                        <AuroraBackground />
                    </div>
                    <div className="hero-container">
                        <Title level={1} className="hero-title">{item.title}</Title>
                        <Text type="secondary" strong className="hero-subtitle">{item.desc}</Text>
                        <Flex>
                        {/* nút button */}
                        <Button className="btn">Xem thêm</Button>
                    </Flex>
                    </div>
                    
                </div>
            ))}
        </Carousel>
    );
};

export default BannerSection;
