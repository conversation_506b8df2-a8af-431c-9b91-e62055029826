import { Button, Carousel, Flex } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import AuroraBackground from "~/components/BannerBg";
import "../style.css";
import { Typography } from "antd";
import { useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faRightLong } from "@fortawesome/free-solid-svg-icons";
import { COLORS } from "~/common/constants";
const { Title, Text } = Typography;

const sliderContents = [
    {
        title: "ApexLink - Tích hợp dữ liệu của bạn với các nền tảng blockchain",
        desc: "ApexLink là nền tảng chuyên biệt cho Web3, cho phép hệ thống Web2 tích hợp blockchain dễ dàng mà không cần phát triển smart contracts. Như BNB, Ethereum,... tiết kiệm nguồn lực kỹ thuật và rút ngắn thời gian triển khai.",
        color: "#FFF8E7",
    },
    {
        title: "AI Procurement - Nền tảng mua hàng B2B SME 360",
        desc: "Kết nối doanh nghiệp của bạn với các nhà cung cấp phù hợp nhất.",
        color: "#FFEAC2",
    },
    {
        title: "CRM System - Quản lý khách hàng thông minh, tăng trưởng doanh số bền vững",
        desc: "Hệ thống CRM toàn diện giúp doanh nghiệp tối ưu hóa quy trình bán hàng, chăm sóc khách hàng và tăng trưởng doanh thu một cách bền vững.",
        color: "#FFDFA8",
    },
    {
        title: "HRM System - Quản lý nhân sự thông minh cho doanh nghiệp hiện đại",
        desc: "Giải pháp quản lý nhân sự toàn diện: từ chấm công, tính lương đến tuyển dụng và đánh giá hiệu suất.",
        color: "#FFD27F",
    },
  
];

const BannerSection: React.FC = () => {
    const carouselRef = useRef<any>(null);

    const goToPrev = () => {
        carouselRef.current?.prev();
    };

    const goToNext = () => {
        carouselRef.current?.next();
    };

    return (
        <div className="banner-wrapper">
            <Carousel
                ref={carouselRef}
                autoplay={{ dotDuration: true }}
                autoplaySpeed={5000}
                style={{height: '600px'}}
                effect="fade"
                dots={{ className: "custom-dots" }}
                pauseOnHover={true}
                pauseOnDotsHover={true}
            >
                {sliderContents.map((item, index) => (
                    <div key={index} className="hero-section">
                        <div className="animated-bg" style={{  background: item.color }}>
                            <AuroraBackground />
                        </div>
                        <div className="hero-container">
                            <div className="hero-content">
                                <Title level={1} className="hero-title">{item.title}</Title>
                                <Text type="secondary" strong className="hero-subtitle">{item.desc}</Text>
                                <Flex justify="center" gap={24}>
                                    <Button type="primary" className="btn free-btn">
                                        <Text style={{color: 'white'}} strong>     
                                            <FontAwesomeIcon icon={faRightLong} color={COLORS.WHITE} style={{marginRight: '8px'}}/>Dùng thử miễn phí
                                        </Text>
                                     </Button>
                                    <Button type="default" className="btn contact-btn"><Text className='btn-title'>Liên hệ</Text></Button>
                                </Flex>
                            </div>
                        </div>
                    </div>
                ))} 
            </Carousel>

            <Button
                className="carousel-nav-btn carousel-prev-btn"
                icon={<LeftOutlined />}
                onClick={goToPrev}
                shape="circle"
                size="large"
            />
            <Button
                className="carousel-nav-btn carousel-next-btn"
                icon={<RightOutlined />}
                onClick={goToNext}
                shape="circle"
                size="large"
            />
        </div>
    );
};

export default BannerSection;
