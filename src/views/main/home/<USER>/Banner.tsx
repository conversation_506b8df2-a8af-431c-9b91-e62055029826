import { Carousel } from "antd";
import AuroraBackground from "~/components/BannerBg";
const contentStyle: React.CSSProperties = {
  height: '600px',
  textAlign: 'center',
  background: '#364d79',
};
const BannerSection: React.FC = () => {
//   return <AuroraBackground />;
    return (
    // slider banner
        <Carousel autoplay={{ dotDuration: true }} autoplaySpeed={5000} style={{ height: '600px' }}>
            <div>
                <div style={{...contentStyle, background: '#fcf2ddff'}}>
                    <AuroraBackground />
                </div>
            </div>
            <div>
                <div style={{...contentStyle, background: '#364d79'}}>
                    <AuroraBackground />
                </div>
            </div>
            <div>
                <div style={{...contentStyle, background: '#364d79'}}>
                    <AuroraBackground />
                </div>
            </div>
            <div>
                <div style={{...contentStyle, background: '#364d79'}}>
                    <AuroraBackground />
                </div>
            </div>
            <div>
                <div style={{...contentStyle, background: '#364d79'}}>
                    <AuroraBackground />
                </div>
            </div>
        </Carousel>
    );
};

export default BannerSection;
