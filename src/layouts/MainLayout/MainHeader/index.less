.main-header {
  height: 80px;
  position: sticky;
  top: 0;
  z-index: 100;
  background: transparent;
  transition: all 0.3s ease;
  background: #fff;
  padding: 0;
  border-bottom: 1px solid #eee;

  &.scrolled {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  @media (max-width: 768px) {
    height: 64px;
  }
}

.main-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  transition: background-color 0.3s ease, opacity 0.3s ease;
  opacity: 1;
  // backdrop-filter: blur(6px); // để sẵn hiệu ứng nhẹ
  // -webkit-backdrop-filter: blur(6px);

  @media (max-width: 768px) {
    padding-inline: 16px;
    height: 64px;
  }
}

.main-header-left {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 8px;

  @media (max-width: 768px) {
    width: auto;
    flex: 1;
  }
}

.logo-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
  }
}

.brand-name {
  display: flex;
  flex-direction: column;
}

.main-header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-end;

  @media (max-width: 768px) {
    width: auto;
    gap: 8px;
  }
}

.mobile-menu-button {
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
}

.mobile-menu-drawer {
  .ant-drawer-header {
    display: none !important;
  }

  .ant-drawer-body {
    padding: 0;
    background: var(--bg-primary);
  }

  .ant-drawer-content {
    animation: slideInRight 0.3s ease-out;
  }

  .ant-drawer-mask {
    animation: fadeIn 0.3s ease-out;
  }
}

// Custom drawer header
.mobile-drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 64px;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-primary);
  animation: slideInFromRight 0.4s ease-out;
}

.mobile-drawer-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-drawer-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  animation: logoScale 0.5s ease-out;
}

.mobile-drawer-close-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: var(--text-primary);

  &:hover {
    background-color: var(--bg-secondary);
    transform: rotate(90deg);
  }

  &:active {
    transform: rotate(90deg) scale(0.95);
  }
}

.mobile-menu-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  animation: slideInFromRight 0.4s ease-out 0.1s both;
}

.mobile-menu-items {
  flex: 1;
  padding: 16px 0;
}

.mobile-menu-item {
  padding: 0;
  margin: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateX(30px);
  animation: slideInMenuItemFromRight 0.3s ease-out forwards;

  &:nth-child(1) { animation-delay: 0.2s; }
  &:nth-child(2) { animation-delay: 0.3s; }
  &:nth-child(3) { animation-delay: 0.4s; }
  &:nth-child(4) { animation-delay: 0.5s; }
  &:nth-child(5) { animation-delay: 0.6s; }
  &:nth-child(6) { animation-delay: 0.7s; }

  &:hover {
    background-color: var(--bg-secondary);
    transform: translateX(-5px);
  }

  &.active {
    background-color: var(--primary-color);

    .mobile-menu-item-content {
      color: #000;
    }
  }
}

.mobile-menu-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  color: var(--text-primary);
  transition: color 0.2s ease;
}

.mobile-menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;

  svg {
    width: 16px;
    height: 16px;
  }
}

.mobile-menu-actions {
  padding: 16px;
  border-top: 1px solid var(--border-primary);
  margin-top: auto;
  background: var(--bg-primary);
}

// Theme toggle in mobile menu
.mobile-menu-actions .theme-toggle-button {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;

  &:hover {
    background: var(--primary-color) !important;
    color: #000 !important;
  }
}

.custom-button {
  color: #fff !important;
  transition: opacity 0.3s ease;
}

.custom-button:hover {
  opacity: 0.9;
}

.custom-button.animated-gradient {
  color: white;
  padding: 10px 20px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  background: linear-gradient(135deg, #ffd38b, #f9ad36, #f98436, #f96436);
  background-size: 300% 300%;
  animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Fix mobile menu visibility
@media (max-width: 768px) {
  .main-header-container .ant-menu {
    display: none !important;
  }

  .mobile-menu-drawer .ant-menu {
    display: block !important;
  }
}

// Hide desktop menu on mobile
@media (max-width: 768px) {
  .layout-page-horizontal-menu {
    display: none !important;
  }
}

// Animation keyframes for drawer
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes logoScale {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInMenuItemFromRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
