import "./index.less";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography } from "antd";
import { useLayoutConfig } from "~/stores/layoutConfig";
import MainMenu from "../MainMenu";
import BaseText from "~/components/BaseText";
import logo from "~/assets/logo/logo-no-bg.png";
import { Header } from "antd/es/layout/layout";
import { useNavigate } from "react-router-dom";
import {
  MenuOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { useScreenSize } from "~/hooks/useScreenSize";
import { useEffect, useState } from "react";
import { useAuthStore } from "~/stores/authStore";
import { useLanguageConfig } from "~/i18n/useLanguageConfig";
import i18n from "~/i18n";
import vi from "~/assets/logo/vi.webp";
import en from "~/assets/logo/en.webp";
const MainHeader = () => {
  const [scrolled, setScrolled] = useState(false);
  const {
    menus,
    openKeys,
    setSelectedKey,
    flatLayoutResource,
    selectedKey,
    addTab,
  } = useLayoutConfig();

  const { isMobile } = useScreenSize();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const { currentLanguageConfig, languageList } = useLanguageConfig();
  const { logged, logout } = useAuthStore();
  const { Text } = Typography;
  const scrollToSection = (sectionId: string) => {
    if (location.pathname !== "/") {
      navigate("/");
      // Wait for navigation to complete then scroll
      setTimeout(() => {
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "start" });
        }
      }, 100);
    } else {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }
  };
  const toggleLanguage = () => {
    const currentLang = i18n.language;
    const currentIndex = languageList.indexOf(currentLang);
    const nextIndex =
      currentIndex >= 0 ? (currentIndex + 1) % languageList.length : 0;
    const nextLang = languageList[nextIndex];
    i18n.changeLanguage(nextLang);
    console.log("Language changed to:", nextLang);
  };

  // Handle menu click
  const handleMenuClick = (menu: any) => {
    const sectionMap: { [key: string]: string } = {
      "route-/": "home",
      "route-/search": "search",
      "route-/solutions": "solutions",
      "route-/about-us": "about-us",
      "route-/contact": "contact",
    };
    const sectionId = sectionMap[menu.key];

    if (sectionId) {
      scrollToSection(sectionId);
      setSelectedKey(menu.key);
    } else {
      // Fallback to original navigation
      const tab = flatLayoutResource.find((v) => v.key === menu.key);
      if (tab) {
        addTab(tab);
        setSelectedKey(menu.key);
      }
    }
  };
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
 
  return (
    <Header className={`main-header ${scrolled ? "scrolled" : ""}`}>
      <div className="main-header-container">
        <div className="main-header-left" onClick={() => navigate("/")}>
          <img src={logo} alt="logo" className="logo-img" />
          <Col className="brand-name">
            <BaseText size="xl" weight="bold">
              APETECH SOLUTION
            </BaseText>
          </Col>
        </div>
        {!isMobile && (
          <MainMenu
            style={{ flexShrink: 1, minWidth: 0 }}
            menus={menus}
            openKeys={openKeys}
            selectedKey={selectedKey}
            onMenuClick={handleMenuClick}
          />
        )}
        {/* Right - Actions */}
        <div className="main-header-actions">
          {!isMobile && (
              <Tooltip title={currentLanguageConfig.label}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "30px",
              width: "40px",
              cursor: "pointer",
              border: "1px solid #d9d9d9",
            }}
            onClick={toggleLanguage}
          >
            <img
              src={
                currentLanguageConfig.label === "Vietnamese" ? vi : en
              }
              alt={currentLanguageConfig.icon}
              style={{
                width: "100%",
                height: "auto",
                objectFit: "cover",
              }}
            />
          </div>
        </Tooltip>
          )}

          {/* Mobile menu button */}
          {isMobile && (
            <Button
              className="mobile-menu-button"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuOpen(true)}
            />
          )}
        </div>

        {/* Mobile Drawer Menu */}
        <Drawer
          title={null}
          placement="right"
          onClose={() => setMobileMenuOpen(false)}
          open={mobileMenuOpen}
          className="mobile-menu-drawer"
          width="100%"
        >
          {/* Custom Header */}
          <div className="mobile-drawer-header">
            <div className="mobile-drawer-header-left">
              <img
                src={logo}
                alt="logo"
                className="mobile-drawer-logo"
              />
              <BaseText weight="bold" size="lg">APETECH SOLUTION</BaseText>
            </div>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={() => setMobileMenuOpen(false)}
              className="mobile-drawer-close-btn"
            />
          </div>
          <div className="mobile-menu-content">
            <div className="mobile-menu-items">
              {menus.map((menu) => (
                <div
                  key={menu.key}
                  className={`mobile-menu-item ${
                    selectedKey === menu.key ? "active" : ""
                  }`}
                  onClick={() => {
                    setMobileMenuOpen(false);
                  }}
                >
                  <div className="mobile-menu-item-content">
                    <BaseText
                      weight={selectedKey === menu.key ? "semibold" : "normal"}
                    >
                      {menu.title}
                    </BaseText>
                  </div>
                </div>
              ))}
            </div>
            {/* Mobile Actions */}
            <div className="mobile-menu-actions">
              <Button type="primary" size="large" className="btn-primary">
                Dùng thử miễn phí
              </Button>
            </div>
          </div>
        </Drawer>
      </div>
    </Header>
  );
};

export default MainHeader;
