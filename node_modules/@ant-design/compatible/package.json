{"name": "@ant-design/compatible", "version": "5.1.4", "description": "Ant Design v3 to v4 compatible package", "keywords": ["antd", "compatible"], "files": ["lib", "es", "assets/*.css", "assets/*.less"], "sideEffects": ["es/**/style/*", "lib/**/style/*", "assets/*"], "main": "lib/index.js", "module": "es/index.js", "repository": "https://github.com/ant-design/compatible", "publishConfig": {"registry": "https://registry.npmjs.org"}, "scripts": {"start": "dumi dev", "dev": "father dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "compile": "father build", "deploy": "npm run docs:build && npm run docs:deploy", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "umi-test", "test:coverage": "umi-test --coverage", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "lint": "eslint src/ --ext .tsx,.ts", "lint:tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build", "watch": "father dev"}, "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.6.1", "classnames": "^2.2.6", "dayjs": "^1.11.4", "lodash.camelcase": "^4.3.0", "lodash.upperfirst": "^4.3.1", "rc-animate": "^3.1.1", "rc-form": "^2.4.12", "rc-util": "^5.24.5"}, "peerDependencies": {"antd": "^5.0.1", "react": ">=16.0.0", "react-dom": ">=16.0.0"}, "devDependencies": {"@ant-design/icons": "^4.7.0", "@rc-component/father-plugin": "^1.0.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.0", "@types/enzyme": "^3.10.5", "@types/jest": "^26.0.20", "@types/less": "^3.0.3", "@types/lodash": "^4.14.135", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^2.5.2", "antd": "^5.0.1", "dumi": "^1.1.0", "eslint": "^7.18.0", "father": "^4.0.0", "gh-pages": "^3.1.0", "less": "^4.1.3", "np": "^5.0.3", "prettier": "^2.1.2", "rc-trigger": "^5.3.1", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^4.6.3", "umi-test": "^1.9.7"}, "overrides": {"cheerio": "1.0.0-rc.12"}}