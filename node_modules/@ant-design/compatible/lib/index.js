"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Comment", {
  enumerable: true,
  get: function get() {
    return _comment.default;
  }
});
Object.defineProperty(exports, "Form", {
  enumerable: true,
  get: function get() {
    return _form.default;
  }
});
Object.defineProperty(exports, "Icon", {
  enumerable: true,
  get: function get() {
    return _icon.default;
  }
});
Object.defineProperty(exports, "convertLegacyToken", {
  enumerable: true,
  get: function get() {
    return _convertLegacyToken.default;
  }
});
Object.defineProperty(exports, "darkAlgorithm", {
  enumerable: true,
  get: function get() {
    return _theme.darkAlgorithm;
  }
});
Object.defineProperty(exports, "darkTheme", {
  enumerable: true,
  get: function get() {
    return _theme.darkTheme;
  }
});
Object.defineProperty(exports, "defaultAlgorithm", {
  enumerable: true,
  get: function get() {
    return _theme.defaultAlgorithm;
  }
});
Object.defineProperty(exports, "defaultTheme", {
  enumerable: true,
  get: function get() {
    return _theme.defaultTheme;
  }
});
var _comment = _interopRequireDefault(require("./comment"));
var _form = _interopRequireDefault(require("./form"));
var _icon = _interopRequireDefault(require("./icon"));
var _theme = require("./theme");
var _convertLegacyToken = _interopRequireDefault(require("./theme/convertLegacyToken"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }