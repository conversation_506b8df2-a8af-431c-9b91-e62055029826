import type { ThemeType } from './index';
export declare const svgBaseProps: {
    width: string;
    height: string;
    fill: string;
    'aria-hidden': boolean;
    focusable: string;
};
export declare function getThemeFromTypeName(type: string): ThemeType | null;
export declare function removeTypeTheme(type: string): string;
export declare function withThemeSuffix(type: string, theme: ThemeType): any;
export declare function alias(type: string): string;
