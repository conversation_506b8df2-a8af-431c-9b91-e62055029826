import React from 'react';
import { createFromIconfontCN, getTwoToneColor, setTwoToneColor } from '@ant-design/icons';
export interface CustomIconComponentProps {
    width: string | number;
    height: string | number;
    fill: string;
    viewBox?: string;
    className?: string;
    style?: React.CSSProperties;
    spin?: boolean;
    rotate?: number;
    ['aria-hidden']?: React.AriaAttributes['aria-hidden'];
}
export type ThemeType = 'filled' | 'outlined' | 'twoTone';
interface CoreIconProps {
    tabIndex?: number;
    className?: string;
    theme?: ThemeType;
    title?: string;
    onKeyUp?: React.KeyboardEventHandler<HTMLElement>;
    onClick?: React.MouseEventHandler<HTMLElement>;
    twoToneColor?: string;
    viewBox?: string;
    spin?: boolean;
    rotate?: number;
    style?: React.CSSProperties;
    role?: string;
}
export interface IconProps extends CoreIconProps {
    type?: string;
    component?: React.ComponentType<CustomIconComponentProps | React.SVGProps<SVGSVGElement>>;
    children?: React.ReactNode;
}
export interface IconComponent<P> extends React.FC<P> {
    createFromIconfontCN: typeof createFromIconfontCN;
    getTwoToneColor: typeof getTwoToneColor;
    setTwoToneColor: typeof setTwoToneColor;
}
declare const Icon: IconComponent<IconProps>;
export default Icon;
