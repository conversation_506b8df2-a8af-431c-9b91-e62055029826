"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
var allIcons = _interopRequireWildcard(require("@ant-design/icons/lib/icons"));
var _icons2 = _interopRequireWildcard(require("@ant-design/icons"));
var _utils = require("./utils");
var _warning = _interopRequireDefault(require("../_util/warning"));
var _upgradeMessage = _interopRequireDefault(require("../_util/upgradeMessage"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
var iconsMap = allIcons;
var LegacyTypeIcon = function LegacyTypeIcon(props) {
  var type = props.type,
    theme = props.theme;
  if (theme) {
    var themeInName = (0, _utils.getThemeFromTypeName)(type);
    (0, _warning.default)(!themeInName || theme === themeInName, 'Icon', "The icon name '".concat(type, "' already specify a theme '").concat(themeInName, "',") + " the 'theme' prop '".concat(theme, "' will be ignored."));
  }
  var computedType = (0, _utils.withThemeSuffix)((0, _utils.removeTypeTheme)((0, _utils.alias)(type)), theme || 'outlined');
  var targetIconComponent = iconsMap[computedType];
  (0, _warning.default)(targetIconComponent, 'Icon', "The icon name '".concat(type, "'").concat(theme ? "with ".concat(theme) : '', " doesn't exist, please check it at https://ant.design/components/icon"));
  return targetIconComponent ? /*#__PURE__*/_react.default.createElement(targetIconComponent, props) : null;
};
var Icon = function Icon(props) {
  var type = props.type,
    component = props.component,
    children = props.children;
  (0, _upgradeMessage.default)('Icon');
  (0, _warning.default)(Boolean(type || component || children), 'Icon', 'Should have `type` prop or `component` prop or `children`.');
  if (component || children) {
    return /*#__PURE__*/_react.default.createElement(_icons2.default, props);
  }
  if (typeof type === 'string') {
    return /*#__PURE__*/_react.default.createElement(LegacyTypeIcon, _extends({}, props, {
      type: type
    }));
  }
  return /*#__PURE__*/_react.default.createElement(_icons2.default, null);
};
Icon.createFromIconfontCN = _icons2.createFromIconfontCN;
Icon.getTwoToneColor = _icons2.getTwoToneColor;
Icon.setTwoToneColor = _icons2.setTwoToneColor;
var _default = exports.default = Icon;