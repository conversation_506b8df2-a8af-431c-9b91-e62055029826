"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useStyle;
var React = _interopRequireWildcard(require("react"));
var _cssinjs = require("@ant-design/cssinjs");
var _antd = require("antd");
var _style = require("antd/lib/style");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
// ============================== Export ==============================
var genSharedButtonStyle = function genSharedButtonStyle(token) {
  var componentCls = token.componentCls,
    colorBgContainer = token.colorBgContainer,
    fontSize = token.fontSize,
    fontSizeSM = token.fontSizeSM,
    padding = token.padding,
    paddingXS = token.paddingXS,
    marginSM = token.marginSM,
    marginXXS = token.marginXXS,
    controlHeight = token.controlHeight,
    lineHeightSM = token.lineHeightSM,
    colorText = token.colorText,
    colorTextSecondary = token.colorTextSecondary,
    colorTextTertiary = token.colorTextTertiary,
    motionDurationSlow = token.motionDurationSlow;
  return _defineProperty({}, componentCls, _objectSpread(_objectSpread({}, (0, _style.resetComponent)(token)), {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    position: 'relative',
    backgroundColor: colorBgContainer
  }, "".concat(componentCls, "-inner"), {
    display: 'flex',
    paddingBlock: padding
  }), "".concat(componentCls, "-avatar"), {
    position: 'relative',
    flexShrink: 0,
    marginInlineEnd: marginSM,
    cursor: 'pointer',
    img: {
      width: controlHeight,
      height: controlHeight,
      borderRadius: '50%'
    }
  }), "".concat(componentCls, "-content"), {
    position: 'relative',
    flex: 'auto',
    minWidth: 0,
    wordWrap: 'break-word',
    '&-author': {
      display: 'flex',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      marginBottom: marginXXS,
      '& > a, & > span': {
        paddingInlineEnd: paddingXS,
        fontSize: fontSizeSM,
        lineHeight: lineHeightSM
      },
      '&-name': {
        color: colorTextSecondary,
        fontSize: fontSize,
        transition: "color ".concat(motionDurationSlow),
        '> *': {
          color: colorTextSecondary,
          '&:hover': {
            color: colorTextSecondary
          }
        }
      },
      '&-time': {
        color: colorTextTertiary,
        whiteSpace: 'nowrap',
        cursor: 'auto'
      }
    },
    '&-detail p': {
      whiteSpace: 'pre-wrap',
      marginBlock: 0
    }
  }), "".concat(componentCls, "-actions"), {
    marginTop: marginSM,
    marginBottom: 0,
    paddingInlineStart: 0,
    '> li': {
      display: 'inline-block',
      color: colorTextSecondary,
      '> span': {
        marginInlineEnd: marginSM,
        color: colorTextSecondary,
        fontSize: fontSizeSM,
        cursor: 'pointer',
        transition: "color ".concat(motionDurationSlow),
        userSelect: 'none',
        '&:hover': {
          color: colorText
        }
      }
    }
  }), "".concat(componentCls, "-nested"), {
    marginInlineStart: 44
  })));
};
function useStyle(prefixCls) {
  var _antdTheme$useToken = _antd.theme.useToken(),
    theme = _antdTheme$useToken.theme,
    token = _antdTheme$useToken.token,
    hashId = _antdTheme$useToken.hashId;
  var _React$useContext = React.useContext(_antd.ConfigProvider.ConfigContext),
    iconPrefixCls = _React$useContext.iconPrefixCls;
  return [(0, _cssinjs.useStyleRegister)({
    theme: theme,
    token: token,
    hashId: hashId,
    path: ['compatible', 'Comment', prefixCls, iconPrefixCls]
  }, function () {
    var mergedToken = _objectSpread({
      componentCls: ".".concat(prefixCls)
    }, token);
    return [genSharedButtonStyle(mergedToken)];
  }), hashId];
}