import * as React from 'react';
import type { ColProps } from 'antd/lib/grid/col';
import type { ConfigConsumerProps } from '../CompatibleConsumer';
declare const ValidateStatuses: ["success", "warning", "error", "validating", ""];
declare const FormLabelAligns: ["left", "right"];
export type FormLabelAlign = typeof FormLabelAligns[number];
export interface FormItemProps {
    prefixCls?: string;
    className?: string;
    id?: string;
    htmlFor?: string;
    label?: React.ReactNode;
    name?: string;
    labelAlign?: FormLabelAlign;
    labelCol?: ColProps;
    wrapperCol?: ColProps;
    help?: React.ReactNode;
    extra?: React.ReactNode;
    validateStatus?: typeof ValidateStatuses[number];
    hasFeedback?: boolean;
    required?: boolean;
    style?: React.CSSProperties;
    colon?: boolean;
    children?: React.ReactNode;
}
export default class FormItem extends React.Component<FormItemProps, any> {
    helpShow: boolean;
    static defaultProps: {
        hasFeedback: boolean;
    };
    componentDidMount(): void;
    getHelpMessage(): React.ReactNode;
    getControls(children: React.ReactNode, recursively: boolean): React.ReactElement<any, string | React.JSXElementConstructor<any>>[];
    getOnlyControl(): React.ReactElement<any, string | React.JSXElementConstructor<any>>;
    getChildProp(prop: string): any;
    getId(): any;
    getMeta(): any;
    getField(): any;
    getValidateStatus(): "success" | "error" | "validating" | "";
    onLabelClick: () => void;
    isRequired(): any;
    renderHelp(prefixCls: string): React.JSX.Element;
    renderExtra(prefixCls: string): React.JSX.Element;
    renderValidateWrapper(prefixCls: string, c1: React.ReactNode, c2: React.ReactNode, c3: React.ReactNode): React.JSX.Element;
    renderWrapper(prefixCls: string, children: React.ReactNode): React.JSX.Element;
    renderLabel(prefixCls: string): React.JSX.Element;
    renderChildren(prefixCls: string): React.JSX.Element[];
    renderFormItem: ({ getPrefixCls }: ConfigConsumerProps) => React.JSX.Element;
    render(): React.JSX.Element;
}
export {};
