"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useStyle;
var React = _interopRequireWildcard(require("react"));
var _cssinjs = require("@ant-design/cssinjs");
var _antd = require("antd");
var _style = require("antd/lib/style");
var _mixin = require("./mixin");
var _layout = require("./layout");
var _feedback = require("./feedback");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); } // import '../../style/index.less';
// import './index.less';
// ============================== Export ==============================
var genFormStyle = function genFormStyle(token) {
  var antCls = token.antCls,
    iconCls = token.iconCls,
    componentCls = token.componentCls,
    controlHeightLG = token.controlHeightLG,
    controlHeight = token.controlHeight,
    colorTextHeading = token.colorTextHeading,
    colorHighlight = token.colorHighlight,
    colorTextSecondary = token.colorTextSecondary,
    fontSize = token.fontSize,
    lineHeight = token.lineHeight,
    marginXS = token.marginXS,
    marginXXS = token.marginXXS,
    marginLG = token.marginLG,
    motionEaseOut = token.motionEaseOut,
    motionDurationSlow = token.motionDurationSlow,
    paddingXXS = token.paddingXXS,
    paddingXS = token.paddingXS,
    formExplainPrecision = token.formExplainPrecision;
  var formExplainHeight = Math.floor(fontSize * lineHeight);
  var formHelpMarginTop = (controlHeight - controlHeightLG) / 2 + 2;
  return _defineProperty({}, componentCls, _objectSpread(_objectSpread(_objectSpread({}, (0, _style.resetComponent)(token)), (0, _mixin.resetForm)(token)), {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(componentCls, "-item-required::before"), {
    display: 'inline-block',
    marginRight: 4,
    color: colorHighlight,
    fontSize: fontSize,
    fontFamily: 'SimSun, sans-serif',
    lineHeight: 1,
    content: '" * "'
  }), "".concat(componentCls, "-hide-required-mark ").concat(componentCls, "-item-required::before"), {
    display: 'none'
  }), "".concat(componentCls, "-item-label > label"), {
    color: colorTextHeading,
    '&::after': {
      content: '":"'
    },
    position: 'relative',
    top: -0.5,
    margin: "0 ".concat(marginXS, "px 0 ").concat(marginXXS / 2, "px")
  }), "&".concat(componentCls, "-item-no-colon::after"), {
    content: '" "'
  }), "".concat(componentCls, "-item"), _objectSpread(_objectSpread({
    label: _defineProperty({
      position: 'relative'
    }, "> ".concat(iconCls), {
      fontSize: fontSize,
      verticalAlign: 'top'
    })
  }, (0, _style.resetComponent)(token)), {}, _defineProperty({
    marginBottom: marginLG,
    verticalAlign: 'top',
    '&-control': _objectSpread({
      position: 'relative',
      lineHeight: "".concat(controlHeightLG, "px")
    }, (0, _style.clearFix)()),
    '&-children': {
      position: 'relative'
    },
    '&-with-help': {
      marginBottom: Math.max(0, marginLG - formExplainHeight - formHelpMarginTop)
    },
    '&-label': {
      display: 'inline-block',
      overflow: 'hidden',
      lineHeight: "".concat(controlHeightLG - 0.0001, "px"),
      whiteSpace: 'nowrap',
      textAlign: 'right',
      verticalAlign: 'middle',
      flexGrow: '0',
      '&-left': {
        textAlign: 'left'
      }
    },
    '&-control-wrapper': {
      flex: '1 1 0'
    }
  }, "".concat(antCls, "-switch"), {
    margin: '2px 0 4px'
  }))), "".concat(componentCls, "-explain, ").concat(componentCls, "-extra"), {
    clear: 'both',
    minHeight: formExplainHeight + formExplainPrecision,
    marginTop: formHelpMarginTop,
    color: colorTextSecondary,
    fontSize: fontSize,
    lineHeight: lineHeight,
    transition: "color ".concat(motionDurationSlow, " ").concat(motionEaseOut)
  }), "".concat(componentCls, "-explain"), {
    marginBottom: -formExplainPrecision
  }), "".concat(componentCls, "-extra"), {
    paddingTop: paddingXXS
  }), "".concat(componentCls, "-text"), {
    display: 'inline-block',
    paddingRight: paddingXS
  }), "".concat(componentCls, "-split"), {
    display: 'block',
    textAlign: 'center'
  })));
};
function useStyle(prefixCls) {
  var _antdTheme$useToken = _antd.theme.useToken(),
    theme = _antdTheme$useToken.theme,
    token = _antdTheme$useToken.token,
    hashId = _antdTheme$useToken.hashId;
  var _React$useContext = React.useContext(_antd.ConfigProvider.ConfigContext),
    iconPrefixCls = _React$useContext.iconPrefixCls,
    getPrefixCls = _React$useContext.getPrefixCls;
  var rootPrefixCls = getPrefixCls();
  return [(0, _cssinjs.useStyleRegister)({
    theme: theme,
    token: token,
    hashId: hashId,
    path: ['compatible', 'Form', prefixCls, iconPrefixCls]
  }, function () {
    var mergedToken = _objectSpread({
      componentCls: ".".concat(prefixCls),
      antCls: ".".concat(rootPrefixCls),
      iconCls: ".".concat(iconPrefixCls),
      formExplainPrecision: 1
    }, token);
    return [genFormStyle(mergedToken), (0, _layout.genFormLayoutStyle)(mergedToken), (0, _feedback.genFeedbackStyle)(mergedToken)];
  }), hashId];
}