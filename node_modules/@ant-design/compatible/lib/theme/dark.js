"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getSolidColor = exports.getAlphaColor = exports.default = void 0;
var _colors = require("@ant-design/colors");
var _genColorMapToken = _interopRequireDefault(require("./genColorMapToken"));
var _antd = require("antd");
var _tinycolor = require("@ctrl/tinycolor");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var darkAlgorithm = _antd.theme.darkAlgorithm;
var getAlphaColor = exports.getAlphaColor = function getAlphaColor(baseColor, alpha) {
  return new _tinycolor.TinyColor(baseColor).setAlpha(alpha).toRgbString();
};
var getSolidColor = exports.getSolidColor = function getSolidColor(baseColor, brightness) {
  var instance = new _tinycolor.TinyColor(baseColor);
  return instance.lighten(brightness).toHexString();
};
var generateColorPalettes = function generateColorPalettes(baseColor) {
  var colors = (0, _colors.generate)(baseColor, {
    theme: 'dark'
  });
  return {
    1: colors[0],
    2: colors[1],
    3: colors[2],
    4: colors[3],
    5: colors[6],
    6: colors[5],
    7: colors[4],
    8: colors[6],
    9: colors[5],
    10: colors[4]
  };
};
var generateNeutralColorPalettes = function generateNeutralColorPalettes(bgBaseColor, textBaseColor) {
  var colorBgBase = bgBaseColor || '#000';
  var colorTextBase = textBaseColor || '#fff';
  return {
    colorBgBase: colorBgBase,
    colorTextBase: colorTextBase,
    colorBgBlur: 'transparent',
    colorText: getAlphaColor(colorTextBase, 0.85),
    colorTextSecondary: getAlphaColor(colorTextBase, 0.45),
    // Different from v5
    colorTextTertiary: getAlphaColor(colorTextBase, 0.45),
    colorTextQuaternary: getAlphaColor(colorTextBase, 0.25),
    colorFill: getAlphaColor(colorTextBase, 0.18),
    colorFillSecondary: getAlphaColor(colorTextBase, 0.12),
    colorFillTertiary: getAlphaColor(colorTextBase, 0.08),
    colorFillQuaternary: getAlphaColor(colorTextBase, 0.04),
    colorBgElevated: getSolidColor(colorBgBase, 12),
    colorBgContainer: getSolidColor(colorBgBase, 8),
    colorBgLayout: getSolidColor(colorBgBase, 0),
    colorBgSpotlight: getSolidColor(colorBgBase, 26),
    colorBorder: getSolidColor(colorBgBase, 26),
    colorBorderSecondary: getSolidColor(colorBgBase, 19),
    colorSplit: getAlphaColor(colorTextBase, 0.12),
    colorBgSolid: getAlphaColor(colorTextBase, 0.95),
    colorBgSolidHover: getAlphaColor(colorTextBase, 1),
    colorBgSolidActive: getAlphaColor(colorTextBase, 0.9)
  };
};
var derivative = function derivative(token, mapToken) {
  var mergedMapToken = mapToken !== null && mapToken !== void 0 ? mapToken : darkAlgorithm(token);
  return _objectSpread(_objectSpread({}, mergedMapToken), (0, _genColorMapToken.default)(mapToken !== null && mapToken !== void 0 ? mapToken : token, {
    generateColorPalettes: generateColorPalettes,
    generateNeutralColorPalettes: generateNeutralColorPalettes
  }));
};
var _default = exports.default = derivative;