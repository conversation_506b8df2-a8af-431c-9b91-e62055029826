import type { DerivativeFunc } from '@ant-design/cssinjs';
import type { GenerateColorMap } from 'antd/lib/theme/themes/ColorMap';
import type { MapToken, SeedToken } from 'antd/lib/theme/interface';
export declare const getAlphaColor: (baseColor: string, alpha: number) => string;
export declare const getSolidColor: (baseColor: string, brightness: number) => string;
export declare const generateColorPalettes: GenerateColorMap;
declare const derivative: DerivativeFunc<SeedToken, MapToken>;
export default derivative;
