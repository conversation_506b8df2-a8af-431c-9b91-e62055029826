"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "darkAlgorithm", {
  enumerable: true,
  get: function get() {
    return _dark.default;
  }
});
exports.darkTheme = void 0;
Object.defineProperty(exports, "defaultAlgorithm", {
  enumerable: true,
  get: function get() {
    return _default.default;
  }
});
exports.defaultTheme = void 0;
var _default = _interopRequireDefault(require("./default"));
var _dark = _interopRequireDefault(require("./dark"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var v4Token = {
  token: {
    borderRadius: 2,
    colorPrimary: '#1890ff',
    wireframe: true
  }
};
var defaultTheme = exports.defaultTheme = _objectSpread(_objectSpread({}, v4Token), {}, {
  algorithm: _default.default,
  components: {
    Menu: {
      itemBorderRadius: 0,
      subMenuItemBorderRadius: 0,
      itemHoverColor: '#1890ff',
      itemSelectedColor: '#1890ff',
      itemSelectedBg: '#e6f7ff',
      activeBarWidth: 3,
      itemMarginInline: 0,
      itemHoverBg: 'transparent'
    }
  }
});
var darkTheme = exports.darkTheme = _objectSpread(_objectSpread({}, v4Token), {}, {
  algorithm: _dark.default,
  components: {
    Menu: {
      itemBorderRadius: 0,
      subMenuItemBorderRadius: 0,
      itemHoverColor: 'transparent',
      itemSelectedColor: '#1890ff',
      itemSelectedBg: '#111b26',
      activeBarWidth: 3,
      itemMarginInline: 0,
      itemHoverBg: 'transparent'
    }
  }
});