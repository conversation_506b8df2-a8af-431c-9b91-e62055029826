import type { MapToken } from 'antd/lib/theme/interface';
export default function convertLegacyToken(mapToken: MapToken): Record<"theme" | "black" | "white" | "ease-in" | "ease-in-out" | "ease-out" | "ant-prefix" | "html-selector" | "primary-color" | "primary-color-hover" | "primary-color-active" | "primary-color-outline" | "processing-color" | "info-color" | "info-color-deprecated-bg" | "info-color-deprecated-border" | "success-color" | "success-color-hover" | "success-color-active" | "success-color-outline" | "success-color-deprecated-bg" | "success-color-deprecated-border" | "warning-color" | "warning-color-hover" | "warning-color-active" | "warning-color-outline" | "warning-color-deprecated-bg" | "warning-color-deprecated-border" | "error-color" | "error-color-hover" | "error-color-active" | "error-color-outline" | "error-color-deprecated-bg" | "error-color-deprecated-border" | "highlight-color" | "normal-color" | "primary-1" | "primary-2" | "primary-3" | "primary-4" | "primary-5" | "primary-6" | "primary-7" | "primary-8" | "primary-9" | "primary-10" | "body-background" | "component-background" | "popover-background" | "popover-customize-border-color" | "font-family" | "code-family" | "text-color" | "text-color-secondary" | "text-color-inverse" | "icon-color" | "icon-color-hover" | "heading-color" | "text-color-dark" | "text-color-secondary-dark" | "text-selection-bg" | "font-variant-base" | "font-feature-settings-base" | "font-size-base" | "font-size-lg" | "font-size-sm" | "heading-1-size" | "heading-2-size" | "heading-3-size" | "heading-4-size" | "heading-5-size" | "line-height-base" | "border-radius-base" | "border-radius-sm" | "control-border-radius" | "arrow-border-radius" | "padding-lg" | "padding-md" | "padding-sm" | "padding-xs" | "padding-xss" | "control-padding-horizontal" | "control-padding-horizontal-sm" | "margin-lg" | "margin-md" | "margin-sm" | "margin-xs" | "margin-xss" | "height-base" | "height-lg" | "height-sm" | "item-active-bg" | "item-hover-bg" | "iconfont-css-prefix" | "link-color" | "link-hover-color" | "link-active-color" | "link-decoration" | "link-hover-decoration" | "link-focus-decoration" | "link-focus-outline" | "ease-base-out" | "ease-base-in" | "ease-out-back" | "ease-in-back" | "ease-in-out-back" | "ease-out-circ" | "ease-in-circ" | "ease-in-out-circ" | "ease-out-quint" | "ease-in-quint" | "ease-in-out-quint" | "border-color-base" | "border-color-split" | "border-color-inverse" | "border-width-base" | "border-style-base" | "outline-blur-size" | "outline-width" | "outline-color" | "outline-fade" | "background-color-light" | "background-color-base" | "disabled-color" | "disabled-bg" | "disabled-active-bg" | "disabled-color-dark" | "shadow-color" | "shadow-color-inverse" | "box-shadow-base" | "shadow-1-up" | "shadow-1-down" | "shadow-1-left" | "shadow-1-right" | "shadow-2" | "btn-font-weight" | "btn-border-radius-base" | "btn-border-radius-sm" | "btn-border-width" | "btn-border-style" | "btn-shadow" | "btn-primary-shadow" | "btn-text-shadow" | "btn-primary-color" | "btn-primary-bg" | "btn-default-color" | "btn-default-bg" | "btn-default-border" | "btn-danger-color" | "btn-danger-bg" | "btn-danger-border" | "btn-disable-color" | "btn-disable-bg" | "btn-disable-border" | "btn-default-ghost-color" | "btn-default-ghost-bg" | "btn-default-ghost-border" | "btn-font-size-lg" | "btn-font-size-sm" | "btn-padding-horizontal-base" | "btn-padding-horizontal-lg" | "btn-padding-horizontal-sm" | "btn-height-base" | "btn-height-lg" | "btn-height-sm" | "btn-line-height" | "btn-circle-size" | "btn-circle-size-lg" | "btn-circle-size-sm" | "btn-square-size" | "btn-square-size-lg" | "btn-square-size-sm" | "btn-square-only-icon-size" | "btn-square-only-icon-size-sm" | "btn-square-only-icon-size-lg" | "btn-group-border" | "btn-link-hover-bg" | "btn-text-hover-bg" | "checkbox-size" | "checkbox-color" | "checkbox-check-color" | "checkbox-check-bg" | "checkbox-border-width" | "checkbox-border-radius" | "checkbox-group-item-margin-right" | "descriptions-bg" | "descriptions-title-margin-bottom" | "descriptions-default-padding" | "descriptions-middle-padding" | "descriptions-small-padding" | "descriptions-item-padding-bottom" | "descriptions-item-trailing-colon" | "descriptions-item-label-colon-margin-right" | "descriptions-item-label-colon-margin-left" | "descriptions-extra-color" | "divider-text-padding" | "divider-orientation-margin" | "divider-color" | "divider-vertical-gutter" | "dropdown-selected-color" | "dropdown-menu-submenu-disabled-bg" | "dropdown-selected-bg" | "empty-font-size" | "radio-size" | "radio-top" | "radio-border-width" | "radio-dot-size" | "radio-dot-color" | "radio-dot-disabled-color" | "radio-solid-checked-color" | "radio-button-bg" | "radio-button-checked-bg" | "radio-button-color" | "radio-button-hover-color" | "radio-button-active-color" | "radio-button-padding-horizontal" | "radio-disabled-button-checked-bg" | "radio-disabled-button-checked-color" | "radio-wrapper-margin-right" | "screen-xs" | "screen-xs-min" | "screen-sm" | "screen-sm-min" | "screen-md" | "screen-md-min" | "screen-lg" | "screen-lg-min" | "screen-xl" | "screen-xl-min" | "screen-xxl" | "screen-xxl-min" | "screen-xs-max" | "screen-sm-max" | "screen-md-max" | "screen-lg-max" | "screen-xl-max" | "grid-columns" | "layout-body-background" | "layout-header-background" | "layout-header-height" | "layout-header-padding" | "layout-header-color" | "layout-footer-padding" | "layout-footer-background" | "layout-sider-background" | "layout-trigger-height" | "layout-trigger-background" | "layout-trigger-color" | "layout-zero-trigger-width" | "layout-zero-trigger-height" | "layout-sider-background-light" | "layout-trigger-background-light" | "layout-trigger-color-light" | "zindex-badge" | "zindex-table-fixed" | "zindex-affix" | "zindex-back-top" | "zindex-picker-panel" | "zindex-popup-close" | "zindex-modal" | "zindex-modal-mask" | "zindex-message" | "zindex-notification" | "zindex-popover" | "zindex-dropdown" | "zindex-picker" | "zindex-popoconfirm" | "zindex-tooltip" | "zindex-image" | "animation-duration-slow" | "animation-duration-base" | "animation-duration-fast" | "collapse-panel-border-radius" | "dropdown-menu-bg" | "dropdown-vertical-padding" | "dropdown-edge-child-vertical-padding" | "dropdown-font-size" | "dropdown-line-height" | "label-required-color" | "label-color" | "form-warning-input-bg" | "form-item-margin-bottom" | "form-item-trailing-colon" | "form-vertical-label-padding" | "form-vertical-label-margin" | "form-item-label-font-size" | "form-item-label-height" | "form-item-label-colon-margin-right" | "form-item-label-colon-margin-left" | "form-error-input-bg" | "input-height-base" | "input-height-lg" | "input-height-sm" | "input-padding-horizontal" | "input-padding-horizontal-base" | "input-padding-horizontal-sm" | "input-padding-horizontal-lg" | "input-padding-vertical-base" | "input-padding-vertical-sm" | "input-padding-vertical-lg" | "input-placeholder-color" | "input-color" | "input-icon-color" | "input-border-color" | "input-bg" | "input-number-hover-border-color" | "input-number-handler-active-bg" | "input-number-handler-hover-bg" | "input-number-handler-bg" | "input-number-handler-border-color" | "input-addon-bg" | "input-hover-border-color" | "input-disabled-bg" | "input-outline-offset" | "input-icon-hover-color" | "input-disabled-color" | "mentions-dropdown-bg" | "mentions-dropdown-menu-item-hover-bg" | "select-border-color" | "select-item-selected-color" | "select-item-selected-font-weight" | "select-dropdown-bg" | "select-item-selected-bg" | "select-item-active-bg" | "select-dropdown-vertical-padding" | "select-dropdown-font-size" | "select-dropdown-line-height" | "select-dropdown-height" | "select-background" | "select-clear-background" | "select-selection-item-bg" | "select-selection-item-border-color" | "select-single-item-height-lg" | "select-multiple-item-height" | "select-multiple-item-height-lg" | "select-multiple-item-spacing-half" | "select-multiple-disabled-background" | "select-multiple-item-disabled-color" | "select-multiple-item-disabled-border-color" | "cascader-bg" | "cascader-item-selected-bg" | "cascader-menu-bg" | "cascader-menu-border-color-split" | "cascader-dropdown-vertical-padding" | "cascader-dropdown-edge-child-vertical-padding" | "cascader-dropdown-font-size" | "cascader-dropdown-line-height" | "anchor-bg" | "anchor-border-color" | "anchor-link-top" | "anchor-link-left" | "anchor-link-padding" | "tooltip-max-width" | "tooltip-color" | "tooltip-bg" | "tooltip-arrow-width" | "tooltip-distance" | "tooltip-arrow-color" | "tooltip-border-radius" | "popover-bg" | "popover-color" | "popover-min-width" | "popover-min-height" | "popover-arrow-width" | "popover-arrow-color" | "popover-arrow-outer-color" | "popover-distance" | "popover-padding-horizontal" | "modal-header-padding-vertical" | "modal-header-padding-horizontal" | "modal-body-padding" | "modal-header-bg" | "modal-header-padding" | "modal-header-border-width" | "modal-header-border-style" | "modal-header-title-line-height" | "modal-header-title-font-size" | "modal-header-border-color-split" | "modal-header-close-size" | "modal-content-bg" | "modal-heading-color" | "modal-close-color" | "modal-footer-bg" | "modal-footer-border-color-split" | "modal-footer-border-style" | "modal-footer-padding-vertical" | "modal-footer-padding-horizontal" | "modal-footer-border-width" | "modal-mask-bg" | "modal-confirm-body-padding" | "modal-confirm-title-font-size" | "modal-border-radius" | "progress-default-color" | "progress-remaining-color" | "progress-info-text-color" | "progress-radius" | "progress-steps-item-bg" | "progress-text-font-size" | "progress-text-color" | "progress-circle-text-font-size" | "menu-inline-toplevel-item-height" | "menu-item-height" | "menu-item-group-height" | "menu-collapsed-width" | "menu-bg" | "menu-popup-bg" | "menu-item-color" | "menu-inline-submenu-bg" | "menu-highlight-color" | "menu-highlight-danger-color" | "menu-item-active-bg" | "menu-item-active-danger-bg" | "menu-item-active-border-width" | "menu-item-group-title-color" | "menu-item-vertical-margin" | "menu-item-font-size" | "menu-item-boundary-margin" | "menu-item-padding-horizontal" | "menu-item-padding" | "menu-horizontal-line-height" | "menu-icon-margin-right" | "menu-icon-size" | "menu-icon-size-lg" | "menu-item-group-title-font-size" | "menu-dark-color" | "menu-dark-danger-color" | "menu-dark-bg" | "menu-dark-arrow-color" | "menu-dark-inline-submenu-bg" | "menu-dark-highlight-color" | "menu-dark-item-active-bg" | "menu-dark-item-active-danger-bg" | "menu-dark-selected-item-icon-color" | "menu-dark-selected-item-text-color" | "menu-dark-item-hover-bg" | "spin-dot-size-sm" | "spin-dot-size" | "spin-dot-size-lg" | "table-bg" | "table-header-bg" | "table-header-color" | "table-header-sort-bg" | "table-body-sort-bg" | "table-row-hover-bg" | "table-selected-row-color" | "table-selected-row-bg" | "table-body-selected-sort-bg" | "table-selected-row-hover-bg" | "table-expanded-row-bg" | "table-padding-vertical" | "table-padding-horizontal" | "table-padding-vertical-md" | "table-padding-horizontal-md" | "table-padding-vertical-sm" | "table-padding-horizontal-sm" | "table-border-color" | "table-border-radius-base" | "table-footer-bg" | "table-footer-color" | "table-header-bg-sm" | "table-font-size" | "table-font-size-md" | "table-font-size-sm" | "table-header-cell-split-color" | "table-header-sort-active-bg" | "table-fixed-header-sort-active-bg" | "table-header-filter-active-bg" | "table-filter-btns-bg" | "table-filter-dropdown-bg" | "table-expand-icon-bg" | "table-selection-column-width" | "table-sticky-scroll-bar-bg" | "table-sticky-scroll-bar-radius" | "tag-border-radius" | "tag-default-bg" | "tag-default-color" | "tag-font-size" | "tag-line-height" | "picker-bg" | "picker-basic-cell-hover-color" | "picker-basic-cell-active-with-range-color" | "picker-basic-cell-hover-with-range-color" | "picker-basic-cell-disabled-bg" | "picker-border-color" | "picker-date-hover-range-border-color" | "picker-date-hover-range-color" | "picker-time-panel-column-width" | "picker-time-panel-column-height" | "picker-time-panel-cell-height" | "picker-panel-cell-height" | "picker-panel-cell-width" | "picker-text-height" | "picker-panel-without-time-cell-height" | "calendar-bg" | "calendar-input-bg" | "calendar-border-color" | "calendar-item-active-bg" | "calendar-column-active-bg" | "calendar-full-bg" | "calendar-full-panel-bg" | "carousel-dot-width" | "carousel-dot-height" | "carousel-dot-active-width" | "badge-height" | "badge-height-sm" | "badge-dot-size" | "badge-font-size" | "badge-font-size-sm" | "badge-font-weight" | "badge-status-size" | "badge-text-color" | "badge-color" | "rate-star-color" | "rate-star-bg" | "rate-star-size" | "rate-star-hover-scale" | "card-head-color" | "card-head-background" | "card-head-font-size" | "card-head-font-size-sm" | "card-head-padding" | "card-head-padding-sm" | "card-head-height" | "card-head-height-sm" | "card-inner-head-padding" | "card-padding-base" | "card-padding-base-sm" | "card-actions-background" | "card-actions-li-margin" | "card-skeleton-bg" | "card-background" | "card-shadow" | "card-radius" | "card-head-tabs-margin-bottom" | "card-head-extra-color" | "comment-bg" | "comment-padding-base" | "comment-nest-indent" | "comment-font-size-base" | "comment-font-size-sm" | "comment-author-name-color" | "comment-author-time-color" | "comment-action-color" | "comment-action-hover-color" | "comment-actions-margin-bottom" | "comment-actions-margin-top" | "comment-content-detail-p-margin-bottom" | "tabs-card-head-background" | "tabs-card-height" | "tabs-card-active-color" | "tabs-card-horizontal-padding" | "tabs-card-horizontal-padding-sm" | "tabs-card-horizontal-padding-lg" | "tabs-title-font-size" | "tabs-title-font-size-lg" | "tabs-title-font-size-sm" | "tabs-ink-bar-color" | "tabs-bar-margin" | "tabs-horizontal-gutter" | "tabs-horizontal-margin" | "tabs-horizontal-margin-rtl" | "tabs-horizontal-padding" | "tabs-horizontal-padding-lg" | "tabs-horizontal-padding-sm" | "tabs-vertical-padding" | "tabs-vertical-margin" | "tabs-scrolling-size" | "tabs-highlight-color" | "tabs-hover-color" | "tabs-active-color" | "tabs-card-gutter" | "tabs-card-tab-active-border-top" | "back-top-color" | "back-top-bg" | "back-top-hover-bg" | "avatar-size-base" | "avatar-size-lg" | "avatar-size-sm" | "avatar-font-size-base" | "avatar-font-size-lg" | "avatar-font-size-sm" | "avatar-bg" | "avatar-color" | "avatar-border-radius" | "avatar-group-overlapping" | "avatar-group-space" | "avatar-group-border-color" | "switch-height" | "switch-sm-height" | "switch-min-width" | "switch-sm-min-width" | "switch-disabled-opacity" | "switch-color" | "switch-bg" | "switch-shadow-color" | "switch-padding" | "switch-inner-margin-min" | "switch-inner-margin-max" | "switch-sm-inner-margin-min" | "switch-sm-inner-margin-max" | "pagination-item-bg" | "pagination-item-size" | "pagination-item-size-sm" | "pagination-font-family" | "pagination-font-weight-active" | "pagination-item-bg-active" | "pagination-item-link-bg" | "pagination-item-disabled-color-active" | "pagination-item-disabled-bg-active" | "pagination-item-input-bg" | "pagination-mini-options-size-changer-top" | "page-header-padding" | "page-header-padding-vertical" | "page-header-padding-breadcrumb" | "page-header-content-padding-vertical" | "page-header-back-color" | "page-header-ghost-bg" | "page-header-heading-title" | "page-header-heading-sub-title" | "page-header-tabs-tab-font-size" | "breadcrumb-base-color" | "breadcrumb-last-item-color" | "breadcrumb-font-size" | "breadcrumb-icon-font-size" | "breadcrumb-link-color" | "breadcrumb-link-color-hover" | "breadcrumb-separator-color" | "breadcrumb-separator-margin" | "slider-margin" | "slider-rail-background-color" | "slider-rail-background-color-hover" | "slider-track-background-color" | "slider-track-background-color-hover" | "slider-handle-border-width" | "slider-handle-background-color" | "slider-handle-color" | "slider-handle-color-hover" | "slider-handle-color-focus" | "slider-handle-color-focus-shadow" | "slider-handle-color-tooltip-open" | "slider-handle-size" | "slider-handle-margin-top" | "slider-handle-margin-left" | "slider-handle-shadow" | "slider-dot-border-color" | "slider-dot-border-color-active" | "slider-disabled-color" | "slider-disabled-background-color" | "tree-bg" | "tree-title-height" | "tree-child-padding" | "tree-directory-selected-color" | "tree-directory-selected-bg" | "tree-node-hover-bg" | "tree-node-selected-bg" | "collapse-header-padding" | "collapse-header-padding-extra" | "collapse-header-bg" | "collapse-content-padding" | "collapse-content-bg" | "collapse-header-arrow-left" | "skeleton-color" | "skeleton-to-color" | "skeleton-paragraph-margin-top" | "skeleton-paragraph-li-margin-top" | "skeleton-paragraph-li-height" | "skeleton-title-height" | "skeleton-title-paragraph-margin-top" | "transfer-header-height" | "transfer-item-height" | "transfer-disabled-bg" | "transfer-list-height" | "transfer-item-hover-bg" | "transfer-item-selected-hover-bg" | "transfer-item-padding-vertical" | "transfer-list-search-icon-top" | "message-notice-content-padding" | "message-notice-content-bg" | "wave-animation-width" | "alert-success-border-color" | "alert-success-bg-color" | "alert-success-icon-color" | "alert-info-border-color" | "alert-info-bg-color" | "alert-info-icon-color" | "alert-warning-border-color" | "alert-warning-bg-color" | "alert-warning-icon-color" | "alert-error-border-color" | "alert-error-bg-color" | "alert-error-icon-color" | "alert-message-color" | "alert-text-color" | "alert-close-color" | "alert-close-hover-color" | "alert-padding-vertical" | "alert-padding-horizontal" | "alert-no-icon-padding-vertical" | "alert-with-description-no-icon-padding-vertical" | "alert-with-description-padding-vertical" | "alert-with-description-padding" | "alert-icon-top" | "alert-with-description-icon-size" | "list-header-background" | "list-footer-background" | "list-empty-text-padding" | "list-item-padding" | "list-item-padding-sm" | "list-item-padding-lg" | "list-item-meta-margin-bottom" | "list-item-meta-avatar-margin-right" | "list-item-meta-title-margin-bottom" | "list-customize-card-bg" | "list-item-meta-description-font-size" | "statistic-title-font-size" | "statistic-content-font-size" | "statistic-unit-font-size" | "statistic-font-family" | "drawer-header-padding" | "drawer-body-padding" | "drawer-bg" | "drawer-footer-padding-vertical" | "drawer-footer-padding-horizontal" | "drawer-header-close-size" | "drawer-title-font-size" | "drawer-title-line-height" | "timeline-width" | "timeline-color" | "timeline-dot-border-width" | "timeline-dot-color" | "timeline-dot-bg" | "timeline-item-padding-bottom" | "typography-title-font-weight" | "typography-title-margin-top" | "typography-title-margin-bottom" | "upload-actions-color" | "process-tail-color" | "steps-nav-arrow-color" | "steps-background" | "steps-icon-size" | "steps-icon-custom-size" | "steps-icon-custom-top" | "steps-icon-custom-font-size" | "steps-icon-top" | "steps-icon-font-size" | "steps-icon-margin" | "steps-title-line-height" | "steps-small-icon-size" | "steps-small-icon-margin" | "steps-dot-size" | "steps-dot-top" | "steps-current-dot-size" | "steps-description-max-width" | "steps-nav-content-max-width" | "steps-vertical-icon-width" | "steps-vertical-tail-width" | "steps-vertical-tail-width-sm" | "notification-bg" | "notification-padding-vertical" | "notification-padding-horizontal" | "result-title-font-size" | "result-subtitle-font-size" | "result-icon-font-size" | "result-extra-margin" | "image-size-base" | "image-font-size-base" | "image-bg" | "image-color" | "image-mask-font-size" | "image-preview-operation-size" | "image-preview-operation-color" | "image-preview-operation-disabled-color" | "segmented-bg" | "segmented-hover-bg" | "segmented-selected-bg" | "segmented-label-color" | "segmented-label-hover-color", string>;
