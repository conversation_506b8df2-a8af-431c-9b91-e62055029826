function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
export var resetForm = function resetForm(token) {
  var colorText = token.colorText,
    colorTextSecondary = token.colorTextSecondary,
    fontSizeLG = token.fontSizeLG,
    lineHeight = token.lineHeight,
    lineWidth = token.lineWidth,
    lineType = token.lineType,
    colorBorder = token.colorBorder,
    fontSize = token.fontSize;
  return _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({
    // Based on Bootstrap framework
    legend: {
      display: 'block',
      width: '100%',
      marginBottom: '20px',
      padding: '0',
      color: colorTextSecondary,
      fontSize: fontSizeLG,
      lineHeight: 'inherit',
      border: '0',
      borderBottom: "".concat(lineWidth, "px ").concat(lineType, " ").concat(colorBorder)
    },
    label: {
      fontSize: fontSize
    }
  }, "input[type='search']", {
    boxSizing: 'border-box'
  }), "input[type='radio'], input[type='checkbox']", {
    lineHeight: 'normal'
  }), "input[type='file']", {
    display: 'block'
  }), "input[type='range']", {
    display: 'block',
    width: '100%'
  }), 'select[multiple], select[size]', {
    height: 'auto'
  }), "input[type='file']:focus, input[type='radio']:focus, input[type='checkbox']:focus", [{
    outline: 'thin dotted'
  }, {
    outline: '5px auto -webkit-focus-ring-color',
    outlineOffset: '-2px'
  }]), "output", {
    display: 'block',
    paddingTop: '15px',
    color: colorText,
    fontSize: fontSize,
    lineHeight: lineHeight
  });
};
export var genFormControlValidation = function genFormControlValidation(componentCls, colorText) {
  return _defineProperty({}, "".concat(componentCls, "-explain, ").concat(componentCls, "-split"), {
    color: colorText
  });
};