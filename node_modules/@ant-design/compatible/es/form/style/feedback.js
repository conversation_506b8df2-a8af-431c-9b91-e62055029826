function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
import { genFormControlValidation } from "./mixin";

// export const showHelpMotion = (
//   className: string,
//   inKeyframes: Keyframes,
//   outKeyframes: Keyframes,
//   duration: string,
// ): CSSObject => {
//   return {
//     //   .make-motion(@className, @keyframeName, @duration);
//     ...(initMotion(className, inKeyframes, outKeyframes, duration) as any),

//     [`.${className}-enter, .${className}-appear`]: {
//       opacity: 0,
//       // animation-timing-function: @ease-in-out;
//     },
//     [`.${className}-leave`]: {
//       // animation-timing-function: @ease-in-out;
//     },
//   };
// };

// export const helpIn = new Keyframes('legacyAntShowHelpIn', {
//   '0%': {
//     transform: 'translateY(-5px)',
//     opacity: '0',
//   },
//   '100%': {
//     transform: 'translateY(0)',
//     opacity: '1',
//   },
// });

// export const helpOut = new Keyframes('legacyAntShowHelpOut', {
//   to: {
//     transform: 'translateY(-5px)',
//     opacity: '0',
//   },
// });

export var genFeedbackStyle = function genFeedbackStyle(token) {
  var componentCls = token.componentCls,
    colorSuccess = token.colorSuccess,
    colorInfo = token.colorInfo,
    colorWarning = token.colorWarning,
    colorError = token.colorError;
  return _defineProperty({}, componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(componentCls, "-item-feedback-icon-success"), {
    color: colorSuccess
  }), "".concat(componentCls, "-item-feedback-icon-validating"), {
    color: colorInfo
  }), "".concat(componentCls, "-item-feedback-icon-warning"), {
    color: colorWarning
  }), "".concat(componentCls, "-item-feedback-icon-error"), {
    color: colorError
  }), '.has-warning', _objectSpread({}, genFormControlValidation(componentCls, colorWarning))), '.has-error', _objectSpread({}, genFormControlValidation(componentCls, colorError))));
};