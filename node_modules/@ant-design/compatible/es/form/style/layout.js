function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : String(i); }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var genVerticalLayoutLabel = function genVerticalLayoutLabel(token) {
  var lineHeight = token.lineHeight;
  return {
    display: 'block',
    margin: 0,
    padding: '0 0 8px',
    lineHeight: lineHeight,
    whiteSpace: 'initial',
    textAlign: 'left',
    flexBasis: '100%',
    'label::after': {
      display: 'none'
    }
  };
};
var genVerticalLayoutControlWrapper = function genVerticalLayoutControlWrapper() {
  return {
    flexBasis: '100%'
  };
};
var genVerticalLayout = function genVerticalLayout(token) {
  var componentCls = token.componentCls;
  return _defineProperty(_defineProperty(_defineProperty({}, "".concat(componentCls, "-item-label, ").concat(componentCls, "-item-control-wrapper"), {
    display: 'block',
    width: '100%'
  }), "".concat(componentCls, "-item-label"), _objectSpread({}, genVerticalLayoutLabel(token))), "".concat(componentCls, "-item-control-wrapper"), _objectSpread({}, genVerticalLayoutControlWrapper()));
};
export var genFormLayoutStyle = function genFormLayoutStyle(token) {
  var antCls = token.antCls,
    componentCls = token.componentCls,
    lineHeight = token.lineHeight,
    formExplainPrecision = token.formExplainPrecision,
    marginLG = token.marginLG;
  return [// =============================== Vertical ===============================
  _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, componentCls, _defineProperty(_defineProperty({}, "\n        &".concat(componentCls, "-vertical ").concat(componentCls, "-item-label,\n        ").concat(antCls, "-col-24").concat(componentCls, "-item-label,\n        ").concat(antCls, "-col-xl-24").concat(componentCls, "-item-label\n      "), _objectSpread({}, genVerticalLayoutLabel(token))), "".concat(componentCls, "-vertical"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, "".concat(componentCls, "-item"), {
    paddingBottom: '8px'
  }), "".concat(componentCls, "-item-control"), {
    lineHeight: lineHeight
  }), "".concat(componentCls, "-explain"), {
    marginTop: 2,
    marginBottom: "-4px -".concat(formExplainPrecision, "px")
  }), "".concat(componentCls, "-extra"), {
    marginTop: 2,
    marginBottom: -4
  }))), "@media (max-width: ".concat(token.screenXSMax, ")"), _objectSpread(_objectSpread({}, genVerticalLayout(token)), {}, _defineProperty({}, "".concat(antCls, "-col-xs-24").concat(componentCls, "-item-label"), _objectSpread({}, genVerticalLayoutLabel(token))))), "@media (max-width: ".concat(token.screenSMMax, ")"), _defineProperty({}, "".concat(antCls, "-col-sm-24").concat(componentCls, "-item-label"), _objectSpread({}, genVerticalLayoutLabel(token)))), "@media (max-width: ".concat(token.screenMDMax, ")"), _defineProperty({}, "".concat(antCls, "-col-md-24").concat(componentCls, "-item-label"), _objectSpread({}, genVerticalLayoutLabel(token)))), "@media (max-width: ".concat(token.screenLGMax, ")"), _defineProperty({}, "".concat(antCls, "-col-lg-24").concat(componentCls, "-item-label"), _objectSpread({}, genVerticalLayoutLabel(token)))), "@media (max-width: ".concat(token.screenXLMax, ")"), _defineProperty({}, "".concat(antCls, "-col-xl-24").concat(componentCls, "-item-label"), _objectSpread({}, genVerticalLayoutLabel(token)))), // ================================ Inline ================================
  _defineProperty({}, "".concat(componentCls, "-inline"), _defineProperty(_defineProperty(_defineProperty({}, "".concat(componentCls, "-item"), _defineProperty({
    display: 'inline-block',
    marginRight: '16px',
    marginBottom: '0',
    '&-with-help': {
      marginBottom: marginLG
    }
  }, "> ".concat(componentCls, "-item-control-wrapper, > ").concat(componentCls, "-item-label"), {
    display: 'inline-block',
    verticalAlign: 'top'
  })), "".concat(componentCls, "-text"), {
    display: 'inline-block'
  }), '.has-feedback', {
    display: 'inline-block'
  }))];
};