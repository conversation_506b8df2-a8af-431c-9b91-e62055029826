import type { GenerateColorMap, GenerateNeutralColorMap } from 'antd/lib/theme/themes/ColorMap';
import type { ColorMapToken, SeedToken } from 'antd/lib/theme/interface';
interface PaletteGenerators {
    generateColorPalettes: GenerateColorMap;
    generateNeutralColorPalettes: GenerateNeutralColorMap;
}
export default function genColorMapToken(seed: SeedToken, { generateColorPalettes, generateNeutralColorPalettes }: PaletteGenerators): ColorMapToken;
export {};
