"use strict";
// This icon file is generated automatically.
Object.defineProperty(exports, "__esModule", { value: true });
var SecurityScanTwoTone = { "icon": function render(primaryColor, secondaryColor) { return { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M866.9 169.9L527.1 54.1C523 52.7 517.5 52 512 52s-11 .7-15.1 2.1L157.1 169.9c-8.3 2.8-15.1 12.4-15.1 21.2v482.4c0 8.8 5.7 20.4 12.6 25.9L499.3 968c3.5 2.7 8 4.1 12.6 4.1s9.2-1.4 12.6-4.1l344.7-268.6c6.9-5.4 12.6-17 12.6-25.9V191.1c.2-8.8-6.6-18.3-14.9-21.2zM810 654.3L512 886.5 214 654.3V226.7l298-101.6 298 101.6v427.6z", "fill": primaryColor } }, { "tag": "path", "attrs": { "d": "M460.7 451.1a80.1 80.1 0 10160.2 0 80.1 80.1 0 10-160.2 0z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M214 226.7v427.6l298 232.2 298-232.2V226.7L512 125.1 214 226.7zm428.7 122.5c56.3 56.3 56.3 147.5 0 203.8-48.5 48.5-123 55.2-178.6 20.1l-77.5 77.5a8.03 8.03 0 01-11.3 0l-34-34a8.03 8.03 0 010-11.3l77.5-77.5c-35.1-55.7-28.4-130.1 20.1-178.6 56.3-56.3 147.5-56.3 203.8 0z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M418.8 527.8l-77.5 77.5a8.03 8.03 0 000 11.3l34 34c3.1 3.1 8.2 3.1 11.3 0l77.5-77.5c55.6 35.1 130.1 28.4 178.6-20.1 56.3-56.3 56.3-147.5 0-203.8-56.3-56.3-147.5-56.3-203.8 0-48.5 48.5-55.2 122.9-20.1 178.6zm65.4-133.3a80.1 80.1 0 01113.3 0 80.1 80.1 0 010 113.3c-31.3 31.3-82 31.3-113.3 0s-31.3-82 0-113.3z", "fill": primaryColor } }] }; }, "name": "security-scan", "theme": "twotone" };
exports.default = SecurityScanTwoTone;
