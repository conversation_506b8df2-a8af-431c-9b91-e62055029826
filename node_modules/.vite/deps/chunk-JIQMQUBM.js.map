{"version": 3, "sources": ["../../antd/es/skeleton/Title.js"], "sourcesContent": ["\"use client\";\n\n/* eslint-disable jsx-a11y/heading-has-content */\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst Title = ({\n  prefixCls,\n  className,\n  width,\n  style\n}) => (\n/*#__PURE__*/\n// biome-ignore lint/a11y/useHeadingContent: HOC here\nReact.createElement(\"h3\", {\n  className: classNames(prefixCls, className),\n  style: Object.assign({\n    width\n  }, style)\n}));\nexport default Title;"], "mappings": ";;;;;;;;;;;AAGA,YAAuB;AACvB,wBAAuB;AACvB,IAAM,QAAQ,CAAC;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAAA;AAAA,EAGM,oBAAc,MAAM;AAAA,IACxB,eAAW,kBAAAA,SAAW,WAAW,SAAS;AAAA,IAC1C,OAAO,OAAO,OAAO;AAAA,MACnB;AAAA,IACF,GAAG,KAAK;AAAA,EACV,CAAC;AAAA;AACD,IAAO,gBAAQ;", "names": ["classNames"]}