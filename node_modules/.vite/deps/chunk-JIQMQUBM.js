import {
  require_classnames
} from "./chunk-RVYA5DUN.js";
import {
  require_react
} from "./chunk-NKBGLYTV.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/antd/es/skeleton/Title.js
var React = __toESM(require_react());
var import_classnames = __toESM(require_classnames());
var Title = ({
  prefixCls,
  className,
  width,
  style
}) => (
  // biome-ignore lint/a11y/useHeadingContent: HOC here
  React.createElement("h3", {
    className: (0, import_classnames.default)(prefixCls, className),
    style: Object.assign({
      width
    }, style)
  })
);
var Title_default = Title;

export {
  Title_default
};
//# sourceMappingURL=chunk-JIQMQUBM.js.map
