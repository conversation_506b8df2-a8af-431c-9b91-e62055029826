{"version": 3, "sources": ["../../hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js", "../../tslib/tslib.es6.mjs", "../../react-intl/lib/src/components/createFormattedComponent.js", "../../react-intl/lib/src/components/useIntl.js", "../../react-intl/lib/src/utils.js", "../../@formatjs/ecma402-abstract/lib/262.js", "../../@formatjs/ecma402-abstract/lib/IsSanctionedSimpleUnitIdentifier.js", "../../@formatjs/fast-memoize/index.ts", "../../@formatjs/ecma402-abstract/lib/utils.js", "../../@formatjs/ecma402-abstract/lib/regex.generated.js", "../../@formatjs/ecma402-abstract/lib/NumberFormat/format_to_parts.js", "../../@formatjs/ecma402-abstract/lib/data.js", "../../@formatjs/ecma402-abstract/lib/types/date-time.js", "../../@formatjs/icu-messageformat-parser/lib/error.js", "../../@formatjs/icu-messageformat-parser/lib/types.js", "../../@formatjs/icu-messageformat-parser/lib/regex.generated.js", "../../@formatjs/icu-skeleton-parser/lib/date-time.js", "../../@formatjs/icu-skeleton-parser/lib/regex.generated.js", "../../@formatjs/icu-skeleton-parser/lib/number.js", "../../@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "../../@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "../../@formatjs/icu-messageformat-parser/lib/parser.js", "../../@formatjs/icu-messageformat-parser/lib/index.js", "../../intl-messageformat/lib/src/error.js", "../../intl-messageformat/lib/src/formatters.js", "../../intl-messageformat/lib/src/core.js", "../../@formatjs/intl/lib/src/error.js", "../../@formatjs/intl/lib/src/utils.js", "../../@formatjs/intl/lib/src/message.js", "../../@formatjs/intl/lib/src/dateTime.js", "../../@formatjs/intl/lib/src/displayName.js", "../../@formatjs/intl/lib/src/list.js", "../../@formatjs/intl/lib/src/plural.js", "../../@formatjs/intl/lib/src/relativeTime.js", "../../@formatjs/intl/lib/src/number.js", "../../@formatjs/intl/lib/src/create-intl.js", "../../react-intl/lib/src/components/injectIntl.js", "../../react-intl/lib/src/components/provider.js", "../../react-intl/lib/src/components/createIntl.js", "../../react-intl/lib/src/components/relative.js", "../../react-intl/lib/src/components/plural.js", "../../react-intl/lib/src/components/message.js", "../../react-intl/lib/src/components/dateTimeRange.js", "../../react-intl/lib/index.js"], "sourcesContent": ["'use strict';\n\nvar reactIs = require('react-is');\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n\n    var keys = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n\n  return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import { __rest } from \"tslib\";\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar DisplayName;\n(function (DisplayName) {\n    DisplayName[\"formatDate\"] = \"FormattedDate\";\n    DisplayName[\"formatTime\"] = \"FormattedTime\";\n    DisplayName[\"formatNumber\"] = \"FormattedNumber\";\n    DisplayName[\"formatList\"] = \"FormattedList\";\n    // Note that this DisplayName is the locale display name, not to be confused with\n    // the name of the enum, which is for React component display name in dev tools.\n    DisplayName[\"formatDisplayName\"] = \"FormattedDisplayName\";\n})(DisplayName || (DisplayName = {}));\nvar DisplayNameParts;\n(function (DisplayNameParts) {\n    DisplayNameParts[\"formatDate\"] = \"FormattedDateParts\";\n    DisplayNameParts[\"formatTime\"] = \"FormattedTimeParts\";\n    DisplayNameParts[\"formatNumber\"] = \"FormattedNumberParts\";\n    DisplayNameParts[\"formatList\"] = \"FormattedListParts\";\n})(DisplayNameParts || (DisplayNameParts = {}));\nexport var FormattedNumberParts = function (props) {\n    var intl = useIntl();\n    var value = props.value, children = props.children, formatProps = __rest(props, [\"value\", \"children\"]);\n    return children(intl.formatNumberToParts(value, formatProps));\n};\nFormattedNumberParts.displayName = 'FormattedNumberParts';\nexport var FormattedListParts = function (props) {\n    var intl = useIntl();\n    var value = props.value, children = props.children, formatProps = __rest(props, [\"value\", \"children\"]);\n    return children(intl.formatListToParts(value, formatProps));\n};\nFormattedNumberParts.displayName = 'FormattedNumberParts';\nexport function createFormattedDateTimePartsComponent(name) {\n    var ComponentParts = function (props) {\n        var intl = useIntl();\n        var value = props.value, children = props.children, formatProps = __rest(props, [\"value\", \"children\"]);\n        var date = typeof value === 'string' ? new Date(value || 0) : value;\n        var formattedParts = name === 'formatDate'\n            ? intl.formatDateToParts(date, formatProps)\n            : intl.formatTimeToParts(date, formatProps);\n        return children(formattedParts);\n    };\n    ComponentParts.displayName = DisplayNameParts[name];\n    return ComponentParts;\n}\nexport function createFormattedComponent(name) {\n    var Component = function (props) {\n        var intl = useIntl();\n        var value = props.value, children = props.children, formatProps = __rest(props\n        // TODO: fix TS type definition for localeMatcher upstream\n        , [\"value\", \"children\"]);\n        // TODO: fix TS type definition for localeMatcher upstream\n        var formattedValue = intl[name](value, formatProps);\n        if (typeof children === 'function') {\n            return children(formattedValue);\n        }\n        var Text = intl.textComponent || React.Fragment;\n        return React.createElement(Text, null, formattedValue);\n    };\n    Component.displayName = DisplayName[name];\n    return Component;\n}\n", "import * as React from 'react';\nimport { invariantIntlContext } from '../utils';\nimport { Context } from './injectIntl';\nexport default function useIntl() {\n    var intl = React.useContext(Context);\n    invariantIntlContext(intl);\n    return intl;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { invariant } from '@formatjs/ecma402-abstract';\nimport { DEFAULT_INTL_CONFIG as CORE_DEFAULT_INTL_CONFIG } from '@formatjs/intl';\nexport function invariantIntlContext(intl) {\n    invariant(intl, '[React Intl] Could not find required `intl` object. ' +\n        '<IntlProvider> needs to exist in the component ancestry.');\n}\nexport var DEFAULT_INTL_CONFIG = __assign(__assign({}, CORE_DEFAULT_INTL_CONFIG), { textComponent: React.Fragment });\n/**\n * Takes a `formatXMLElementFn`, and composes it in function, which passes\n * argument `parts` through, assigning unique key to each part, to prevent\n * \"Each child in a list should have a unique \"key\"\" React error.\n * @param formatXMLElementFn\n */\nexport function assignUniqueKeysToParts(formatXMLElementFn) {\n    return function (parts) {\n        // eslint-disable-next-line prefer-rest-params\n        return formatXMLElementFn(React.Children.toArray(parts));\n    };\n}\nexport function shallowEqual(objA, objB) {\n    if (objA === objB) {\n        return true;\n    }\n    if (!objA || !objB) {\n        return false;\n    }\n    var aKeys = Object.keys(objA);\n    var bKeys = Object.keys(objB);\n    var len = aKeys.length;\n    if (bKeys.length !== len) {\n        return false;\n    }\n    for (var i = 0; i < len; i++) {\n        var key = aKeys[i];\n        if (objA[key] !== objB[key] ||\n            !Object.prototype.hasOwnProperty.call(objB, key)) {\n            return false;\n        }\n    }\n    return true;\n}\n", "/**\n * https://tc39.es/ecma262/#sec-tostring\n */\nexport function ToString(o) {\n    // Only symbol is irregular...\n    if (typeof o === 'symbol') {\n        throw TypeError('Cannot convert a Symbol value to a string');\n    }\n    return String(o);\n}\n/**\n * https://tc39.es/ecma262/#sec-tonumber\n * @param val\n */\nexport function ToNumber(val) {\n    if (val === undefined) {\n        return NaN;\n    }\n    if (val === null) {\n        return +0;\n    }\n    if (typeof val === 'boolean') {\n        return val ? 1 : +0;\n    }\n    if (typeof val === 'number') {\n        return val;\n    }\n    if (typeof val === 'symbol' || typeof val === 'bigint') {\n        throw new TypeError('Cannot convert symbol/bigint to number');\n    }\n    return Number(val);\n}\n/**\n * https://tc39.es/ecma262/#sec-tointeger\n * @param n\n */\nfunction ToInteger(n) {\n    var number = ToNumber(n);\n    if (isNaN(number) || SameValue(number, -0)) {\n        return 0;\n    }\n    if (isFinite(number)) {\n        return number;\n    }\n    var integer = Math.floor(Math.abs(number));\n    if (number < 0) {\n        integer = -integer;\n    }\n    if (SameValue(integer, -0)) {\n        return 0;\n    }\n    return integer;\n}\n/**\n * https://tc39.es/ecma262/#sec-timeclip\n * @param time\n */\nexport function TimeClip(time) {\n    if (!isFinite(time)) {\n        return NaN;\n    }\n    if (Math.abs(time) > 8.64 * 1e15) {\n        return NaN;\n    }\n    return ToInteger(time);\n}\n/**\n * https://tc39.es/ecma262/#sec-toobject\n * @param arg\n */\nexport function ToObject(arg) {\n    if (arg == null) {\n        throw new TypeError('undefined/null cannot be converted to object');\n    }\n    return Object(arg);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-samevalue\n * @param x\n * @param y\n */\nexport function SameValue(x, y) {\n    if (Object.is) {\n        return Object.is(x, y);\n    }\n    // SameValue algorithm\n    if (x === y) {\n        // Steps 1-5, 7-10\n        // Steps 6.b-6.e: +0 != -0\n        return x !== 0 || 1 / x === 1 / y;\n    }\n    // Step 6.a: NaN == NaN\n    return x !== x && y !== y;\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-arraycreate\n * @param len\n */\nexport function ArrayCreate(len) {\n    return new Array(len);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-hasownproperty\n * @param o\n * @param prop\n */\nexport function HasOwnProperty(o, prop) {\n    return Object.prototype.hasOwnProperty.call(o, prop);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-type\n * @param x\n */\nexport function Type(x) {\n    if (x === null) {\n        return 'Null';\n    }\n    if (typeof x === 'undefined') {\n        return 'Undefined';\n    }\n    if (typeof x === 'function' || typeof x === 'object') {\n        return 'Object';\n    }\n    if (typeof x === 'number') {\n        return 'Number';\n    }\n    if (typeof x === 'boolean') {\n        return 'Boolean';\n    }\n    if (typeof x === 'string') {\n        return 'String';\n    }\n    if (typeof x === 'symbol') {\n        return 'Symbol';\n    }\n    if (typeof x === 'bigint') {\n        return 'BigInt';\n    }\n}\nvar MS_PER_DAY = 86400000;\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#eqn-modulo\n * @param x\n * @param y\n * @return k of the same sign as y\n */\nfunction mod(x, y) {\n    return x - Math.floor(x / y) * y;\n}\n/**\n * https://tc39.es/ecma262/#eqn-Day\n * @param t\n */\nexport function Day(t) {\n    return Math.floor(t / MS_PER_DAY);\n}\n/**\n * https://tc39.es/ecma262/#sec-week-day\n * @param t\n */\nexport function WeekDay(t) {\n    return mod(Day(t) + 4, 7);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function DayFromYear(y) {\n    return Date.UTC(y, 0) / MS_PER_DAY;\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function TimeFromYear(y) {\n    return Date.UTC(y, 0);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param t\n */\nexport function YearFromTime(t) {\n    return new Date(t).getUTCFullYear();\n}\nexport function DaysInYear(y) {\n    if (y % 4 !== 0) {\n        return 365;\n    }\n    if (y % 100 !== 0) {\n        return 366;\n    }\n    if (y % 400 !== 0) {\n        return 365;\n    }\n    return 366;\n}\nexport function DayWithinYear(t) {\n    return Day(t) - DayFromYear(YearFromTime(t));\n}\nexport function InLeapYear(t) {\n    return DaysInYear(YearFromTime(t)) === 365 ? 0 : 1;\n}\n/**\n * https://tc39.es/ecma262/#sec-month-number\n * @param t\n */\nexport function MonthFromTime(t) {\n    var dwy = DayWithinYear(t);\n    var leap = InLeapYear(t);\n    if (dwy >= 0 && dwy < 31) {\n        return 0;\n    }\n    if (dwy < 59 + leap) {\n        return 1;\n    }\n    if (dwy < 90 + leap) {\n        return 2;\n    }\n    if (dwy < 120 + leap) {\n        return 3;\n    }\n    if (dwy < 151 + leap) {\n        return 4;\n    }\n    if (dwy < 181 + leap) {\n        return 5;\n    }\n    if (dwy < 212 + leap) {\n        return 6;\n    }\n    if (dwy < 243 + leap) {\n        return 7;\n    }\n    if (dwy < 273 + leap) {\n        return 8;\n    }\n    if (dwy < 304 + leap) {\n        return 9;\n    }\n    if (dwy < 334 + leap) {\n        return 10;\n    }\n    if (dwy < 365 + leap) {\n        return 11;\n    }\n    throw new Error('Invalid time');\n}\nexport function DateFromTime(t) {\n    var dwy = DayWithinYear(t);\n    var mft = MonthFromTime(t);\n    var leap = InLeapYear(t);\n    if (mft === 0) {\n        return dwy + 1;\n    }\n    if (mft === 1) {\n        return dwy - 30;\n    }\n    if (mft === 2) {\n        return dwy - 58 - leap;\n    }\n    if (mft === 3) {\n        return dwy - 89 - leap;\n    }\n    if (mft === 4) {\n        return dwy - 119 - leap;\n    }\n    if (mft === 5) {\n        return dwy - 150 - leap;\n    }\n    if (mft === 6) {\n        return dwy - 180 - leap;\n    }\n    if (mft === 7) {\n        return dwy - 211 - leap;\n    }\n    if (mft === 8) {\n        return dwy - 242 - leap;\n    }\n    if (mft === 9) {\n        return dwy - 272 - leap;\n    }\n    if (mft === 10) {\n        return dwy - 303 - leap;\n    }\n    if (mft === 11) {\n        return dwy - 333 - leap;\n    }\n    throw new Error('Invalid time');\n}\nvar HOURS_PER_DAY = 24;\nvar MINUTES_PER_HOUR = 60;\nvar SECONDS_PER_MINUTE = 60;\nvar MS_PER_SECOND = 1e3;\nvar MS_PER_MINUTE = MS_PER_SECOND * SECONDS_PER_MINUTE;\nvar MS_PER_HOUR = MS_PER_MINUTE * MINUTES_PER_HOUR;\nexport function HourFromTime(t) {\n    return mod(Math.floor(t / MS_PER_HOUR), HOURS_PER_DAY);\n}\nexport function MinFromTime(t) {\n    return mod(Math.floor(t / MS_PER_MINUTE), MINUTES_PER_HOUR);\n}\nexport function SecFromTime(t) {\n    return mod(Math.floor(t / MS_PER_SECOND), SECONDS_PER_MINUTE);\n}\nfunction IsCallable(fn) {\n    return typeof fn === 'function';\n}\n/**\n * The abstract operation OrdinaryHasInstance implements\n * the default algorithm for determining if an object O\n * inherits from the instance object inheritance path\n * provided by constructor C.\n * @param C class\n * @param O object\n * @param internalSlots internalSlots\n */\nexport function OrdinaryHasInstance(C, O, internalSlots) {\n    if (!IsCallable(C)) {\n        return false;\n    }\n    if (internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction) {\n        var BC = internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction;\n        return O instanceof BC;\n    }\n    if (typeof O !== 'object') {\n        return false;\n    }\n    var P = C.prototype;\n    if (typeof P !== 'object') {\n        throw new TypeError('OrdinaryHasInstance called on an object with an invalid prototype property.');\n    }\n    return Object.prototype.isPrototypeOf.call(P, O);\n}\nexport function msFromTime(t) {\n    return mod(t, MS_PER_SECOND);\n}\n", "/**\n * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers\n */\nexport var SANCTIONED_UNITS = [\n    'angle-degree',\n    'area-acre',\n    'area-hectare',\n    'concentr-percent',\n    'digital-bit',\n    'digital-byte',\n    'digital-gigabit',\n    'digital-gigabyte',\n    'digital-kilobit',\n    'digital-kilobyte',\n    'digital-megabit',\n    'digital-megabyte',\n    'digital-petabyte',\n    'digital-terabit',\n    'digital-terabyte',\n    'duration-day',\n    'duration-hour',\n    'duration-millisecond',\n    'duration-minute',\n    'duration-month',\n    'duration-second',\n    'duration-week',\n    'duration-year',\n    'length-centimeter',\n    'length-foot',\n    'length-inch',\n    'length-kilometer',\n    'length-meter',\n    'length-mile-scandinavian',\n    'length-mile',\n    'length-millimeter',\n    'length-yard',\n    'mass-gram',\n    'mass-kilogram',\n    'mass-ounce',\n    'mass-pound',\n    'mass-stone',\n    'temperature-celsius',\n    'temperature-fahrenheit',\n    'volume-fluid-ounce',\n    'volume-gallon',\n    'volume-liter',\n    'volume-milliliter',\n];\n// In CLDR, the unit name always follows the form `namespace-unit` pattern.\n// For example: `digital-bit` instead of `bit`. This function removes the namespace prefix.\nexport function removeUnitNamespace(unit) {\n    return unit.slice(unit.indexOf('-') + 1);\n}\n/**\n * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers\n */\nexport var SIMPLE_UNITS = SANCTIONED_UNITS.map(removeUnitNamespace);\n/**\n * https://tc39.es/ecma402/#sec-issanctionedsimpleunitidentifier\n */\nexport function IsSanctionedSimpleUnitIdentifier(unitIdentifier) {\n    return SIMPLE_UNITS.indexOf(unitIdentifier) > -1;\n}\n", null, "import { __spreadArray } from \"tslib\";\nimport { memoize, strategies } from '@formatjs/fast-memoize';\n/**\n * Cannot do Math.log(x) / Math.log(10) bc if IEEE floating point issue\n * @param x number\n */\nexport function getMagnitude(x) {\n    // Cannot count string length via Number.toString because it may use scientific notation\n    // for very small or very large numbers.\n    return Math.floor(Math.log(x) * Math.LOG10E);\n}\nexport function repeat(s, times) {\n    if (typeof s.repeat === 'function') {\n        return s.repeat(times);\n    }\n    var arr = new Array(times);\n    for (var i = 0; i < arr.length; i++) {\n        arr[i] = s;\n    }\n    return arr.join('');\n}\nexport function setInternalSlot(map, pl, field, value) {\n    if (!map.get(pl)) {\n        map.set(pl, Object.create(null));\n    }\n    var slots = map.get(pl);\n    slots[field] = value;\n}\nexport function setMultiInternalSlots(map, pl, props) {\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\n        var k = _a[_i];\n        setInternalSlot(map, pl, k, props[k]);\n    }\n}\nexport function getInternalSlot(map, pl, field) {\n    return getMultiInternalSlots(map, pl, field)[field];\n}\nexport function getMultiInternalSlots(map, pl) {\n    var fields = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        fields[_i - 2] = arguments[_i];\n    }\n    var slots = map.get(pl);\n    if (!slots) {\n        throw new TypeError(\"\".concat(pl, \" InternalSlot has not been initialized\"));\n    }\n    return fields.reduce(function (all, f) {\n        all[f] = slots[f];\n        return all;\n    }, Object.create(null));\n}\nexport function isLiteralPart(patternPart) {\n    return patternPart.type === 'literal';\n}\n/*\n  17 ECMAScript Standard Built-in Objects:\n    Every built-in Function object, including constructors, that is not\n    identified as an anonymous function has a name property whose value\n    is a String.\n\n    Unless otherwise specified, the name property of a built-in Function\n    object, if it exists, has the attributes { [[Writable]]: false,\n    [[Enumerable]]: false, [[Configurable]]: true }.\n*/\nexport function defineProperty(target, name, _a) {\n    var value = _a.value;\n    Object.defineProperty(target, name, {\n        configurable: true,\n        enumerable: false,\n        writable: true,\n        value: value,\n    });\n}\n/**\n * 7.3.5 CreateDataProperty\n * @param target\n * @param name\n * @param value\n */\nexport function createDataProperty(target, name, value) {\n    Object.defineProperty(target, name, {\n        configurable: true,\n        enumerable: true,\n        writable: true,\n        value: value,\n    });\n}\nexport var UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi;\nexport function invariant(condition, message, Err) {\n    if (Err === void 0) { Err = Error; }\n    if (!condition) {\n        throw new Err(message);\n    }\n}\nexport var createMemoizedNumberFormat = memoize(function () {\n    var _a;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n}, {\n    strategy: strategies.variadic,\n});\nexport var createMemoizedDateTimeFormat = memoize(function () {\n    var _a;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n}, {\n    strategy: strategies.variadic,\n});\nexport var createMemoizedPluralRules = memoize(function () {\n    var _a;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n}, {\n    strategy: strategies.variadic,\n});\nexport var createMemoizedLocale = memoize(function () {\n    var _a;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return new ((_a = Intl.Locale).bind.apply(_a, __spreadArray([void 0], args, false)))();\n}, {\n    strategy: strategies.variadic,\n});\nexport var createMemoizedListFormat = memoize(function () {\n    var _a;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return new ((_a = Intl.ListFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n}, {\n    strategy: strategies.variadic,\n});\n", "// @generated from regex-gen.ts\nexport var S_UNICODE_REGEX = /[\\$\\+<->\\^`\\|~\\xA2-\\xA6\\xA8\\xA9\\xAC\\xAE-\\xB1\\xB4\\xB8\\xD7\\xF7\\u02C2-\\u02C5\\u02D2-\\u02DF\\u02E5-\\u02EB\\u02ED\\u02EF-\\u02FF\\u0375\\u0384\\u0385\\u03F6\\u0482\\u058D-\\u058F\\u0606-\\u0608\\u060B\\u060E\\u060F\\u06DE\\u06E9\\u06FD\\u06FE\\u07F6\\u07FE\\u07FF\\u09F2\\u09F3\\u09FA\\u09FB\\u0AF1\\u0B70\\u0BF3-\\u0BFA\\u0C7F\\u0D4F\\u0D79\\u0E3F\\u0F01-\\u0F03\\u0F13\\u0F15-\\u0F17\\u0F1A-\\u0F1F\\u0F34\\u0F36\\u0F38\\u0FBE-\\u0FC5\\u0FC7-\\u0FCC\\u0FCE\\u0FCF\\u0FD5-\\u0FD8\\u109E\\u109F\\u1390-\\u1399\\u166D\\u17DB\\u1940\\u19DE-\\u19FF\\u1B61-\\u1B6A\\u1B74-\\u1B7C\\u1FBD\\u1FBF-\\u1FC1\\u1FCD-\\u1FCF\\u1FDD-\\u1FDF\\u1FED-\\u1FEF\\u1FFD\\u1FFE\\u2044\\u2052\\u207A-\\u207C\\u208A-\\u208C\\u20A0-\\u20BF\\u2100\\u2101\\u2103-\\u2106\\u2108\\u2109\\u2114\\u2116-\\u2118\\u211E-\\u2123\\u2125\\u2127\\u2129\\u212E\\u213A\\u213B\\u2140-\\u2144\\u214A-\\u214D\\u214F\\u218A\\u218B\\u2190-\\u2307\\u230C-\\u2328\\u232B-\\u2426\\u2440-\\u244A\\u249C-\\u24E9\\u2500-\\u2767\\u2794-\\u27C4\\u27C7-\\u27E5\\u27F0-\\u2982\\u2999-\\u29D7\\u29DC-\\u29FB\\u29FE-\\u2B73\\u2B76-\\u2B95\\u2B97-\\u2BFF\\u2CE5-\\u2CEA\\u2E50\\u2E51\\u2E80-\\u2E99\\u2E9B-\\u2EF3\\u2F00-\\u2FD5\\u2FF0-\\u2FFB\\u3004\\u3012\\u3013\\u3020\\u3036\\u3037\\u303E\\u303F\\u309B\\u309C\\u3190\\u3191\\u3196-\\u319F\\u31C0-\\u31E3\\u3200-\\u321E\\u322A-\\u3247\\u3250\\u3260-\\u327F\\u328A-\\u32B0\\u32C0-\\u33FF\\u4DC0-\\u4DFF\\uA490-\\uA4C6\\uA700-\\uA716\\uA720\\uA721\\uA789\\uA78A\\uA828-\\uA82B\\uA836-\\uA839\\uAA77-\\uAA79\\uAB5B\\uAB6A\\uAB6B\\uFB29\\uFBB2-\\uFBC1\\uFDFC\\uFDFD\\uFE62\\uFE64-\\uFE66\\uFE69\\uFF04\\uFF0B\\uFF1C-\\uFF1E\\uFF3E\\uFF40\\uFF5C\\uFF5E\\uFFE0-\\uFFE6\\uFFE8-\\uFFEE\\uFFFC\\uFFFD]|\\uD800[\\uDD37-\\uDD3F\\uDD79-\\uDD89\\uDD8C-\\uDD8E\\uDD90-\\uDD9C\\uDDA0\\uDDD0-\\uDDFC]|\\uD802[\\uDC77\\uDC78\\uDEC8]|\\uD805\\uDF3F|\\uD807[\\uDFD5-\\uDFF1]|\\uD81A[\\uDF3C-\\uDF3F\\uDF45]|\\uD82F\\uDC9C|\\uD834[\\uDC00-\\uDCF5\\uDD00-\\uDD26\\uDD29-\\uDD64\\uDD6A-\\uDD6C\\uDD83\\uDD84\\uDD8C-\\uDDA9\\uDDAE-\\uDDE8\\uDE00-\\uDE41\\uDE45\\uDF00-\\uDF56]|\\uD835[\\uDEC1\\uDEDB\\uDEFB\\uDF15\\uDF35\\uDF4F\\uDF6F\\uDF89\\uDFA9\\uDFC3]|\\uD836[\\uDC00-\\uDDFF\\uDE37-\\uDE3A\\uDE6D-\\uDE74\\uDE76-\\uDE83\\uDE85\\uDE86]|\\uD838[\\uDD4F\\uDEFF]|\\uD83B[\\uDCAC\\uDCB0\\uDD2E\\uDEF0\\uDEF1]|\\uD83C[\\uDC00-\\uDC2B\\uDC30-\\uDC93\\uDCA0-\\uDCAE\\uDCB1-\\uDCBF\\uDCC1-\\uDCCF\\uDCD1-\\uDCF5\\uDD0D-\\uDDAD\\uDDE6-\\uDE02\\uDE10-\\uDE3B\\uDE40-\\uDE48\\uDE50\\uDE51\\uDE60-\\uDE65\\uDF00-\\uDFFF]|\\uD83D[\\uDC00-\\uDED7\\uDEE0-\\uDEEC\\uDEF0-\\uDEFC\\uDF00-\\uDF73\\uDF80-\\uDFD8\\uDFE0-\\uDFEB]|\\uD83E[\\uDC00-\\uDC0B\\uDC10-\\uDC47\\uDC50-\\uDC59\\uDC60-\\uDC87\\uDC90-\\uDCAD\\uDCB0\\uDCB1\\uDD00-\\uDD78\\uDD7A-\\uDDCB\\uDDCD-\\uDE53\\uDE60-\\uDE6D\\uDE70-\\uDE74\\uDE78-\\uDE7A\\uDE80-\\uDE86\\uDE90-\\uDEA8\\uDEB0-\\uDEB6\\uDEC0-\\uDEC2\\uDED0-\\uDED6\\uDF00-\\uDF92\\uDF94-\\uDFCA]/;\n", "import { S_UNICODE_REGEX } from '../regex.generated';\nimport { ToRawFixed } from './ToRawFixed';\nimport { digitMapping } from './digit-mapping.generated';\n// This is from: unicode-12.1.0/General_Category/Symbol/regex.js\n// IE11 does not support unicode flag, otherwise this is just /\\p{S}/u.\n// /^\\p{S}/u\nvar CARET_S_UNICODE_REGEX = new RegExp(\"^\".concat(S_UNICODE_REGEX.source));\n// /\\p{S}$/u\nvar S_DOLLAR_UNICODE_REGEX = new RegExp(\"\".concat(S_UNICODE_REGEX.source, \"$\"));\nvar CLDR_NUMBER_PATTERN = /[#0](?:[\\.,][#0]+)*/g;\nexport default function formatToParts(numberResult, data, pl, options) {\n    var sign = numberResult.sign, exponent = numberResult.exponent, magnitude = numberResult.magnitude;\n    var notation = options.notation, style = options.style, numberingSystem = options.numberingSystem;\n    var defaultNumberingSystem = data.numbers.nu[0];\n    // #region Part 1: partition and interpolate the CLDR number pattern.\n    // ----------------------------------------------------------\n    var compactNumberPattern = null;\n    if (notation === 'compact' && magnitude) {\n        compactNumberPattern = getCompactDisplayPattern(numberResult, pl, data, style, options.compactDisplay, options.currencyDisplay, numberingSystem);\n    }\n    // This is used multiple times\n    var nonNameCurrencyPart;\n    if (style === 'currency' && options.currencyDisplay !== 'name') {\n        var byCurrencyDisplay = data.currencies[options.currency];\n        if (byCurrencyDisplay) {\n            switch (options.currencyDisplay) {\n                case 'code':\n                    nonNameCurrencyPart = options.currency;\n                    break;\n                case 'symbol':\n                    nonNameCurrencyPart = byCurrencyDisplay.symbol;\n                    break;\n                default:\n                    nonNameCurrencyPart = byCurrencyDisplay.narrow;\n                    break;\n            }\n        }\n        else {\n            // Fallback for unknown currency\n            nonNameCurrencyPart = options.currency;\n        }\n    }\n    var numberPattern;\n    if (!compactNumberPattern) {\n        // Note: if the style is unit, or is currency and the currency display is name,\n        // its unit parts will be interpolated in part 2. So here we can fallback to decimal.\n        if (style === 'decimal' ||\n            style === 'unit' ||\n            (style === 'currency' && options.currencyDisplay === 'name')) {\n            // Shortcut for decimal\n            var decimalData = data.numbers.decimal[numberingSystem] ||\n                data.numbers.decimal[defaultNumberingSystem];\n            numberPattern = getPatternForSign(decimalData.standard, sign);\n        }\n        else if (style === 'currency') {\n            var currencyData = data.numbers.currency[numberingSystem] ||\n                data.numbers.currency[defaultNumberingSystem];\n            // We replace number pattern part with `0` for easier postprocessing.\n            numberPattern = getPatternForSign(currencyData[options.currencySign], sign);\n        }\n        else {\n            // percent\n            var percentPattern = data.numbers.percent[numberingSystem] ||\n                data.numbers.percent[defaultNumberingSystem];\n            numberPattern = getPatternForSign(percentPattern, sign);\n        }\n    }\n    else {\n        numberPattern = compactNumberPattern;\n    }\n    // Extract the decimal number pattern string. It looks like \"#,##0,00\", which will later be\n    // used to infer decimal group sizes.\n    var decimalNumberPattern = CLDR_NUMBER_PATTERN.exec(numberPattern)[0];\n    // Now we start to substitute patterns\n    // 1. replace strings like `0` and `#,##0.00` with `{0}`\n    // 2. unquote characters (invariant: the quoted characters does not contain the special tokens)\n    numberPattern = numberPattern\n        .replace(CLDR_NUMBER_PATTERN, '{0}')\n        .replace(/'(.)'/g, '$1');\n    // Handle currency spacing (both compact and non-compact).\n    if (style === 'currency' && options.currencyDisplay !== 'name') {\n        var currencyData = data.numbers.currency[numberingSystem] ||\n            data.numbers.currency[defaultNumberingSystem];\n        // See `currencySpacing` substitution rule in TR-35.\n        // Here we always assume the currencyMatch is \"[:^S:]\" and surroundingMatch is \"[:digit:]\".\n        //\n        // Example 1: for pattern \"#,##0.00¤\" with symbol \"US$\", we replace \"¤\" with the symbol,\n        // but insert an extra non-break space before the symbol, because \"[:^S:]\" matches \"U\" in\n        // \"US$\" and \"[:digit:]\" matches the latn numbering system digits.\n        //\n        // Example 2: for pattern \"¤#,##0.00\" with symbol \"US$\", there is no spacing between symbol\n        // and number, because `$` does not match \"[:^S:]\".\n        //\n        // Implementation note: here we do the best effort to infer the insertion.\n        // We also assume that `beforeInsertBetween` and `afterInsertBetween` will never be `;`.\n        var afterCurrency = currencyData.currencySpacing.afterInsertBetween;\n        if (afterCurrency && !S_DOLLAR_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n            numberPattern = numberPattern.replace('¤{0}', \"\\u00A4\".concat(afterCurrency, \"{0}\"));\n        }\n        var beforeCurrency = currencyData.currencySpacing.beforeInsertBetween;\n        if (beforeCurrency && !CARET_S_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n            numberPattern = numberPattern.replace('{0}¤', \"{0}\".concat(beforeCurrency, \"\\u00A4\"));\n        }\n    }\n    // The following tokens are special: `{0}`, `¤`, `%`, `-`, `+`, `{c:...}.\n    var numberPatternParts = numberPattern.split(/({c:[^}]+}|\\{0\\}|[¤%\\-\\+])/g);\n    var numberParts = [];\n    var symbols = data.numbers.symbols[numberingSystem] ||\n        data.numbers.symbols[defaultNumberingSystem];\n    for (var _i = 0, numberPatternParts_1 = numberPatternParts; _i < numberPatternParts_1.length; _i++) {\n        var part = numberPatternParts_1[_i];\n        if (!part) {\n            continue;\n        }\n        switch (part) {\n            case '{0}': {\n                // We only need to handle scientific and engineering notation here.\n                numberParts.push.apply(numberParts, paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem, \n                // If compact number pattern exists, do not insert group separators.\n                !compactNumberPattern && Boolean(options.useGrouping), decimalNumberPattern, style));\n                break;\n            }\n            case '-':\n                numberParts.push({ type: 'minusSign', value: symbols.minusSign });\n                break;\n            case '+':\n                numberParts.push({ type: 'plusSign', value: symbols.plusSign });\n                break;\n            case '%':\n                numberParts.push({ type: 'percentSign', value: symbols.percentSign });\n                break;\n            case '¤':\n                // Computed above when handling currency spacing.\n                numberParts.push({ type: 'currency', value: nonNameCurrencyPart });\n                break;\n            default:\n                if (/^\\{c:/.test(part)) {\n                    numberParts.push({\n                        type: 'compact',\n                        value: part.substring(3, part.length - 1),\n                    });\n                }\n                else {\n                    // literal\n                    numberParts.push({ type: 'literal', value: part });\n                }\n                break;\n        }\n    }\n    // #endregion\n    // #region Part 2: interpolate unit pattern if necessary.\n    // ----------------------------------------------\n    switch (style) {\n        case 'currency': {\n            // `currencyDisplay: 'name'` has similar pattern handling as units.\n            if (options.currencyDisplay === 'name') {\n                var unitPattern = (data.numbers.currency[numberingSystem] ||\n                    data.numbers.currency[defaultNumberingSystem]).unitPattern;\n                // Select plural\n                var unitName = void 0;\n                var currencyNameData = data.currencies[options.currency];\n                if (currencyNameData) {\n                    unitName = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), currencyNameData.displayName);\n                }\n                else {\n                    // Fallback for unknown currency\n                    unitName = options.currency;\n                }\n                // Do {0} and {1} substitution\n                var unitPatternParts = unitPattern.split(/(\\{[01]\\})/g);\n                var result = [];\n                for (var _a = 0, unitPatternParts_1 = unitPatternParts; _a < unitPatternParts_1.length; _a++) {\n                    var part = unitPatternParts_1[_a];\n                    switch (part) {\n                        case '{0}':\n                            result.push.apply(result, numberParts);\n                            break;\n                        case '{1}':\n                            result.push({ type: 'currency', value: unitName });\n                            break;\n                        default:\n                            if (part) {\n                                result.push({ type: 'literal', value: part });\n                            }\n                            break;\n                    }\n                }\n                return result;\n            }\n            else {\n                return numberParts;\n            }\n        }\n        case 'unit': {\n            var unit = options.unit, unitDisplay = options.unitDisplay;\n            var unitData = data.units.simple[unit];\n            var unitPattern = void 0;\n            if (unitData) {\n                // Simple unit pattern\n                unitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[unit][unitDisplay]);\n            }\n            else {\n                // See: http://unicode.org/reports/tr35/tr35-general.html#perUnitPatterns\n                // If cannot find unit in the simple pattern, it must be \"per\" compound pattern.\n                // Implementation note: we are not following TR-35 here because we need to format to parts!\n                var _b = unit.split('-per-'), numeratorUnit = _b[0], denominatorUnit = _b[1];\n                unitData = data.units.simple[numeratorUnit];\n                var numeratorUnitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[numeratorUnit][unitDisplay]);\n                var perUnitPattern = data.units.simple[denominatorUnit].perUnit[unitDisplay];\n                if (perUnitPattern) {\n                    // perUnitPattern exists, combine it with numeratorUnitPattern\n                    unitPattern = perUnitPattern.replace('{0}', numeratorUnitPattern);\n                }\n                else {\n                    // get compoundUnit pattern (e.g. \"{0} per {1}\"), repalce {0} with numerator pattern and {1} with\n                    // the denominator pattern in singular form.\n                    var perPattern = data.units.compound.per[unitDisplay];\n                    var denominatorPattern = selectPlural(pl, 1, data.units.simple[denominatorUnit][unitDisplay]);\n                    unitPattern = unitPattern = perPattern\n                        .replace('{0}', numeratorUnitPattern)\n                        .replace('{1}', denominatorPattern.replace('{0}', ''));\n                }\n            }\n            var result = [];\n            // We need spacing around \"{0}\" because they are not treated as \"unit\" parts, but \"literal\".\n            for (var _c = 0, _d = unitPattern.split(/(\\s*\\{0\\}\\s*)/); _c < _d.length; _c++) {\n                var part = _d[_c];\n                var interpolateMatch = /^(\\s*)\\{0\\}(\\s*)$/.exec(part);\n                if (interpolateMatch) {\n                    // Space before \"{0}\"\n                    if (interpolateMatch[1]) {\n                        result.push({ type: 'literal', value: interpolateMatch[1] });\n                    }\n                    // \"{0}\" itself\n                    result.push.apply(result, numberParts);\n                    // Space after \"{0}\"\n                    if (interpolateMatch[2]) {\n                        result.push({ type: 'literal', value: interpolateMatch[2] });\n                    }\n                }\n                else if (part) {\n                    result.push({ type: 'unit', value: part });\n                }\n            }\n            return result;\n        }\n        default:\n            return numberParts;\n    }\n    // #endregion\n}\n// A subset of https://tc39.es/ecma402/#sec-partitionnotationsubpattern\n// Plus the exponent parts handling.\nfunction paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem, useGrouping, \n/**\n * This is the decimal number pattern without signs or symbols.\n * It is used to infer the group size when `useGrouping` is true.\n *\n * A typical value looks like \"#,##0.00\" (primary group size is 3).\n * Some locales like Hindi has secondary group size of 2 (e.g. \"#,##,##0.00\").\n */\ndecimalNumberPattern, style) {\n    var result = [];\n    // eslint-disable-next-line prefer-const\n    var n = numberResult.formattedString, x = numberResult.roundedNumber;\n    if (isNaN(x)) {\n        return [{ type: 'nan', value: n }];\n    }\n    else if (!isFinite(x)) {\n        return [{ type: 'infinity', value: n }];\n    }\n    var digitReplacementTable = digitMapping[numberingSystem];\n    if (digitReplacementTable) {\n        n = n.replace(/\\d/g, function (digit) { return digitReplacementTable[+digit] || digit; });\n    }\n    // TODO: Else use an implementation dependent algorithm to map n to the appropriate\n    // representation of n in the given numbering system.\n    var decimalSepIndex = n.indexOf('.');\n    var integer;\n    var fraction;\n    if (decimalSepIndex > 0) {\n        integer = n.slice(0, decimalSepIndex);\n        fraction = n.slice(decimalSepIndex + 1);\n    }\n    else {\n        integer = n;\n    }\n    // #region Grouping integer digits\n    // The weird compact and x >= 10000 check is to ensure consistency with Node.js and Chrome.\n    // Note that `de` does not have compact form for thousands, but Node.js does not insert grouping separator\n    // unless the rounded number is greater than 10000:\n    //   NumberFormat('de', {notation: 'compact', compactDisplay: 'short'}).format(1234) //=> \"1234\"\n    //   NumberFormat('de').format(1234) //=> \"1.234\"\n    if (useGrouping && (notation !== 'compact' || x >= 10000)) {\n        // a. Let groupSepSymbol be the implementation-, locale-, and numbering system-dependent (ILND) String representing the grouping separator.\n        // For currency we should use `currencyGroup` instead of generic `group`\n        var groupSepSymbol = style === 'currency' && symbols.currencyGroup != null\n            ? symbols.currencyGroup\n            : symbols.group;\n        var groups = [];\n        // > There may be two different grouping sizes: The primary grouping size used for the least\n        // > significant integer group, and the secondary grouping size used for more significant groups.\n        // > If a pattern contains multiple grouping separators, the interval between the last one and the\n        // > end of the integer defines the primary grouping size, and the interval between the last two\n        // > defines the secondary grouping size. All others are ignored.\n        var integerNumberPattern = decimalNumberPattern.split('.')[0];\n        var patternGroups = integerNumberPattern.split(',');\n        var primaryGroupingSize = 3;\n        var secondaryGroupingSize = 3;\n        if (patternGroups.length > 1) {\n            primaryGroupingSize = patternGroups[patternGroups.length - 1].length;\n        }\n        if (patternGroups.length > 2) {\n            secondaryGroupingSize = patternGroups[patternGroups.length - 2].length;\n        }\n        var i = integer.length - primaryGroupingSize;\n        if (i > 0) {\n            // Slice the least significant integer group\n            groups.push(integer.slice(i, i + primaryGroupingSize));\n            // Then iteratively push the more signicant groups\n            // TODO: handle surrogate pairs in some numbering system digits\n            for (i -= secondaryGroupingSize; i > 0; i -= secondaryGroupingSize) {\n                groups.push(integer.slice(i, i + secondaryGroupingSize));\n            }\n            groups.push(integer.slice(0, i + secondaryGroupingSize));\n        }\n        else {\n            groups.push(integer);\n        }\n        while (groups.length > 0) {\n            var integerGroup = groups.pop();\n            result.push({ type: 'integer', value: integerGroup });\n            if (groups.length > 0) {\n                result.push({ type: 'group', value: groupSepSymbol });\n            }\n        }\n    }\n    else {\n        result.push({ type: 'integer', value: integer });\n    }\n    // #endregion\n    if (fraction !== undefined) {\n        var decimalSepSymbol = style === 'currency' && symbols.currencyDecimal != null\n            ? symbols.currencyDecimal\n            : symbols.decimal;\n        result.push({ type: 'decimal', value: decimalSepSymbol }, { type: 'fraction', value: fraction });\n    }\n    if ((notation === 'scientific' || notation === 'engineering') &&\n        isFinite(x)) {\n        result.push({ type: 'exponentSeparator', value: symbols.exponential });\n        if (exponent < 0) {\n            result.push({ type: 'exponentMinusSign', value: symbols.minusSign });\n            exponent = -exponent;\n        }\n        var exponentResult = ToRawFixed(exponent, 0, 0);\n        result.push({\n            type: 'exponentInteger',\n            value: exponentResult.formattedString,\n        });\n    }\n    return result;\n}\nfunction getPatternForSign(pattern, sign) {\n    if (pattern.indexOf(';') < 0) {\n        pattern = \"\".concat(pattern, \";-\").concat(pattern);\n    }\n    var _a = pattern.split(';'), zeroPattern = _a[0], negativePattern = _a[1];\n    switch (sign) {\n        case 0:\n            return zeroPattern;\n        case -1:\n            return negativePattern;\n        default:\n            return negativePattern.indexOf('-') >= 0\n                ? negativePattern.replace(/-/g, '+')\n                : \"+\".concat(zeroPattern);\n    }\n}\n// Find the CLDR pattern for compact notation based on the magnitude of data and style.\n//\n// Example return value: \"¤ {c:laki}000;¤{c:laki} -0\" (`sw` locale):\n// - Notice the `{c:...}` token that wraps the compact literal.\n// - The consecutive zeros are normalized to single zero to match CLDR_NUMBER_PATTERN.\n//\n// Returning null means the compact display pattern cannot be found.\nfunction getCompactDisplayPattern(numberResult, pl, data, style, compactDisplay, currencyDisplay, numberingSystem) {\n    var _a;\n    var roundedNumber = numberResult.roundedNumber, sign = numberResult.sign, magnitude = numberResult.magnitude;\n    var magnitudeKey = String(Math.pow(10, magnitude));\n    var defaultNumberingSystem = data.numbers.nu[0];\n    var pattern;\n    if (style === 'currency' && currencyDisplay !== 'name') {\n        var byNumberingSystem = data.numbers.currency;\n        var currencyData = byNumberingSystem[numberingSystem] ||\n            byNumberingSystem[defaultNumberingSystem];\n        // NOTE: compact notation ignores currencySign!\n        var compactPluralRules = (_a = currencyData.short) === null || _a === void 0 ? void 0 : _a[magnitudeKey];\n        if (!compactPluralRules) {\n            return null;\n        }\n        pattern = selectPlural(pl, roundedNumber, compactPluralRules);\n    }\n    else {\n        var byNumberingSystem = data.numbers.decimal;\n        var byCompactDisplay = byNumberingSystem[numberingSystem] ||\n            byNumberingSystem[defaultNumberingSystem];\n        var compactPlaralRule = byCompactDisplay[compactDisplay][magnitudeKey];\n        if (!compactPlaralRule) {\n            return null;\n        }\n        pattern = selectPlural(pl, roundedNumber, compactPlaralRule);\n    }\n    // See https://unicode.org/reports/tr35/tr35-numbers.html#Compact_Number_Formats\n    // > If the value is precisely “0”, either explicit or defaulted, then the normal number format\n    // > pattern for that sort of object is supplied.\n    if (pattern === '0') {\n        return null;\n    }\n    pattern = getPatternForSign(pattern, sign)\n        // Extract compact literal from the pattern\n        .replace(/([^\\s;\\-\\+\\d¤]+)/g, '{c:$1}')\n        // We replace one or more zeros with a single zero so it matches `CLDR_NUMBER_PATTERN`.\n        .replace(/0+/, '0');\n    return pattern;\n}\nfunction selectPlural(pl, x, rules) {\n    return rules[pl.select(x)] || rules.other;\n}\n", "import { __extends } from \"tslib\";\nvar MissingLocaleDataError = /** @class */ (function (_super) {\n    __extends(MissingLocaleDataError, _super);\n    function MissingLocaleDataError() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = 'MISSING_LOCALE_DATA';\n        return _this;\n    }\n    return MissingLocaleDataError;\n}(Error));\nexport function isMissingLocaleDataError(e) {\n    return e.type === 'MISSING_LOCALE_DATA';\n}\n", "export var RangePatternType;\n(function (RangePatternType) {\n    RangePatternType[\"startRange\"] = \"startRange\";\n    RangePatternType[\"shared\"] = \"shared\";\n    RangePatternType[\"endRange\"] = \"endRange\";\n})(RangePatternType || (RangePatternType = {}));\n", "export var ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n", "export var TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nexport var SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nexport function isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexport function isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexport function isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexport function isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexport function isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexport function isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexport function isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexport function isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexport function isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexport function isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexport function isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexport function createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexport function createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n", "// @generated from regex-gen.ts\nexport var SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n", "/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n", "// @generated from regex-gen.ts\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n", "import { __assign } from \"tslib\";\nimport { WHITE_SPACE_REGEX } from './regex.generated';\nexport function parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nexport function parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = __assign(__assign(__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = __assign(__assign(__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = __assign(__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = __assign(__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = __assign(__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n", "// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n", "import { timeData } from './time-data.generated';\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nexport function getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = timeData[regionTag || ''] ||\n        timeData[languageTag || ''] ||\n        timeData[\"\".concat(languageTag, \"-001\")] ||\n        timeData['001'];\n    return hourCycles[0];\n}\n", "var _a;\nimport { __assign } from \"tslib\";\nimport { <PERSON>rrorKind } from './error';\nimport { SKELETON_TYPE, TYPE, } from './types';\nimport { SPACE_SEPARATOR_REGEX } from './regex.generated';\nimport { parseNumberSkeleton, parseNumberSkeletonFromString, parseDateTimeSkeleton, } from '@formatjs/icu-skeleton-parser';\nimport { getBestPattern } from './date-time-pattern-generator';\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = getBestPattern(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? parseDateTimeSkeleton(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? TYPE.date : TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? TYPE.number\n                            : argType === 'date'\n                                ? TYPE.date\n                                : TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = parseNumberSkeletonFromString(skeleton);\n        }\n        catch (e) {\n            return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? parseNumberSkeleton(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n", "import { __assign } from \"tslib\";\nimport { ErrorKind } from './error';\nimport { Parser } from './parser';\nimport { isDateElement, isDateTimeSkeleton, isNumberElement, isNumberSkeleton, isPluralElement, isSelectElement, isTagElement, isTimeElement, } from './types';\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if (isSelectElement(el) || isPluralElement(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if (isNumberElement(el) && isNumberSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if ((isDateElement(el) || isTimeElement(el)) &&\n            isDateTimeSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if (isTagElement(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nexport function parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexport * from './types';\n// only for testing\nexport var _Parser = Parser;\n", "import { __extends } from \"tslib\";\nexport var ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    __extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexport { FormatError };\nvar InvalidValueError = /** @class */ (function (_super) {\n    __extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexport { InvalidValueError };\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    __extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexport { InvalidValueTypeError };\nvar MissingValueError = /** @class */ (function (_super) {\n    __extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexport { MissingValueError };\n", "import { isArgumentElement, isDateElement, isDateTimeSkeleton, isLiteralElement, isNumberElement, isNumberSkeleton, isPluralElement, isPoundElement, isSelectElement, isTagElement, isTimeElement, } from '@formatjs/icu-messageformat-parser';\nimport { ErrorCode, FormatError, InvalidValueError, InvalidValueTypeError, MissingValueError, } from './error';\nexport var PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nexport function isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nexport function formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && isLiteralElement(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if (isLiteralElement(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if (isPoundElement(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if (isArgumentElement(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if (isDateElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTimeElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isNumberElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : isNumberSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTagElement(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if (isSelectElement(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if (isPluralElement(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n", "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { parse, } from '@formatjs/icu-messageformat-parser';\nimport { formatToParts, PART_TYPE, } from './formatters';\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return __assign(__assign(__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = __assign(__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, __assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: strategies.variadic,\n        }),\n        getDateTimeFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return formatToParts(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = __rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, __assign(__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexport { IntlMessageFormat };\n", "import { __extends } from \"tslib\";\nexport var IntlErrorCode;\n(function (IntlErrorCode) {\n    IntlErrorCode[\"FORMAT_ERROR\"] = \"FORMAT_ERROR\";\n    IntlErrorCode[\"UNSUPPORTED_FORMATTER\"] = \"UNSUPPORTED_FORMATTER\";\n    IntlErrorCode[\"INVALID_CONFIG\"] = \"INVALID_CONFIG\";\n    IntlErrorCode[\"MISSING_DATA\"] = \"MISSING_DATA\";\n    IntlErrorCode[\"MISSING_TRANSLATION\"] = \"MISSING_TRANSLATION\";\n})(IntlErrorCode || (IntlErrorCode = {}));\nvar IntlError = /** @class */ (function (_super) {\n    __extends(IntlError, _super);\n    function IntlError(code, message, exception) {\n        var _this = this;\n        var err = exception\n            ? exception instanceof Error\n                ? exception\n                : new Error(String(exception))\n            : undefined;\n        _this = _super.call(this, \"[@formatjs/intl Error \".concat(code, \"] \").concat(message, \"\\n\").concat(err ? \"\\n\".concat(err.message, \"\\n\").concat(err.stack) : '')) || this;\n        _this.code = code;\n        // @ts-ignore just so we don't need to declare dep on @types/node\n        if (typeof Error.captureStackTrace === 'function') {\n            // @ts-ignore just so we don't need to declare dep on @types/node\n            Error.captureStackTrace(_this, IntlError);\n        }\n        return _this;\n    }\n    return IntlError;\n}(Error));\nexport { IntlError };\nvar UnsupportedFormatterError = /** @class */ (function (_super) {\n    __extends(UnsupportedFormatterError, _super);\n    function UnsupportedFormatterError(message, exception) {\n        return _super.call(this, IntlErrorCode.UNSUPPORTED_FORMATTER, message, exception) || this;\n    }\n    return UnsupportedFormatterError;\n}(IntlError));\nexport { UnsupportedFormatterError };\nvar InvalidConfigError = /** @class */ (function (_super) {\n    __extends(InvalidConfigError, _super);\n    function InvalidConfigError(message, exception) {\n        return _super.call(this, IntlErrorCode.INVALID_CONFIG, message, exception) || this;\n    }\n    return InvalidConfigError;\n}(IntlError));\nexport { InvalidConfigError };\nvar MissingDataError = /** @class */ (function (_super) {\n    __extends(MissingDataError, _super);\n    function MissingDataError(message, exception) {\n        return _super.call(this, IntlErrorCode.MISSING_DATA, message, exception) || this;\n    }\n    return MissingDataError;\n}(IntlError));\nexport { MissingDataError };\nvar IntlFormatError = /** @class */ (function (_super) {\n    __extends(IntlFormatError, _super);\n    function IntlFormatError(message, locale, exception) {\n        var _this = _super.call(this, IntlErrorCode.FORMAT_ERROR, \"\".concat(message, \"\\nLocale: \").concat(locale, \"\\n\"), exception) || this;\n        _this.locale = locale;\n        return _this;\n    }\n    return IntlFormatError;\n}(IntlError));\nexport { IntlFormatError };\nvar MessageFormatError = /** @class */ (function (_super) {\n    __extends(MessageFormatError, _super);\n    function MessageFormatError(message, locale, descriptor, exception) {\n        var _this = _super.call(this, \"\".concat(message, \"\\nMessageID: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.id, \"\\nDefault Message: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.defaultMessage, \"\\nDescription: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.description, \"\\n\"), locale, exception) || this;\n        _this.descriptor = descriptor;\n        _this.locale = locale;\n        return _this;\n    }\n    return MessageFormatError;\n}(IntlFormatError));\nexport { MessageFormatError };\nvar MissingTranslationError = /** @class */ (function (_super) {\n    __extends(MissingTranslationError, _super);\n    function MissingTranslationError(descriptor, locale) {\n        var _this = _super.call(this, IntlErrorCode.MISSING_TRANSLATION, \"Missing message: \\\"\".concat(descriptor.id, \"\\\" for locale \\\"\").concat(locale, \"\\\", using \").concat(descriptor.defaultMessage\n            ? \"default message (\".concat(typeof descriptor.defaultMessage === 'string'\n                ? descriptor.defaultMessage\n                : descriptor.defaultMessage\n                    .map(function (e) { var _a; return (_a = e.value) !== null && _a !== void 0 ? _a : JSON.stringify(e); })\n                    .join(), \")\")\n            : 'id', \" as fallback.\")) || this;\n        _this.descriptor = descriptor;\n        return _this;\n    }\n    return MissingTranslationError;\n}(IntlError));\nexport { MissingTranslationError };\n", "import { __assign, __spreadArray } from \"tslib\";\nimport { IntlMessageFormat } from 'intl-messageformat';\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { UnsupportedFormatterError } from './error';\nexport function filterProps(props, allowlist, defaults) {\n    if (defaults === void 0) { defaults = {}; }\n    return allowlist.reduce(function (filtered, name) {\n        if (name in props) {\n            filtered[name] = props[name];\n        }\n        else if (name in defaults) {\n            filtered[name] = defaults[name];\n        }\n        return filtered;\n    }, {});\n}\nvar defaultErrorHandler = function (error) {\n    // @ts-ignore just so we don't need to declare dep on @types/node\n    if (process.env.NODE_ENV !== 'production') {\n        console.error(error);\n    }\n};\nvar defaultWarnHandler = function (warning) {\n    // @ts-ignore just so we don't need to declare dep on @types/node\n    if (process.env.NODE_ENV !== 'production') {\n        console.warn(warning);\n    }\n};\nexport var DEFAULT_INTL_CONFIG = {\n    formats: {},\n    messages: {},\n    timeZone: undefined,\n    defaultLocale: 'en',\n    defaultFormats: {},\n    fallbackOnEmptyString: true,\n    onError: defaultErrorHandler,\n    onWarn: defaultWarnHandler,\n};\nexport function createIntlCache() {\n    return {\n        dateTime: {},\n        number: {},\n        message: {},\n        relativeTime: {},\n        pluralRules: {},\n        list: {},\n        displayNames: {},\n    };\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\n/**\n * Create intl formatters and populate cache\n * @param cache explicit cache to prevent leaking memory\n */\nexport function createFormatters(cache) {\n    if (cache === void 0) { cache = createIntlCache(); }\n    var RelativeTimeFormat = Intl.RelativeTimeFormat;\n    var ListFormat = Intl.ListFormat;\n    var DisplayNames = Intl.DisplayNames;\n    var getDateTimeFormat = memoize(function () {\n        var _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n    }, {\n        cache: createFastMemoizeCache(cache.dateTime),\n        strategy: strategies.variadic,\n    });\n    var getNumberFormat = memoize(function () {\n        var _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n    }, {\n        cache: createFastMemoizeCache(cache.number),\n        strategy: strategies.variadic,\n    });\n    var getPluralRules = memoize(function () {\n        var _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n    }, {\n        cache: createFastMemoizeCache(cache.pluralRules),\n        strategy: strategies.variadic,\n    });\n    return {\n        getDateTimeFormat: getDateTimeFormat,\n        getNumberFormat: getNumberFormat,\n        getMessageFormat: memoize(function (message, locales, overrideFormats, opts) {\n            return new IntlMessageFormat(message, locales, overrideFormats, __assign({ formatters: {\n                    getNumberFormat: getNumberFormat,\n                    getDateTimeFormat: getDateTimeFormat,\n                    getPluralRules: getPluralRules,\n                } }, (opts || {})));\n        }, {\n            cache: createFastMemoizeCache(cache.message),\n            strategy: strategies.variadic,\n        }),\n        getRelativeTimeFormat: memoize(function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (RelativeTimeFormat.bind.apply(RelativeTimeFormat, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.relativeTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: getPluralRules,\n        getListFormat: memoize(function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (ListFormat.bind.apply(ListFormat, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.list),\n            strategy: strategies.variadic,\n        }),\n        getDisplayNames: memoize(function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (DisplayNames.bind.apply(DisplayNames, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.displayNames),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nexport function getNamedFormat(formats, type, name, onError) {\n    var formatType = formats && formats[type];\n    var format;\n    if (formatType) {\n        format = formatType[name];\n    }\n    if (format) {\n        return format;\n    }\n    onError(new UnsupportedFormatterError(\"No \".concat(type, \" format named: \").concat(name)));\n}\n", "import { __assign } from \"tslib\";\nimport { invariant } from '@formatjs/ecma402-abstract';\nimport { IntlMessageFormat, } from 'intl-messageformat';\nimport { MissingTranslationError, MessageFormatError } from './error';\nimport { TYPE } from '@formatjs/icu-messageformat-parser';\nfunction setTimeZoneInOptions(opts, timeZone) {\n    return Object.keys(opts).reduce(function (all, k) {\n        all[k] = __assign({ timeZone: timeZone }, opts[k]);\n        return all;\n    }, {});\n}\nfunction deepMergeOptions(opts1, opts2) {\n    var keys = Object.keys(__assign(__assign({}, opts1), opts2));\n    return keys.reduce(function (all, k) {\n        all[k] = __assign(__assign({}, (opts1[k] || {})), (opts2[k] || {}));\n        return all;\n    }, {});\n}\nfunction deepMergeFormatsAndSetTimeZone(f1, timeZone) {\n    if (!timeZone) {\n        return f1;\n    }\n    var mfFormats = IntlMessageFormat.formats;\n    return __assign(__assign(__assign({}, mfFormats), f1), { date: deepMergeOptions(setTimeZoneInOptions(mfFormats.date, timeZone), setTimeZoneInOptions(f1.date || {}, timeZone)), time: deepMergeOptions(setTimeZoneInOptions(mfFormats.time, timeZone), setTimeZoneInOptions(f1.time || {}, timeZone)) });\n}\nexport var formatMessage = function (_a, state, messageDescriptor, values, opts) {\n    var locale = _a.locale, formats = _a.formats, messages = _a.messages, defaultLocale = _a.defaultLocale, defaultFormats = _a.defaultFormats, fallbackOnEmptyString = _a.fallbackOnEmptyString, onError = _a.onError, timeZone = _a.timeZone, defaultRichTextElements = _a.defaultRichTextElements;\n    if (messageDescriptor === void 0) { messageDescriptor = { id: '' }; }\n    var msgId = messageDescriptor.id, defaultMessage = messageDescriptor.defaultMessage;\n    // `id` is a required field of a Message Descriptor.\n    invariant(!!msgId, \"[@formatjs/intl] An `id` must be provided to format a message. You can either:\\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\\nto autofix this issue\");\n    var id = String(msgId);\n    var message = \n    // In case messages is Object.create(null)\n    // e.g import('foo.json') from webpack)\n    // See https://github.com/formatjs/formatjs/issues/1914\n    messages &&\n        Object.prototype.hasOwnProperty.call(messages, id) &&\n        messages[id];\n    // IMPORTANT: Hot path if `message` is AST with a single literal node\n    if (Array.isArray(message) &&\n        message.length === 1 &&\n        message[0].type === TYPE.literal) {\n        return message[0].value;\n    }\n    // IMPORTANT: Hot path straight lookup for performance\n    if (!values &&\n        message &&\n        typeof message === 'string' &&\n        !defaultRichTextElements) {\n        return message.replace(/'\\{(.*?)\\}'/gi, \"{$1}\");\n    }\n    values = __assign(__assign({}, defaultRichTextElements), (values || {}));\n    formats = deepMergeFormatsAndSetTimeZone(formats, timeZone);\n    defaultFormats = deepMergeFormatsAndSetTimeZone(defaultFormats, timeZone);\n    if (!message) {\n        if (fallbackOnEmptyString === false && message === '') {\n            return message;\n        }\n        if (!defaultMessage ||\n            (locale && locale.toLowerCase() !== defaultLocale.toLowerCase())) {\n            // This prevents warnings from littering the console in development\n            // when no `messages` are passed into the <IntlProvider> for the\n            // default locale.\n            onError(new MissingTranslationError(messageDescriptor, locale));\n        }\n        if (defaultMessage) {\n            try {\n                var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);\n                return formatter.format(values);\n            }\n            catch (e) {\n                onError(new MessageFormatError(\"Error formatting default message for: \\\"\".concat(id, \"\\\", rendering default message verbatim\"), locale, messageDescriptor, e));\n                return typeof defaultMessage === 'string' ? defaultMessage : id;\n            }\n        }\n        return id;\n    }\n    // We have the translated message\n    try {\n        var formatter = state.getMessageFormat(message, locale, formats, __assign({ formatters: state }, (opts || {})));\n        return formatter.format(values);\n    }\n    catch (e) {\n        onError(new MessageFormatError(\"Error formatting message: \\\"\".concat(id, \"\\\", using \").concat(defaultMessage ? 'default message' : 'id', \" as fallback.\"), locale, messageDescriptor, e));\n    }\n    if (defaultMessage) {\n        try {\n            var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);\n            return formatter.format(values);\n        }\n        catch (e) {\n            onError(new MessageFormatError(\"Error formatting the default message for: \\\"\".concat(id, \"\\\", rendering message verbatim\"), locale, messageDescriptor, e));\n        }\n    }\n    if (typeof message === 'string') {\n        return message;\n    }\n    if (typeof defaultMessage === 'string') {\n        return defaultMessage;\n    }\n    return id;\n};\n", "import { __assign } from \"tslib\";\nimport { filterProps, getNamedFormat } from './utils';\nimport { IntlFormatError } from './error';\nvar DATE_TIME_FORMAT_OPTIONS = [\n    'formatMatcher',\n    'timeZone',\n    'hour12',\n    'weekday',\n    'era',\n    'year',\n    'month',\n    'day',\n    'hour',\n    'minute',\n    'second',\n    'timeZoneName',\n    'hourCycle',\n    'dateStyle',\n    'timeStyle',\n    'calendar',\n    // 'dayPeriod',\n    'numberingSystem',\n    'fractionalSecondDigits',\n];\nexport function getFormatter(_a, type, getDateTimeFormat, options) {\n    var locale = _a.locale, formats = _a.formats, onError = _a.onError, timeZone = _a.timeZone;\n    if (options === void 0) { options = {}; }\n    var format = options.format;\n    var defaults = __assign(__assign({}, (timeZone && { timeZone: timeZone })), (format && getNamedFormat(formats, type, format, onError)));\n    var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, defaults);\n    if (type === 'time' &&\n        !filteredOptions.hour &&\n        !filteredOptions.minute &&\n        !filteredOptions.second &&\n        !filteredOptions.timeStyle &&\n        !filteredOptions.dateStyle) {\n        // Add default formatting options if hour, minute, or second isn't defined.\n        filteredOptions = __assign(__assign({}, filteredOptions), { hour: 'numeric', minute: 'numeric' });\n    }\n    return getDateTimeFormat(locale, filteredOptions);\n}\nexport function formatDate(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'date', getDateTimeFormat, options).format(date);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting date.', config.locale, e));\n    }\n    return String(date);\n}\nexport function formatTime(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'time', getDateTimeFormat, options).format(date);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting time.', config.locale, e));\n    }\n    return String(date);\n}\nexport function formatDateTimeRange(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var from = _a[0], to = _a[1], _b = _a[2], options = _b === void 0 ? {} : _b;\n    var timeZone = config.timeZone, locale = config.locale, onError = config.onError;\n    var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, timeZone ? { timeZone: timeZone } : {});\n    try {\n        return getDateTimeFormat(locale, filteredOptions).formatRange(from, to);\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting date time range.', config.locale, e));\n    }\n    return String(from);\n}\nexport function formatDateToParts(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'date', getDateTimeFormat, options).formatToParts(date); // TODO: remove this when https://github.com/microsoft/TypeScript/pull/50402 is merged\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting date.', config.locale, e));\n    }\n    return [];\n}\nexport function formatTimeToParts(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'time', getDateTimeFormat, options).formatToParts(date); // TODO: remove this when https://github.com/microsoft/TypeScript/pull/50402 is merged\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting time.', config.locale, e));\n    }\n    return [];\n}\n", "import { filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar DISPLAY_NAMES_OPTONS = [\n    'style',\n    'type',\n    'fallback',\n    'languageDisplay',\n];\nexport function formatDisplayName(_a, getDisplayNames, value, options) {\n    var locale = _a.locale, onError = _a.onError;\n    var DisplayNames = Intl.DisplayNames;\n    if (!DisplayNames) {\n        onError(new FormatError(\"Intl.DisplayNames is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-displaynames\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    var filteredOptions = filterProps(options, DISPLAY_NAMES_OPTONS);\n    try {\n        return getDisplayNames(locale, filteredOptions).of(value);\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting display name.', locale, e));\n    }\n}\n", "import { __assign } from \"tslib\";\nimport { filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar LIST_FORMAT_OPTIONS = [\n    'type',\n    'style',\n];\nvar now = Date.now();\nfunction generateToken(i) {\n    return \"\".concat(now, \"_\").concat(i, \"_\").concat(now);\n}\nexport function formatList(opts, getListFormat, values, options) {\n    if (options === void 0) { options = {}; }\n    var results = formatListToParts(opts, getListFormat, values, options).reduce(function (all, el) {\n        var val = el.value;\n        if (typeof val !== 'string') {\n            all.push(val);\n        }\n        else if (typeof all[all.length - 1] === 'string') {\n            all[all.length - 1] += val;\n        }\n        else {\n            all.push(val);\n        }\n        return all;\n    }, []);\n    return results.length === 1 ? results[0] : results.length === 0 ? '' : results;\n}\nexport function formatListToParts(_a, getListFormat, values, options) {\n    var locale = _a.locale, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    var ListFormat = Intl.ListFormat;\n    if (!ListFormat) {\n        onError(new FormatError(\"Intl.ListFormat is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-listformat\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    var filteredOptions = filterProps(options, LIST_FORMAT_OPTIONS);\n    try {\n        var richValues_1 = {};\n        var serializedValues = values.map(function (v, i) {\n            if (typeof v === 'object') {\n                var id = generateToken(i);\n                richValues_1[id] = v;\n                return id;\n            }\n            return String(v);\n        });\n        return getListFormat(locale, filteredOptions)\n            .formatToParts(serializedValues)\n            .map(function (part) {\n            return part.type === 'literal'\n                ? part\n                : __assign(__assign({}, part), { value: richValues_1[part.value] || part.value });\n        });\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting list.', locale, e));\n    }\n    // @ts-ignore\n    return values;\n}\n", "import { filterProps } from './utils';\nimport { IntlFormatError } from './error';\nimport { ErrorCode, FormatError } from 'intl-messageformat';\nvar PLURAL_FORMAT_OPTIONS = ['type'];\nexport function formatPlural(_a, getPluralRules, value, options) {\n    var locale = _a.locale, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    if (!Intl.PluralRules) {\n        onError(new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    var filteredOptions = filterProps(options, PLURAL_FORMAT_OPTIONS);\n    try {\n        return getPluralRules(locale, filteredOptions).select(value);\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting plural.', locale, e));\n    }\n    return 'other';\n}\n", "import { getNamedFormat, filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar RELATIVE_TIME_FORMAT_OPTIONS = ['numeric', 'style'];\nfunction getFormatter(_a, getRelativeTimeFormat, options) {\n    var locale = _a.locale, formats = _a.formats, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    var format = options.format;\n    var defaults = (!!format && getNamedFormat(formats, 'relative', format, onError)) || {};\n    var filteredOptions = filterProps(options, RELATIVE_TIME_FORMAT_OPTIONS, defaults);\n    return getRelativeTimeFormat(locale, filteredOptions);\n}\nexport function formatRelativeTime(config, getRelativeTimeFormat, value, unit, options) {\n    if (options === void 0) { options = {}; }\n    if (!unit) {\n        unit = 'second';\n    }\n    var RelativeTimeFormat = Intl.RelativeTimeFormat;\n    if (!RelativeTimeFormat) {\n        config.onError(new FormatError(\"Intl.RelativeTimeFormat is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-relativetimeformat\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    try {\n        return getFormatter(config, getRelativeTimeFormat, options).format(value, unit);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting relative time.', config.locale, e));\n    }\n    return String(value);\n}\n", "import { IntlFormatError } from './error';\nimport { filterProps, getNamedFormat } from './utils';\nvar NUMBER_FORMAT_OPTIONS = [\n    'style',\n    'currency',\n    'unit',\n    'unitDisplay',\n    'useGrouping',\n    'minimumIntegerDigits',\n    'minimumFractionDigits',\n    'maximumFractionDigits',\n    'minimumSignificantDigits',\n    'maximumSignificantDigits',\n    // ES2020 NumberFormat\n    'compactDisplay',\n    'currencyDisplay',\n    'currencySign',\n    'notation',\n    'signDisplay',\n    'unit',\n    'unitDisplay',\n    'numberingSystem',\n    // ES2023 NumberFormat\n    'trailingZeroDisplay',\n    'roundingPriority',\n    'roundingIncrement',\n    'roundingMode',\n];\nexport function getFormatter(_a, getNumberFormat, options) {\n    var locale = _a.locale, formats = _a.formats, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    var format = options.format;\n    var defaults = ((format &&\n        getNamedFormat(formats, 'number', format, onError)) ||\n        {});\n    var filteredOptions = filterProps(options, NUMBER_FORMAT_OPTIONS, defaults);\n    return getNumberFormat(locale, filteredOptions);\n}\nexport function formatNumber(config, getNumberFormat, value, options) {\n    if (options === void 0) { options = {}; }\n    try {\n        return getFormatter(config, getNumberFormat, options).format(value);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting number.', config.locale, e));\n    }\n    return String(value);\n}\nexport function formatNumberToParts(config, getNumberFormat, value, options) {\n    if (options === void 0) { options = {}; }\n    try {\n        return getFormatter(config, getNumberFormat, options).formatToParts(value);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting number.', config.locale, e));\n    }\n    return [];\n}\n", "import { __assign } from \"tslib\";\nimport { createFormatters, DEFAULT_INTL_CONFIG } from './utils';\nimport { InvalidConfigError, MissingDataError } from './error';\nimport { formatNumber, formatNumberToParts } from './number';\nimport { formatRelativeTime } from './relativeTime';\nimport { formatDate, formatDateToParts, formatTime, formatTimeToParts, formatDateTimeRange, } from './dateTime';\nimport { formatPlural } from './plural';\nimport { formatMessage } from './message';\nimport { formatList, formatListToParts } from './list';\nimport { formatDisplayName } from './displayName';\nfunction messagesContainString(messages) {\n    var firstMessage = messages ? messages[Object.keys(messages)[0]] : undefined;\n    return typeof firstMessage === 'string';\n}\nfunction verifyConfigMessages(config) {\n    if (config.onWarn &&\n        config.defaultRichTextElements &&\n        messagesContainString(config.messages || {})) {\n        config.onWarn(\"[@formatjs/intl] \\\"defaultRichTextElements\\\" was specified but \\\"message\\\" was not pre-compiled. \\nPlease consider using \\\"@formatjs/cli\\\" to pre-compile your messages for performance.\\nFor more details see https://formatjs.io/docs/getting-started/message-distribution\");\n    }\n}\n/**\n * Create intl object\n * @param config intl config\n * @param cache cache for formatter instances to prevent memory leak\n */\nexport function createIntl(config, cache) {\n    var formatters = createFormatters(cache);\n    var resolvedConfig = __assign(__assign({}, DEFAULT_INTL_CONFIG), config);\n    var locale = resolvedConfig.locale, defaultLocale = resolvedConfig.defaultLocale, onError = resolvedConfig.onError;\n    if (!locale) {\n        if (onError) {\n            onError(new InvalidConfigError(\"\\\"locale\\\" was not configured, using \\\"\".concat(defaultLocale, \"\\\" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details\")));\n        }\n        // Since there's no registered locale data for `locale`, this will\n        // fallback to the `defaultLocale` to make sure things can render.\n        // The `messages` are overridden to the `defaultProps` empty object\n        // to maintain referential equality across re-renders. It's assumed\n        // each <FormattedMessage> contains a `defaultMessage` prop.\n        resolvedConfig.locale = resolvedConfig.defaultLocale || 'en';\n    }\n    else if (!Intl.NumberFormat.supportedLocalesOf(locale).length && onError) {\n        onError(new MissingDataError(\"Missing locale data for locale: \\\"\".concat(locale, \"\\\" in Intl.NumberFormat. Using default locale: \\\"\").concat(defaultLocale, \"\\\" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details\")));\n    }\n    else if (!Intl.DateTimeFormat.supportedLocalesOf(locale).length &&\n        onError) {\n        onError(new MissingDataError(\"Missing locale data for locale: \\\"\".concat(locale, \"\\\" in Intl.DateTimeFormat. Using default locale: \\\"\").concat(defaultLocale, \"\\\" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details\")));\n    }\n    verifyConfigMessages(resolvedConfig);\n    return __assign(__assign({}, resolvedConfig), { formatters: formatters, formatNumber: formatNumber.bind(null, resolvedConfig, formatters.getNumberFormat), formatNumberToParts: formatNumberToParts.bind(null, resolvedConfig, formatters.getNumberFormat), formatRelativeTime: formatRelativeTime.bind(null, resolvedConfig, formatters.getRelativeTimeFormat), formatDate: formatDate.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatDateToParts: formatDateToParts.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatTime: formatTime.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatDateTimeRange: formatDateTimeRange.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatTimeToParts: formatTimeToParts.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatPlural: formatPlural.bind(null, resolvedConfig, formatters.getPluralRules), \n        // @ts-expect-error TODO: will get to this later\n        formatMessage: formatMessage.bind(null, resolvedConfig, formatters), \n        // @ts-expect-error TODO: will get to this later\n        $t: formatMessage.bind(null, resolvedConfig, formatters), formatList: formatList.bind(null, resolvedConfig, formatters.getListFormat), formatListToParts: formatListToParts.bind(null, resolvedConfig, formatters.getListFormat), formatDisplayName: formatDisplayName.bind(null, resolvedConfig, formatters.getDisplayNames) });\n}\n", "import { __assign } from \"tslib\";\nimport hoistNonReactStatics from 'hoist-non-react-statics';\nimport * as React from 'react';\nimport { invariantIntlContext } from '../utils';\nfunction getDisplayName(Component) {\n    return Component.displayName || Component.name || 'Component';\n}\n// This is primarily dealing with packaging systems where multiple copies of react-intl\n// might exist\nvar IntlContext = typeof window !== 'undefined' && !window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__\n    ? window.__REACT_INTL_CONTEXT__ ||\n        (window.__REACT_INTL_CONTEXT__ = React.createContext(null))\n    : React.createContext(null);\nvar IntlConsumer = IntlContext.Consumer, IntlProvider = IntlContext.Provider;\nexport var Provider = IntlProvider;\nexport var Context = IntlContext;\nexport default function injectIntl(WrappedComponent, options) {\n    var _a = options || {}, _b = _a.intlPropName, intlPropName = _b === void 0 ? 'intl' : _b, _c = _a.forwardRef, forwardRef = _c === void 0 ? false : _c, _d = _a.enforceContext, enforceContext = _d === void 0 ? true : _d;\n    var WithIntl = function (props) { return (React.createElement(IntlConsumer, null, function (intl) {\n        var _a;\n        if (enforceContext) {\n            invariantIntlContext(intl);\n        }\n        var intlProp = (_a = {}, _a[intlPropName] = intl, _a);\n        return (React.createElement(WrappedComponent, __assign({}, props, intlProp, { ref: forwardRef ? props.forwardedRef : null })));\n    })); };\n    WithIntl.displayName = \"injectIntl(\".concat(getDisplayName(WrappedComponent), \")\");\n    WithIntl.WrappedComponent = WrappedComponent;\n    if (forwardRef) {\n        return hoistNonReactStatics(\n        // @ts-expect-error\n        React.forwardRef(function (props, ref) { return (React.createElement(WithIntl, __assign({}, props, { forwardedRef: ref }))); }), WrappedComponent);\n    }\n    return hoistNonReactStatics(WithIntl, WrappedComponent);\n}\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport { __extends } from \"tslib\";\nimport { createIntlCache } from '@formatjs/intl';\nimport * as React from 'react';\nimport { DEFAULT_INTL_CONFIG, invariantIntlContext, shallowEqual } from '../utils';\nimport { Provider } from './injectIntl';\nimport { createIntl } from './createIntl';\nfunction processIntlConfig(config) {\n    return {\n        locale: config.locale,\n        timeZone: config.timeZone,\n        fallbackOnEmptyString: config.fallbackOnEmptyString,\n        formats: config.formats,\n        textComponent: config.textComponent,\n        messages: config.messages,\n        defaultLocale: config.defaultLocale,\n        defaultFormats: config.defaultFormats,\n        onError: config.onError,\n        onWarn: config.onWarn,\n        wrapRichTextChunksInFragment: config.wrapRichTextChunksInFragment,\n        defaultRichTextElements: config.defaultRichTextElements,\n    };\n}\nvar IntlProvider = /** @class */ (function (_super) {\n    __extends(IntlProvider, _super);\n    function IntlProvider() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.cache = createIntlCache();\n        _this.state = {\n            cache: _this.cache,\n            intl: createIntl(processIntlConfig(_this.props), _this.cache),\n            prevConfig: processIntlConfig(_this.props),\n        };\n        return _this;\n    }\n    IntlProvider.getDerivedStateFromProps = function (props, _a) {\n        var prevConfig = _a.prevConfig, cache = _a.cache;\n        var config = processIntlConfig(props);\n        if (!shallowEqual(prevConfig, config)) {\n            return {\n                intl: createIntl(config, cache),\n                prevConfig: config,\n            };\n        }\n        return null;\n    };\n    IntlProvider.prototype.render = function () {\n        invariantIntlContext(this.state.intl);\n        return React.createElement(Provider, { value: this.state.intl }, this.props.children);\n    };\n    IntlProvider.displayName = 'IntlProvider';\n    IntlProvider.defaultProps = DEFAULT_INTL_CONFIG;\n    return IntlProvider;\n}(React.PureComponent));\nexport default IntlProvider;\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { createIntl as coreCreateIntl, formatMessage as coreFormatMessage, } from '@formatjs/intl';\nimport * as React from 'react';\nimport { DEFAULT_INTL_CONFIG, assignUniqueKeysToParts } from '../utils';\nimport { isFormatXMLElementFn, } from 'intl-messageformat';\nfunction assignUniqueKeysToFormatXMLElementFnArgument(values) {\n    if (!values) {\n        return values;\n    }\n    return Object.keys(values).reduce(function (acc, k) {\n        var v = values[k];\n        acc[k] = isFormatXMLElementFn(v)\n            ? assignUniqueKeysToParts(v)\n            : v;\n        return acc;\n    }, {});\n}\nvar formatMessage = function (config, formatters, descriptor, rawValues) {\n    var rest = [];\n    for (var _i = 4; _i < arguments.length; _i++) {\n        rest[_i - 4] = arguments[_i];\n    }\n    var values = assignUniqueKeysToFormatXMLElementFnArgument(rawValues);\n    var chunks = coreFormatMessage.apply(void 0, __spreadArray([config,\n        formatters,\n        descriptor,\n        values], rest, false));\n    if (Array.isArray(chunks)) {\n        return React.Children.toArray(chunks);\n    }\n    return chunks;\n};\n/**\n * Create intl object\n * @param config intl config\n * @param cache cache for formatter instances to prevent memory leak\n */\nexport var createIntl = function (_a, cache) {\n    var rawDefaultRichTextElements = _a.defaultRichTextElements, config = __rest(_a, [\"defaultRichTextElements\"]);\n    var defaultRichTextElements = assignUniqueKeysToFormatXMLElementFnArgument(rawDefaultRichTextElements);\n    var coreIntl = coreCreateIntl(__assign(__assign(__assign({}, DEFAULT_INTL_CONFIG), config), { defaultRichTextElements: defaultRichTextElements }), cache);\n    var resolvedConfig = {\n        locale: coreIntl.locale,\n        timeZone: coreIntl.timeZone,\n        fallbackOnEmptyString: coreIntl.fallbackOnEmptyString,\n        formats: coreIntl.formats,\n        defaultLocale: coreIntl.defaultLocale,\n        defaultFormats: coreIntl.defaultFormats,\n        messages: coreIntl.messages,\n        onError: coreIntl.onError,\n        defaultRichTextElements: defaultRichTextElements,\n    };\n    return __assign(__assign({}, coreIntl), { formatMessage: formatMessage.bind(null, resolvedConfig, \n        // @ts-expect-error fix this\n        coreIntl.formatters), \n        // @ts-expect-error fix this\n        $t: formatMessage.bind(null, resolvedConfig, coreIntl.formatters) });\n};\n", "import { __assign, __rest } from \"tslib\";\n/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport * as React from 'react';\nimport { invariant, } from '@formatjs/ecma402-abstract';\nimport useIntl from './useIntl';\nvar MINUTE = 60;\nvar HOUR = 60 * 60;\nvar DAY = 60 * 60 * 24;\nfunction selectUnit(seconds) {\n    var absValue = Math.abs(seconds);\n    if (absValue < MINUTE) {\n        return 'second';\n    }\n    if (absValue < HOUR) {\n        return 'minute';\n    }\n    if (absValue < DAY) {\n        return 'hour';\n    }\n    return 'day';\n}\nfunction getDurationInSeconds(unit) {\n    switch (unit) {\n        case 'second':\n            return 1;\n        case 'minute':\n            return MINUTE;\n        case 'hour':\n            return HOUR;\n        default:\n            return DAY;\n    }\n}\nfunction valueToSeconds(value, unit) {\n    if (!value) {\n        return 0;\n    }\n    switch (unit) {\n        case 'second':\n            return value;\n        case 'minute':\n            return value * MINUTE;\n        default:\n            return value * HOUR;\n    }\n}\nvar INCREMENTABLE_UNITS = [\n    'second',\n    'minute',\n    'hour',\n];\nfunction canIncrement(unit) {\n    if (unit === void 0) { unit = 'second'; }\n    return INCREMENTABLE_UNITS.indexOf(unit) > -1;\n}\nvar SimpleFormattedRelativeTime = function (props) {\n    var _a = useIntl(), formatRelativeTime = _a.formatRelativeTime, Text = _a.textComponent;\n    var children = props.children, value = props.value, unit = props.unit, otherProps = __rest(props, [\"children\", \"value\", \"unit\"]);\n    var formattedRelativeTime = formatRelativeTime(value || 0, unit, otherProps);\n    if (typeof children === 'function') {\n        return children(formattedRelativeTime);\n    }\n    if (Text) {\n        return React.createElement(Text, null, formattedRelativeTime);\n    }\n    return React.createElement(React.Fragment, null, formattedRelativeTime);\n};\nvar FormattedRelativeTime = function (_a) {\n    var _b = _a.value, value = _b === void 0 ? 0 : _b, _c = _a.unit, unit = _c === void 0 ? 'second' : _c, updateIntervalInSeconds = _a.updateIntervalInSeconds, otherProps = __rest(_a, [\"value\", \"unit\", \"updateIntervalInSeconds\"]);\n    invariant(!updateIntervalInSeconds ||\n        !!(updateIntervalInSeconds && canIncrement(unit)), 'Cannot schedule update with unit longer than hour');\n    var _d = React.useState(), prevUnit = _d[0], setPrevUnit = _d[1];\n    var _e = React.useState(0), prevValue = _e[0], setPrevValue = _e[1];\n    var _f = React.useState(0), currentValueInSeconds = _f[0], setCurrentValueInSeconds = _f[1];\n    var updateTimer;\n    if (unit !== prevUnit || value !== prevValue) {\n        setPrevValue(value || 0);\n        setPrevUnit(unit);\n        setCurrentValueInSeconds(canIncrement(unit) ? valueToSeconds(value, unit) : 0);\n    }\n    React.useEffect(function () {\n        function clearUpdateTimer() {\n            clearTimeout(updateTimer);\n        }\n        clearUpdateTimer();\n        // If there's no interval and we cannot increment this unit, do nothing\n        if (!updateIntervalInSeconds || !canIncrement(unit)) {\n            return clearUpdateTimer;\n        }\n        // Figure out the next interesting time\n        var nextValueInSeconds = currentValueInSeconds - updateIntervalInSeconds;\n        var nextUnit = selectUnit(nextValueInSeconds);\n        // We've reached the max auto incrementable unit, don't schedule another update\n        if (nextUnit === 'day') {\n            return clearUpdateTimer;\n        }\n        var unitDuration = getDurationInSeconds(nextUnit);\n        var remainder = nextValueInSeconds % unitDuration;\n        var prevInterestingValueInSeconds = nextValueInSeconds - remainder;\n        var nextInterestingValueInSeconds = prevInterestingValueInSeconds >= currentValueInSeconds\n            ? prevInterestingValueInSeconds - unitDuration\n            : prevInterestingValueInSeconds;\n        var delayInSeconds = Math.abs(nextInterestingValueInSeconds - currentValueInSeconds);\n        if (currentValueInSeconds !== nextInterestingValueInSeconds) {\n            updateTimer = setTimeout(function () { return setCurrentValueInSeconds(nextInterestingValueInSeconds); }, delayInSeconds * 1e3);\n        }\n        return clearUpdateTimer;\n    }, [currentValueInSeconds, updateIntervalInSeconds, unit]);\n    var currentValue = value || 0;\n    var currentUnit = unit;\n    if (canIncrement(unit) &&\n        typeof currentValueInSeconds === 'number' &&\n        updateIntervalInSeconds) {\n        currentUnit = selectUnit(currentValueInSeconds);\n        var unitDuration = getDurationInSeconds(currentUnit);\n        currentValue = Math.round(currentValueInSeconds / unitDuration);\n    }\n    return (React.createElement(SimpleFormattedRelativeTime, __assign({ value: currentValue, unit: currentUnit }, otherProps)));\n};\nFormattedRelativeTime.displayName = 'FormattedRelativeTime';\nexport default FormattedRelativeTime;\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar FormattedPlural = function (props) {\n    var _a = useIntl(), formatPlural = _a.formatPlural, Text = _a.textComponent;\n    var value = props.value, other = props.other, children = props.children;\n    var pluralCategory = formatPlural(value, props);\n    var formattedPlural = props[pluralCategory] || other;\n    if (typeof children === 'function') {\n        return children(formattedPlural);\n    }\n    if (Text) {\n        return React.createElement(Text, null, formattedPlural);\n    }\n    // Work around @types/react where React.FC cannot return string\n    return formattedPlural;\n};\nFormattedPlural.displayName = 'FormattedPlural';\nexport default FormattedPlural;\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport { __rest } from \"tslib\";\nimport * as React from 'react';\nimport { shallowEqual } from '../utils';\nimport useIntl from './useIntl';\nfunction areEqual(prevProps, nextProps) {\n    var values = prevProps.values, otherProps = __rest(prevProps, [\"values\"]);\n    var nextValues = nextProps.values, nextOtherProps = __rest(nextProps, [\"values\"]);\n    return (shallowEqual(nextValues, values) &&\n        shallowEqual(otherProps, nextOtherProps));\n}\nfunction FormattedMessage(props) {\n    var intl = useIntl();\n    var formatMessage = intl.formatMessage, _a = intl.textComponent, Text = _a === void 0 ? React.Fragment : _a;\n    var id = props.id, description = props.description, defaultMessage = props.defaultMessage, values = props.values, children = props.children, _b = props.tagName, Component = _b === void 0 ? Text : _b, ignoreTag = props.ignoreTag;\n    var descriptor = { id: id, description: description, defaultMessage: defaultMessage };\n    var nodes = formatMessage(descriptor, values, {\n        ignoreTag: ignoreTag,\n    });\n    if (typeof children === 'function') {\n        return children(Array.isArray(nodes) ? nodes : [nodes]);\n    }\n    if (Component) {\n        return React.createElement(Component, null, React.Children.toArray(nodes));\n    }\n    return React.createElement(React.Fragment, null, nodes);\n}\nFormattedMessage.displayName = 'FormattedMessage';\nvar MemoizedFormattedMessage = React.memo(FormattedMessage, areEqual);\nMemoizedFormattedMessage.displayName = 'MemoizedFormattedMessage';\nexport default MemoizedFormattedMessage;\n", "import { __rest } from \"tslib\";\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar FormattedDateTimeRange = function (props) {\n    var intl = useIntl();\n    var from = props.from, to = props.to, children = props.children, formatProps = __rest(props, [\"from\", \"to\", \"children\"]);\n    var formattedValue = intl.formatDateTimeRange(from, to, formatProps);\n    if (typeof children === 'function') {\n        return children(formattedValue);\n    }\n    var Text = intl.textComponent || React.Fragment;\n    return React.createElement(Text, null, formattedValue);\n};\nFormattedDateTimeRange.displayName = 'FormattedDateTimeRange';\nexport default FormattedDateTimeRange;\n", "import { createFormattedComponent, createFormattedDateTimePartsComponent, } from './src/components/createFormattedComponent';\nimport injectIntl, { Provider as RawIntlProvider, Context as IntlContext, } from './src/components/injectIntl';\nimport useIntl from './src/components/useIntl';\nimport IntlProvider from './src/components/provider';\nimport { createIntl } from './src/components/createIntl';\nimport FormattedRelativeTime from './src/components/relative';\nimport FormattedPlural from './src/components/plural';\nimport FormattedMessage from './src/components/message';\nimport FormattedDateTimeRange from './src/components/dateTimeRange';\nexport { FormattedDateTimeRange, FormattedMessage, FormattedPlural, FormattedRelativeTime, IntlContext, IntlProvider, RawIntlProvider, createIntl, injectIntl, useIntl, };\nexport { createIntlCache, UnsupportedFormatterError, InvalidConfigError, MissingDataError, MessageFormatError, MissingTranslationError, IntlErrorCode as ReactIntlErrorCode, IntlError as ReactIntlError, } from '@formatjs/intl';\nexport function defineMessages(msgs) {\n    return msgs;\n}\nexport function defineMessage(msg) {\n    return msg;\n}\n// IMPORTANT: Explicit here to prevent api-extractor from outputing `import('./src/types').CustomFormatConfig`\nexport var FormattedDate = createFormattedComponent('formatDate');\nexport var FormattedTime = createFormattedComponent('formatTime');\nexport var FormattedNumber = createFormattedComponent('formatNumber');\nexport var FormattedList = createFormattedComponent('formatList');\nexport var FormattedDisplayName = createFormattedComponent('formatDisplayName');\nexport var FormattedDateParts = createFormattedDateTimePartsComponent('formatDate');\nexport var FormattedTimeParts = createFormattedDateTimePartsComponent('formatTime');\nexport { FormattedNumberParts, FormattedListParts, } from './src/components/createFormattedComponent';\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,UAAU;AAMd,QAAI,gBAAgB;AAAA,MAClB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,0BAA0B;AAAA,MAC1B,0BAA0B;AAAA,MAC1B,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AACA,QAAI,gBAAgB;AAAA,MAClB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AACA,QAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AACA,QAAI,eAAe;AAAA,MACjB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AACA,QAAI,eAAe,CAAC;AACpB,iBAAa,QAAQ,UAAU,IAAI;AACnC,iBAAa,QAAQ,IAAI,IAAI;AAE7B,aAAS,WAAW,WAAW;AAE7B,UAAI,QAAQ,OAAO,SAAS,GAAG;AAC7B,eAAO;AAAA,MACT;AAGA,aAAO,aAAa,UAAU,UAAU,CAAC,KAAK;AAAA,IAChD;AAEA,QAAIA,kBAAiB,OAAO;AAC5B,QAAI,sBAAsB,OAAO;AACjC,QAAI,wBAAwB,OAAO;AACnC,QAAI,2BAA2B,OAAO;AACtC,QAAI,iBAAiB,OAAO;AAC5B,QAAI,kBAAkB,OAAO;AAC7B,aAASC,sBAAqB,iBAAiB,iBAAiB,WAAW;AACzE,UAAI,OAAO,oBAAoB,UAAU;AAEvC,YAAI,iBAAiB;AACnB,cAAI,qBAAqB,eAAe,eAAe;AAEvD,cAAI,sBAAsB,uBAAuB,iBAAiB;AAChE,YAAAA,sBAAqB,iBAAiB,oBAAoB,SAAS;AAAA,UACrE;AAAA,QACF;AAEA,YAAI,OAAO,oBAAoB,eAAe;AAE9C,YAAI,uBAAuB;AACzB,iBAAO,KAAK,OAAO,sBAAsB,eAAe,CAAC;AAAA,QAC3D;AAEA,YAAI,gBAAgB,WAAW,eAAe;AAC9C,YAAI,gBAAgB,WAAW,eAAe;AAE9C,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,cAAI,MAAM,KAAK,CAAC;AAEhB,cAAI,CAAC,cAAc,GAAG,KAAK,EAAE,aAAa,UAAU,GAAG,MAAM,EAAE,iBAAiB,cAAc,GAAG,MAAM,EAAE,iBAAiB,cAAc,GAAG,IAAI;AAC7I,gBAAI,aAAa,yBAAyB,iBAAiB,GAAG;AAE9D,gBAAI;AAEF,cAAAD,gBAAe,iBAAiB,KAAK,UAAU;AAAA,YACjD,SAAS,GAAG;AAAA,YAAC;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAUC;AAAA;AAAA;;;ACtFjB,IAAI,gBAAgB,SAAS,GAAG,GAAG;AACjC,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC3B;AAEO,SAAS,UAAU,GAAG,GAAG;AAC9B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACpF;AAEO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAiKO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;AC5NA,IAAAC,SAAuB;;;ACDvB,IAAAC,SAAuB;;;ACCvB,YAAuB;;;ACiSvB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB,gBAAgB;AACpC,IAAI,cAAc,gBAAgB;;;ACnS3B,IAAI,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAGO,SAAS,oBAAoB,MAAM;AACtC,SAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC;AAC3C;AAIO,IAAI,eAAe,iBAAiB,IAAI,mBAAmB;;;ACpB5D,SAAU,QAAwB,IAAO,SAAoB;AACjE,MAAM,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ;AAEzD,MAAM,aACJ,WAAW,QAAQ,aAAa,QAAQ,aAAa;AAEvD,MAAM,WACJ,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAEnD,SAAO,SAAS,IAAI;IAClB;IACA;GACD;AACH;AAMA,SAAS,YAAY,OAAU;AAC7B,SACE,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU;AAEnE;AAUA,SAAS,QAEP,IACA,OACA,YACA,KAAQ;AAER,MAAM,WAAW,YAAY,GAAG,IAAI,MAAM,WAAW,GAAG;AAExD,MAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,MAAI,OAAO,kBAAkB,aAAa;AACxC,oBAAgB,GAAG,KAAK,MAAM,GAAG;AACjC,UAAM,IAAI,UAAU,aAAa;EACnC;AAEA,SAAO;AACT;AAEA,SAAS,SAEP,IACA,OACA,YAAsB;AAEtB,MAAM,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACpD,MAAM,WAAW,WAAW,IAAI;AAEhC,MAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,MAAI,OAAO,kBAAkB,aAAa;AACxC,oBAAgB,GAAG,MAAM,MAAM,IAAI;AACnC,UAAM,IAAI,UAAU,aAAa;EACnC;AAEA,SAAO;AACT;AAEA,SAAS,SACP,IACA,SACA,UACA,OACA,WAAqB;AAErB,SAAO,SAAS,KAAK,SAAS,IAAI,OAAO,SAAS;AACpD;AAEA,SAAS,gBAEP,IACA,SAA2B;AAE3B,MAAM,WAAW,GAAG,WAAW,IAAI,UAAU;AAE7C,SAAO,SACL,IACA,MACA,UACA,QAAQ,MAAM,OAAM,GACpB,QAAQ,UAAU;AAEtB;AAEA,SAAS,iBAEP,IACA,SAA2B;AAE3B,SAAO,SACL,IACA,MACA,UACA,QAAQ,MAAM,OAAM,GACpB,QAAQ,UAAU;AAEtB;AAEA,SAAS,gBAEP,IACA,SAA2B;AAE3B,SAAO,SAAS,IAAI,MAAM,SAAS,QAAQ,MAAM,OAAM,GAAI,QAAQ,UAAU;AAC/E;AAMA,IAAM,oBAAgC,WAAA;AACpC,SAAO,KAAK,UAAU,SAAS;AACjC;AAMA,SAAS,8BAA2B;AAClC,OAAK,QAAQ,uBAAO,OAAO,IAAI;AACjC;AAEA,4BAA4B,UAAU,MAAM,SAAU,KAAW;AAC/D,SAAO,KAAK,MAAM,GAAG;AACvB;AAEA,4BAA4B,UAAU,MAAM,SAC1C,KACA,OAAQ;AAER,OAAK,MAAM,GAAG,IAAI;AACpB;AAEA,IAAM,eAAgC;EACpC,QAAQ,SAAS,SAAM;AAErB,WAAO,IAAI,4BAA2B;EACxC;;AAYK,IAAM,aAA+B;EAC1C,UAAU;EACV,SAAS;;;;AC9GJ,SAAS,UAAU,WAAW,SAAS,KAAK;AAC/C,MAAI,QAAQ,QAAQ;AAAE,UAAM;AAAA,EAAO;AACnC,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,IAAI,OAAO;AAAA,EACzB;AACJ;AACO,IAAI,6BAA6B,QAAQ,WAAY;AACxD,MAAIC;AACJ,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,SAAO,MAAMA,MAAK,KAAK,cAAc,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAC/F,GAAG;AAAA,EACC,UAAU,WAAW;AACzB,CAAC;AACM,IAAI,+BAA+B,QAAQ,WAAY;AAC1D,MAAIA;AACJ,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,SAAO,MAAMA,MAAK,KAAK,gBAAgB,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AACjG,GAAG;AAAA,EACC,UAAU,WAAW;AACzB,CAAC;AACM,IAAI,4BAA4B,QAAQ,WAAY;AACvD,MAAIA;AACJ,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,SAAO,MAAMA,MAAK,KAAK,aAAa,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAC9F,GAAG;AAAA,EACC,UAAU,WAAW;AACzB,CAAC;AACM,IAAI,uBAAuB,QAAQ,WAAY;AAClD,MAAIA;AACJ,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,SAAO,MAAMA,MAAK,KAAK,QAAQ,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AACzF,GAAG;AAAA,EACC,UAAU,WAAW;AACzB,CAAC;AACM,IAAI,2BAA2B,QAAQ,WAAY;AACtD,MAAIA;AACJ,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,SAAO,MAAMA,MAAK,KAAK,YAAY,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAC7F,GAAG;AAAA,EACC,UAAU,WAAW;AACzB,CAAC;;;AC9IM,IAAI,kBAAkB;;;ACK7B,IAAI,wBAAwB,IAAI,OAAO,IAAI,OAAO,gBAAgB,MAAM,CAAC;AAEzE,IAAI,yBAAyB,IAAI,OAAO,GAAG,OAAO,gBAAgB,QAAQ,GAAG,CAAC;;;ACP9E,IAAI;AAAA;AAAA,EAAwC,SAAU,QAAQ;AAC1D,cAAUC,yBAAwB,MAAM;AACxC,aAASA,0BAAyB;AAC9B,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAO;AACb,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;;;ACTA,IAAI;AAAA,CACV,SAAUC,mBAAkB;AACzB,EAAAA,kBAAiB,YAAY,IAAI;AACjC,EAAAA,kBAAiB,QAAQ,IAAI;AAC7B,EAAAA,kBAAiB,UAAU,IAAI;AACnC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;;;ACLvC,IAAI;AAAA,CACV,SAAUC,YAAW;AAElB,EAAAA,WAAUA,WAAU,+BAA+B,IAAI,CAAC,IAAI;AAE5D,EAAAA,WAAUA,WAAU,gBAAgB,IAAI,CAAC,IAAI;AAE7C,EAAAA,WAAUA,WAAU,oBAAoB,IAAI,CAAC,IAAI;AAEjD,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,CAAC,IAAI;AAEnD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,EAAAA,WAAUA,WAAU,yBAAyB,IAAI,CAAC,IAAI;AAEtD,EAAAA,WAAUA,WAAU,4BAA4B,IAAI,CAAC,IAAI;AAEzD,EAAAA,WAAUA,WAAU,wBAAwB,IAAI,CAAC,IAAI;AAErD,EAAAA,WAAUA,WAAU,2BAA2B,IAAI,EAAE,IAAI;AAEzD,EAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAEhE,EAAAA,WAAUA,WAAU,gCAAgC,IAAI,EAAE,IAAI;AAE9D,EAAAA,WAAUA,WAAU,qCAAqC,IAAI,EAAE,IAAI;AAEnE,EAAAA,WAAUA,WAAU,sCAAsC,IAAI,EAAE,IAAI;AAEpE,EAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,EAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,EAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAKxE,EAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAExE,EAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAKhE,EAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAIlE,EAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAElE,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,EAAE,IAAI;AAEpD,EAAAA,WAAUA,WAAU,aAAa,IAAI,EAAE,IAAI;AAE3C,EAAAA,WAAUA,WAAU,kBAAkB,IAAI,EAAE,IAAI;AAEhD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,EAAE,IAAI;AAErD,EAAAA,WAAUA,WAAU,cAAc,IAAI,EAAE,IAAI;AAChD,GAAG,cAAc,YAAY,CAAC,EAAE;;;AC9DzB,IAAI;AAAA,CACV,SAAUC,OAAM;AAIb,EAAAA,MAAKA,MAAK,SAAS,IAAI,CAAC,IAAI;AAI5B,EAAAA,MAAKA,MAAK,UAAU,IAAI,CAAC,IAAI;AAI7B,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,EAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,EAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAK3B,EAAAA,MAAKA,MAAK,OAAO,IAAI,CAAC,IAAI;AAI1B,EAAAA,MAAKA,MAAK,KAAK,IAAI,CAAC,IAAI;AAC5B,GAAG,SAAS,OAAO,CAAC,EAAE;AACf,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AACnD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAIjC,SAAS,iBAAiB,IAAI;AACjC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,kBAAkB,IAAI;AAClC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,cAAc,IAAI;AAC9B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,cAAc,IAAI;AAC9B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,eAAe,IAAI;AAC/B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,aAAa,IAAI;AAC7B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,iBAAiB,IAAI;AACjC,SAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AACxE;AACO,SAAS,mBAAmB,IAAI;AACnC,SAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AACxE;;;AC/EO,IAAI,wBAAwB;;;ACInC,IAAI,kBAAkB;AAOf,SAAS,sBAAsB,UAAU;AAC5C,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ,iBAAiB,SAAU,OAAO;AAC/C,QAAI,MAAM,MAAM;AAChB,YAAQ,MAAM,CAAC,GAAG;AAAA;AAAA,MAEd,KAAK;AACD,eAAO,MAAM,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AACzD;AAAA;AAAA,MAEJ,KAAK;AACD,eAAO,OAAO,QAAQ,IAAI,YAAY;AACtC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,8DAA8D;AAAA;AAAA,MAEvF,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4CAA4C;AAAA;AAAA,MAErE,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ,CAAC,WAAW,WAAW,SAAS,QAAQ,QAAQ,EAAE,MAAM,CAAC;AACxE;AAAA;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,yCAAyC;AAAA,MAClE,KAAK;AACD,eAAO,MAAM,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC3C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,2DAA2D;AAAA;AAAA,MAEpF,KAAK;AACD,eAAO,UAAU,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AAC7D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,GAAG;AACT,gBAAM,IAAI,WAAW,+CAA+C;AAAA,QACxE;AACA,eAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,GAAG;AACT,gBAAM,IAAI,WAAW,+CAA+C;AAAA,QACxE;AACA,eAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS;AAChB;AAAA,MACJ,KAAK;AAAA;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4DAA4D;AAAA;AAAA,MAErF,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,kEAAkE;AAAA;AAAA,MAE3F,KAAK;AACD,eAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4DAA4D;AAAA;AAAA,MAErF,KAAK;AACD,eAAO,eAAe,MAAM,IAAI,UAAU;AAC1C;AAAA,MACJ,KAAK;AAAA;AAAA,MACL,KAAK;AAAA;AAAA,MACL,KAAK;AAAA;AAAA,MACL,KAAK;AAAA;AAAA,MACL,KAAK;AAAA;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,sEAAsE;AAAA,IACnG;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;;;ACvHO,IAAI,oBAAoB;;;ACCxB,SAAS,8BAA8B,UAAU;AACpD,MAAI,SAAS,WAAW,GAAG;AACvB,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACrD;AAEA,MAAI,eAAe,SACd,MAAM,iBAAiB,EACvB,OAAO,SAAU,GAAG;AAAE,WAAO,EAAE,SAAS;AAAA,EAAG,CAAC;AACjD,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,QAAQ,MAAM;AAC9E,QAAI,cAAc,eAAe,EAAE;AACnC,QAAI,iBAAiB,YAAY,MAAM,GAAG;AAC1C,QAAI,eAAe,WAAW,GAAG;AAC7B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AACA,QAAI,OAAO,eAAe,CAAC,GAAG,UAAU,eAAe,MAAM,CAAC;AAC9D,aAASC,MAAK,GAAG,YAAY,SAASA,MAAK,UAAU,QAAQA,OAAM;AAC/D,UAAI,SAAS,UAAUA,GAAE;AACzB,UAAI,OAAO,WAAW,GAAG;AACrB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC7C;AAAA,IACJ;AACA,WAAO,KAAK,EAAE,MAAY,QAAiB,CAAC;AAAA,EAChD;AACA,SAAO;AACX;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,KAAK,QAAQ,WAAW,EAAE;AACrC;AACA,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAClC,IAAI,sBAAsB;AAC1B,IAAI,8BAA8B;AAClC,SAAS,0BAA0B,KAAK;AACpC,MAAI,SAAS,CAAC;AACd,MAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAC7B,WAAO,mBAAmB;AAAA,EAC9B,WACS,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAClC,WAAO,mBAAmB;AAAA,EAC9B;AACA,MAAI,QAAQ,6BAA6B,SAAU,GAAG,IAAI,IAAI;AAE1D,QAAI,OAAO,OAAO,UAAU;AACxB,aAAO,2BAA2B,GAAG;AACrC,aAAO,2BAA2B,GAAG;AAAA,IACzC,WAES,OAAO,KAAK;AACjB,aAAO,2BAA2B,GAAG;AAAA,IACzC,WAES,GAAG,CAAC,MAAM,KAAK;AACpB,aAAO,2BAA2B,GAAG;AAAA,IACzC,OAEK;AACD,aAAO,2BAA2B,GAAG;AACrC,aAAO,2BACH,GAAG,UAAU,OAAO,OAAO,WAAW,GAAG,SAAS;AAAA,IAC1D;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;AACA,SAAS,UAAU,KAAK;AACpB,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,EACR;AACJ;AACA,SAAS,yCAAyC,MAAM;AAEpD,MAAI;AACJ,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,KAAK;AACpC,aAAS;AAAA,MACL,UAAU;AAAA,IACd;AACA,WAAO,KAAK,MAAM,CAAC;AAAA,EACvB,WACS,KAAK,CAAC,MAAM,KAAK;AACtB,aAAS;AAAA,MACL,UAAU;AAAA,IACd;AACA,WAAO,KAAK,MAAM,CAAC;AAAA,EACvB;AACA,MAAI,QAAQ;AACR,QAAI,cAAc,KAAK,MAAM,GAAG,CAAC;AACjC,QAAI,gBAAgB,MAAM;AACtB,aAAO,cAAc;AACrB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,WACS,gBAAgB,MAAM;AAC3B,aAAO,cAAc;AACrB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB;AACA,QAAI,CAAC,4BAA4B,KAAK,IAAI,GAAG;AACzC,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC/D;AACA,WAAO,uBAAuB,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,KAAK;AAC/B,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,UAAU,GAAG;AAC5B,MAAI,UAAU;AACV,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIO,SAAS,oBAAoB,QAAQ;AACxC,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,WAAW,QAAQ,KAAK,SAAS,QAAQ,MAAM;AAC5D,QAAI,QAAQ,SAAS,EAAE;AACvB,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ;AACf;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,WAAW,MAAM,QAAQ,CAAC;AACjC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,wBAAwB;AAC/B;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,OAAO,cAAc,MAAM,QAAQ,CAAC,CAAC;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,WAAW;AAClB,eAAO,iBAAiB;AACxB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,WAAW;AAClB,eAAO,iBAAiB;AACxB;AAAA,MACJ,KAAK;AACD,iBAAS,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,aAAa,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKC,MAAK;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,QAAI,GAAG,CAAC,CAAC,CAAC;AAChM;AAAA,MACJ,KAAK;AACD,iBAAS,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,cAAc,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKA,MAAK;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,QAAI,GAAG,CAAC,CAAC,CAAC;AACjM;AAAA,MACJ,KAAK;AACD,eAAO,WAAW;AAClB;AAAA;AAAA,MAEJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAC1C;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA;AAAA,MAEJ,KAAK;AACD,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,gBAAM,IAAI,WAAW,0DAA0D;AAAA,QACnF;AACA,cAAM,QAAQ,CAAC,EAAE,QAAQ,qBAAqB,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3E,cAAI,IAAI;AACJ,mBAAO,uBAAuB,GAAG;AAAA,UACrC,WACS,MAAM,IAAI;AACf,kBAAM,IAAI,MAAM,oDAAoD;AAAA,UACxE,WACS,IAAI;AACT,kBAAM,IAAI,MAAM,kDAAkD;AAAA,UACtE;AACA,iBAAO;AAAA,QACX,CAAC;AACD;AAAA,IACR;AAEA,QAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,aAAO,uBAAuB,MAAM,KAAK;AACzC;AAAA,IACJ;AACA,QAAI,yBAAyB,KAAK,MAAM,IAAI,GAAG;AAI3C,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,cAAM,IAAI,WAAW,+DAA+D;AAAA,MACxF;AACA,YAAM,KAAK,QAAQ,0BAA0B,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAE1E,YAAI,OAAO,KAAK;AACZ,iBAAO,wBAAwB,GAAG;AAAA,QACtC,WAES,MAAM,GAAG,CAAC,MAAM,KAAK;AAC1B,iBAAO,wBAAwB,GAAG;AAAA,QACtC,WAES,MAAM,IAAI;AACf,iBAAO,wBAAwB,GAAG;AAClC,iBAAO,wBAAwB,GAAG,SAAS,GAAG;AAAA,QAClD,OACK;AACD,iBAAO,wBAAwB,GAAG;AAClC,iBAAO,wBAAwB,GAAG;AAAA,QACtC;AACA,eAAO;AAAA,MACX,CAAC;AACD,UAAI,MAAM,MAAM,QAAQ,CAAC;AAEzB,UAAI,QAAQ,KAAK;AACb,iBAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,qBAAqB,iBAAiB,CAAC;AAAA,MACrF,WACS,KAAK;AACV,iBAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,GAAG,CAAC;AAAA,MAC1E;AACA;AAAA,IACJ;AAEA,QAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,MAAM,IAAI,CAAC;AAC7E;AAAA,IACJ;AACA,QAAI,WAAW,UAAU,MAAM,IAAI;AACnC,QAAI,UAAU;AACV,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,QAAQ;AAAA,IACpD;AACA,QAAI,sCAAsC,yCAAyC,MAAM,IAAI;AAC7F,QAAI,qCAAqC;AACrC,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,mCAAmC;AAAA,IAC/E;AAAA,EACJ;AACA,SAAO;AACX;;;ACzTO,IAAI,WAAW;AAAA,EAClB,OAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;ACh4CO,SAAS,eAAe,UAAU,QAAQ;AAC7C,MAAI,eAAe;AACnB,WAAS,aAAa,GAAG,aAAa,SAAS,QAAQ,cAAc;AACjE,QAAI,cAAc,SAAS,OAAO,UAAU;AAC5C,QAAI,gBAAgB,KAAK;AACrB,UAAI,cAAc;AAClB,aAAO,aAAa,IAAI,SAAS,UAC7B,SAAS,OAAO,aAAa,CAAC,MAAM,aAAa;AACjD;AACA;AAAA,MACJ;AACA,UAAI,UAAU,KAAK,cAAc;AACjC,UAAI,eAAe,cAAc,IAAI,IAAI,KAAK,eAAe;AAC7D,UAAI,gBAAgB;AACpB,UAAI,WAAW,+BAA+B,MAAM;AACpD,UAAI,YAAY,OAAO,YAAY,KAAK;AACpC,uBAAe;AAAA,MACnB;AACA,aAAO,iBAAiB,GAAG;AACvB,wBAAgB;AAAA,MACpB;AACA,aAAO,YAAY,GAAG;AAClB,uBAAe,WAAW;AAAA,MAC9B;AAAA,IACJ,WACS,gBAAgB,KAAK;AAC1B,sBAAgB;AAAA,IACpB,OACK;AACD,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;AAMA,SAAS,+BAA+B,QAAQ;AAC5C,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc;AAAA,EAEd,OAAO;AAAA,EAEP,OAAO,WAAW,QAAQ;AAE1B,gBAAY,OAAO,WAAW,CAAC;AAAA,EACnC;AACA,MAAI,WAAW;AACX,YAAQ,WAAW;AAAA,MACf,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX;AACI,cAAM,IAAI,MAAM,mBAAmB;AAAA,IAC3C;AAAA,EACJ;AAEA,MAAI,cAAc,OAAO;AACzB,MAAI;AACJ,MAAI,gBAAgB,QAAQ;AACxB,gBAAY,OAAO,SAAS,EAAE;AAAA,EAClC;AACA,MAAI,aAAa,SAAS,aAAa,EAAE,KACrC,SAAS,eAAe,EAAE,KAC1B,SAAS,GAAG,OAAO,aAAa,MAAM,CAAC,KACvC,SAAS,KAAK;AAClB,SAAO,WAAW,CAAC;AACvB;;;AClFA,IAAI;AAOJ,IAAI,8BAA8B,IAAI,OAAO,IAAI,OAAO,sBAAsB,QAAQ,GAAG,CAAC;AAC1F,IAAI,4BAA4B,IAAI,OAAO,GAAG,OAAO,sBAAsB,QAAQ,IAAI,CAAC;AACxF,SAAS,eAAe,OAAO,KAAK;AAChC,SAAO,EAAE,OAAc,IAAS;AACpC;AAGA,IAAI,sBAAsB,CAAC,CAAC,OAAO,UAAU,cAAc,KAAK,WAAW,KAAK,CAAC;AACjF,IAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,IAAI,uBAAuB,CAAC,CAAC,OAAO;AACpC,IAAI,uBAAuB,CAAC,CAAC,OAAO,UAAU;AAC9C,IAAI,eAAe,CAAC,CAAC,OAAO,UAAU;AACtC,IAAI,aAAa,CAAC,CAAC,OAAO,UAAU;AACpC,IAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,IAAI,gBAAgB,yBACd,OAAO,gBACP,SAAU,GAAG;AACX,SAAQ,OAAO,MAAM,YACjB,SAAS,CAAC,KACV,KAAK,MAAM,CAAC,MAAM,KAClB,KAAK,IAAI,CAAC,KAAK;AACvB;AAEJ,IAAI,yBAAyB;AAC7B,IAAI;AACI,OAAK,GAAG,6CAA6C,IAAI;AAO7D,6BAA2B,KAAK,GAAG,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO;AAClG,SACO,GAAG;AACN,2BAAyB;AAC7B;AAXQ;AAYR,IAAI,aAAa;AAAA;AAAA,EAET,SAASC,YAAW,GAAG,QAAQ,UAAU;AACrC,WAAO,EAAE,WAAW,QAAQ,QAAQ;AAAA,EACxC;AAAA;AAAA;AAAA,EAEA,SAASA,YAAW,GAAG,QAAQ,UAAU;AACrC,WAAO,EAAE,MAAM,UAAU,WAAW,OAAO,MAAM,MAAM;AAAA,EAC3D;AAAA;AACR,IAAI,gBAAgB,yBACd,OAAO;AAAA;AAAA,EAEL,SAASC,iBAAgB;AACrB,QAAI,aAAa,CAAC;AAClB,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAW,EAAE,IAAI,UAAU,EAAE;AAAA,IACjC;AACA,QAAI,WAAW;AACf,QAAI,SAAS,WAAW;AACxB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,SAAS,GAAG;AACf,aAAO,WAAW,GAAG;AACrB,UAAI,OAAO;AACP,cAAM,WAAW,OAAO,4BAA4B;AACxD,kBACI,OAAO,QACD,OAAO,aAAa,IAAI,IACxB,OAAO,eAAe,QAAQ,UAAY,MAAM,OAAS,OAAO,OAAS,KAAM;AAAA,IAC7F;AACA,WAAO;AAAA,EACX;AAAA;AACR,IAAI;AAAA;AAAA,EAEJ,uBACM,OAAO;AAAA;AAAA,IAEL,SAASC,aAAY,SAAS;AAC1B,UAAI,MAAM,CAAC;AACX,eAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,YAAIC,MAAK,UAAU,EAAE,GAAG,IAAIA,IAAG,CAAC,GAAG,IAAIA,IAAG,CAAC;AAC3C,YAAI,CAAC,IAAI;AAAA,MACb;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AACR,IAAI,cAAc;AAAA;AAAA,EAEV,SAASC,aAAY,GAAG,OAAO;AAC3B,WAAO,EAAE,YAAY,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA,EAEA,SAASA,aAAY,GAAG,OAAO;AAC3B,QAAI,OAAO,EAAE;AACb,QAAI,QAAQ,KAAK,SAAS,MAAM;AAC5B,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,EAAE,WAAW,KAAK;AAC9B,QAAI;AACJ,WAAO,QAAQ,SACX,QAAQ,SACR,QAAQ,MAAM,SACb,SAAS,EAAE,WAAW,QAAQ,CAAC,KAAK,SACrC,SAAS,QACP,SACE,QAAQ,SAAW,OAAO,SAAS,SAAU;AAAA,EACzD;AAAA;AACR,IAAI,YAAY;AAAA;AAAA,EAER,SAASC,WAAU,GAAG;AAClB,WAAO,EAAE,UAAU;AAAA,EACvB;AAAA;AAAA;AAAA,EAEA,SAASA,WAAU,GAAG;AAClB,WAAO,EAAE,QAAQ,6BAA6B,EAAE;AAAA,EACpD;AAAA;AACR,IAAI,UAAU;AAAA;AAAA,EAEN,SAASC,SAAQ,GAAG;AAChB,WAAO,EAAE,QAAQ;AAAA,EACrB;AAAA;AAAA;AAAA,EAEA,SAASA,SAAQ,GAAG;AAChB,WAAO,EAAE,QAAQ,2BAA2B,EAAE;AAAA,EAClD;AAAA;AAER,SAAS,GAAG,GAAG,MAAM;AACjB,SAAO,IAAI,OAAO,GAAG,IAAI;AAC7B;AAEA,IAAI;AACJ,IAAI,wBAAwB;AAEpB,2BAAyB,GAAG,6CAA6C,IAAI;AACjF,2BAAyB,SAASC,wBAAuB,GAAG,OAAO;AAC/D,QAAIJ;AACJ,2BAAuB,YAAY;AACnC,QAAI,QAAQ,uBAAuB,KAAK,CAAC;AACzC,YAAQA,MAAK,MAAM,CAAC,OAAO,QAAQA,QAAO,SAASA,MAAK;AAAA,EAC5D;AACJ,OACK;AAED,2BAAyB,SAASI,wBAAuB,GAAG,OAAO;AAC/D,QAAI,QAAQ,CAAC;AACb,WAAO,MAAM;AACT,UAAI,IAAI,YAAY,GAAG,KAAK;AAC5B,UAAI,MAAM,UAAa,cAAc,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC5D;AAAA,MACJ;AACA,YAAM,KAAK,CAAC;AACZ,eAAS,KAAK,QAAU,IAAI;AAAA,IAChC;AACA,WAAO,cAAc,MAAM,QAAQ,KAAK;AAAA,EAC5C;AACJ;AAtBQ;AAuBR,IAAI;AAAA;AAAA,EAAwB,WAAY;AACpC,aAASC,QAAO,SAAS,SAAS;AAC9B,UAAI,YAAY,QAAQ;AAAE,kBAAU,CAAC;AAAA,MAAG;AACxC,WAAK,UAAU;AACf,WAAK,WAAW,EAAE,QAAQ,GAAG,MAAM,GAAG,QAAQ,EAAE;AAChD,WAAK,YAAY,CAAC,CAAC,QAAQ;AAC3B,WAAK,SAAS,QAAQ;AACtB,WAAK,sBAAsB,CAAC,CAAC,QAAQ;AACrC,WAAK,uBAAuB,CAAC,CAAC,QAAQ;AAAA,IAC1C;AACA,IAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,UAAI,KAAK,OAAO,MAAM,GAAG;AACrB,cAAM,MAAM,8BAA8B;AAAA,MAC9C;AACA,aAAO,KAAK,aAAa,GAAG,IAAI,KAAK;AAAA,IACzC;AACA,IAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe,mBAAmB;AACtF,UAAI,WAAW,CAAC;AAChB,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,OAAO,KAAK,KAAK;AACrB,YAAI,SAAS,KAAe;AACxB,cAAI,SAAS,KAAK,cAAc,cAAc,iBAAiB;AAC/D,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B,WACS,SAAS,OAAiB,eAAe,GAAG;AACjD;AAAA,QACJ,WACS,SAAS,OACb,kBAAkB,YAAY,kBAAkB,kBAAkB;AACnE,cAAI,WAAW,KAAK,cAAc;AAClC,eAAK,KAAK;AACV,mBAAS,KAAK;AAAA,YACV,MAAM,KAAK;AAAA,YACX,UAAU,eAAe,UAAU,KAAK,cAAc,CAAC;AAAA,UAC3D,CAAC;AAAA,QACL,WACS,SAAS,MACd,CAAC,KAAK,aACN,KAAK,KAAK,MAAM,IAClB;AACE,cAAI,mBAAmB;AACnB;AAAA,UACJ,OACK;AACD,mBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,UACjH;AAAA,QACJ,WACS,SAAS,MACd,CAAC,KAAK,aACN,SAAS,KAAK,KAAK,KAAK,CAAC,GAAG;AAC5B,cAAI,SAAS,KAAK,SAAS,cAAc,aAAa;AACtD,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B,OACK;AACD,cAAI,SAAS,KAAK,aAAa,cAAc,aAAa;AAC1D,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO,EAAE,KAAK,UAAU,KAAK,KAAK;AAAA,IACtC;AAmBA,IAAAA,QAAO,UAAU,WAAW,SAAU,cAAc,eAAe;AAC/D,UAAI,gBAAgB,KAAK,cAAc;AACvC,WAAK,KAAK;AACV,UAAI,UAAU,KAAK,aAAa;AAChC,WAAK,UAAU;AACf,UAAI,KAAK,OAAO,IAAI,GAAG;AAEnB,eAAO;AAAA,UACH,KAAK;AAAA,YACD,MAAM,KAAK;AAAA,YACX,OAAO,IAAI,OAAO,SAAS,IAAI;AAAA,YAC/B,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,UAChE;AAAA,UACA,KAAK;AAAA,QACT;AAAA,MACJ,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,YAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,IAAI;AAC5E,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,eAAe;AAE9B,YAAI,sBAAsB,KAAK,cAAc;AAC7C,YAAI,KAAK,OAAO,IAAI,GAAG;AACnB,cAAI,KAAK,MAAM,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG;AACxC,mBAAO,KAAK,MAAM,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,UACtG;AACA,cAAI,8BAA8B,KAAK,cAAc;AACrD,cAAI,iBAAiB,KAAK,aAAa;AACvC,cAAI,YAAY,gBAAgB;AAC5B,mBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,6BAA6B,KAAK,cAAc,CAAC,CAAC;AAAA,UACxH;AACA,eAAK,UAAU;AACf,cAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,mBAAO,KAAK,MAAM,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,UACtG;AACA,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,KAAK;AAAA,cACX,OAAO;AAAA,cACP;AAAA,cACA,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,YAChE;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ,OACK;AACD,iBAAO,KAAK,MAAM,UAAU,cAAc,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,QACjG;AAAA,MACJ,OACK;AACD,eAAO,KAAK,MAAM,UAAU,aAAa,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,MAChG;AAAA,IACJ;AAIA,IAAAA,QAAO,UAAU,eAAe,WAAY;AACxC,UAAI,cAAc,KAAK,OAAO;AAC9B,WAAK,KAAK;AACV,aAAO,CAAC,KAAK,MAAM,KAAK,4BAA4B,KAAK,KAAK,CAAC,GAAG;AAC9D,aAAK,KAAK;AAAA,MACd;AACA,aAAO,KAAK,QAAQ,MAAM,aAAa,KAAK,OAAO,CAAC;AAAA,IACxD;AACA,IAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe;AACnE,UAAI,QAAQ,KAAK,cAAc;AAC/B,UAAI,QAAQ;AACZ,aAAO,MAAM;AACT,YAAI,mBAAmB,KAAK,cAAc,aAAa;AACvD,YAAI,kBAAkB;AAClB,mBAAS;AACT;AAAA,QACJ;AACA,YAAI,sBAAsB,KAAK,iBAAiB,cAAc,aAAa;AAC3E,YAAI,qBAAqB;AACrB,mBAAS;AACT;AAAA,QACJ;AACA,YAAI,uBAAuB,KAAK,yBAAyB;AACzD,YAAI,sBAAsB;AACtB,mBAAS;AACT;AAAA,QACJ;AACA;AAAA,MACJ;AACA,UAAI,WAAW,eAAe,OAAO,KAAK,cAAc,CAAC;AACzD,aAAO;AAAA,QACH,KAAK,EAAE,MAAM,KAAK,SAAS,OAAc,SAAmB;AAAA,QAC5D,KAAK;AAAA,MACT;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,2BAA2B,WAAY;AACpD,UAAI,CAAC,KAAK,MAAM,KACZ,KAAK,KAAK,MAAM,OACf,KAAK;AAAA,MAEF,CAAC,gBAAgB,KAAK,KAAK,KAAK,CAAC,IAAI;AACzC,aAAK,KAAK;AACV,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAMA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,eAAe;AACtD,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,IAAc;AAC9C,eAAO;AAAA,MACX;AAGA,cAAQ,KAAK,KAAK,GAAG;AAAA,QACjB,KAAK;AAED,eAAK,KAAK;AACV,eAAK,KAAK;AACV,iBAAO;AAAA;AAAA,QAEX,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD;AAAA,QACJ,KAAK;AACD,cAAI,kBAAkB,YAAY,kBAAkB,iBAAiB;AACjE;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACf;AACA,WAAK,KAAK;AACV,UAAI,aAAa,CAAC,KAAK,KAAK,CAAC;AAC7B,WAAK,KAAK;AAEV,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,YAAI,OAAO,IAAc;AACrB,cAAI,KAAK,KAAK,MAAM,IAAc;AAC9B,uBAAW,KAAK,EAAE;AAElB,iBAAK,KAAK;AAAA,UACd,OACK;AAED,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,QACJ,OACK;AACD,qBAAW,KAAK,EAAE;AAAA,QACtB;AACA,aAAK,KAAK;AAAA,MACd;AACA,aAAO,cAAc,MAAM,QAAQ,UAAU;AAAA,IACjD;AACA,IAAAA,QAAO,UAAU,mBAAmB,SAAU,cAAc,eAAe;AACvE,UAAI,KAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,OAAO,MACP,OAAO,OACN,OAAO,OACH,kBAAkB,YAAY,kBAAkB,oBACpD,OAAO,OAAiB,eAAe,GAAI;AAC5C,eAAO;AAAA,MACX,OACK;AACD,aAAK,KAAK;AACV,eAAO,cAAc,EAAE;AAAA,MAC3B;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,cAAc,mBAAmB;AACxE,UAAI,uBAAuB,KAAK,cAAc;AAC9C,WAAK,KAAK;AACV,WAAK,UAAU;AACf,UAAI,KAAK,MAAM,GAAG;AACd,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,UAAI,KAAK,KAAK,MAAM,KAAe;AAC/B,aAAK,KAAK;AACV,eAAO,KAAK,MAAM,UAAU,gBAAgB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAC1G;AAEA,UAAI,QAAQ,KAAK,0BAA0B,EAAE;AAC7C,UAAI,CAAC,OAAO;AACR,eAAO,KAAK,MAAM,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAC9G;AACA,WAAK,UAAU;AACf,UAAI,KAAK,MAAM,GAAG;AACd,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,cAAQ,KAAK,KAAK,GAAG;AAAA;AAAA,QAEjB,KAAK,KAAe;AAChB,eAAK,KAAK;AACV,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,KAAK;AAAA;AAAA,cAEX;AAAA,cACA,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,YACvE;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ;AAAA;AAAA,QAEA,KAAK,IAAc;AACf,eAAK,KAAK;AACV,eAAK,UAAU;AACf,cAAI,KAAK,MAAM,GAAG;AACd,mBAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UACzH;AACA,iBAAO,KAAK,qBAAqB,cAAc,mBAAmB,OAAO,oBAAoB;AAAA,QACjG;AAAA,QACA;AACI,iBAAO,KAAK,MAAM,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAClH;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,4BAA4B,WAAY;AACrD,UAAI,mBAAmB,KAAK,cAAc;AAC1C,UAAI,cAAc,KAAK,OAAO;AAC9B,UAAI,QAAQ,uBAAuB,KAAK,SAAS,WAAW;AAC5D,UAAI,YAAY,cAAc,MAAM;AACpC,WAAK,OAAO,SAAS;AACrB,UAAI,cAAc,KAAK,cAAc;AACrC,UAAI,WAAW,eAAe,kBAAkB,WAAW;AAC3D,aAAO,EAAE,OAAc,SAAmB;AAAA,IAC9C;AACA,IAAAA,QAAO,UAAU,uBAAuB,SAAU,cAAc,mBAAmB,OAAO,sBAAsB;AAC5G,UAAIL;AAIJ,UAAI,oBAAoB,KAAK,cAAc;AAC3C,UAAI,UAAU,KAAK,0BAA0B,EAAE;AAC/C,UAAI,kBAAkB,KAAK,cAAc;AACzC,cAAQ,SAAS;AAAA,QACb,KAAK;AAED,iBAAO,KAAK,MAAM,UAAU,sBAAsB,eAAe,mBAAmB,eAAe,CAAC;AAAA,QACxG,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QAAQ;AAIT,eAAK,UAAU;AACf,cAAI,mBAAmB;AACvB,cAAI,KAAK,OAAO,GAAG,GAAG;AAClB,iBAAK,UAAU;AACf,gBAAI,qBAAqB,KAAK,cAAc;AAC5C,gBAAI,SAAS,KAAK,8BAA8B;AAChD,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AACA,gBAAI,QAAQ,QAAQ,OAAO,GAAG;AAC9B,gBAAI,MAAM,WAAW,GAAG;AACpB,qBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,YACjH;AACA,gBAAI,gBAAgB,eAAe,oBAAoB,KAAK,cAAc,CAAC;AAC3E,+BAAmB,EAAE,OAAc,cAA6B;AAAA,UACpE;AACA,cAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,cAAI,eAAe,KAAK;AACpB,mBAAO;AAAA,UACX;AACA,cAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAE1E,cAAI,oBAAoB,WAAW,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,MAAM,CAAC,GAAG;AAErI,gBAAI,WAAW,UAAU,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACxD,gBAAI,YAAY,UAAU;AACtB,kBAAI,SAAS,KAAK,8BAA8B,UAAU,iBAAiB,aAAa;AACxF,kBAAI,OAAO,KAAK;AACZ,uBAAO;AAAA,cACX;AACA,qBAAO;AAAA,gBACH,KAAK,EAAE,MAAM,KAAK,QAAQ,OAAc,UAAU,YAAY,OAAO,OAAO,IAAI;AAAA,gBAChF,KAAK;AAAA,cACT;AAAA,YACJ,OACK;AACD,kBAAI,SAAS,WAAW,GAAG;AACvB,uBAAO,KAAK,MAAM,UAAU,2BAA2B,UAAU;AAAA,cACrE;AACA,kBAAI,kBAAkB;AAItB,kBAAI,KAAK,QAAQ;AACb,kCAAkB,eAAe,UAAU,KAAK,MAAM;AAAA,cAC1D;AACA,kBAAI,QAAQ;AAAA,gBACR,MAAM,cAAc;AAAA,gBACpB,SAAS;AAAA,gBACT,UAAU,iBAAiB;AAAA,gBAC3B,eAAe,KAAK,uBACd,sBAAsB,eAAe,IACrC,CAAC;AAAA,cACX;AACA,kBAAI,OAAO,YAAY,SAAS,KAAK,OAAO,KAAK;AACjD,qBAAO;AAAA,gBACH,KAAK,EAAE,MAAY,OAAc,UAAU,YAAY,MAAa;AAAA,gBACpE,KAAK;AAAA,cACT;AAAA,YACJ;AAAA,UACJ;AAEA,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,YAAY,WACZ,KAAK,SACL,YAAY,SACR,KAAK,OACL,KAAK;AAAA,cACf;AAAA,cACA,UAAU;AAAA,cACV,QAAQA,MAAK,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,QAAQA,QAAO,SAASA,MAAK;AAAA,YAC9I;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,UAAU;AAIX,cAAI,oBAAoB,KAAK,cAAc;AAC3C,eAAK,UAAU;AACf,cAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,mBAAO,KAAK,MAAM,UAAU,gCAAgC,eAAe,mBAAmB,SAAS,CAAC,GAAG,iBAAiB,CAAC,CAAC;AAAA,UAClI;AACA,eAAK,UAAU;AASf,cAAI,wBAAwB,KAAK,0BAA0B;AAC3D,cAAI,eAAe;AACnB,cAAI,YAAY,YAAY,sBAAsB,UAAU,UAAU;AAClE,gBAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,qBAAO,KAAK,MAAM,UAAU,qCAAqC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,YAC/H;AACA,iBAAK,UAAU;AACf,gBAAI,SAAS,KAAK,uBAAuB,UAAU,qCAAqC,UAAU,oCAAoC;AACtI,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AAEA,iBAAK,UAAU;AACf,oCAAwB,KAAK,0BAA0B;AACvD,2BAAe,OAAO;AAAA,UAC1B;AACA,cAAI,gBAAgB,KAAK,8BAA8B,cAAc,SAAS,mBAAmB,qBAAqB;AACtH,cAAI,cAAc,KAAK;AACnB,mBAAO;AAAA,UACX;AACA,cAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,cAAI,eAAe,KAAK;AACpB,mBAAO;AAAA,UACX;AACA,cAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAC1E,cAAI,YAAY,UAAU;AACtB,mBAAO;AAAA,cACH,KAAK;AAAA,gBACD,MAAM,KAAK;AAAA,gBACX;AAAA,gBACA,SAAS,YAAY,cAAc,GAAG;AAAA,gBACtC,UAAU;AAAA,cACd;AAAA,cACA,KAAK;AAAA,YACT;AAAA,UACJ,OACK;AACD,mBAAO;AAAA,cACH,KAAK;AAAA,gBACD,MAAM,KAAK;AAAA,gBACX;AAAA,gBACA,SAAS,YAAY,cAAc,GAAG;AAAA,gBACtC,QAAQ;AAAA,gBACR,YAAY,YAAY,WAAW,aAAa;AAAA,gBAChD,UAAU;AAAA,cACd;AAAA,cACA,KAAK;AAAA,YACT;AAAA,UACJ;AAAA,QACJ;AAAA,QACA;AACI,iBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,mBAAmB,eAAe,CAAC;AAAA,MAC7G;AAAA,IACJ;AACA,IAAAK,QAAO,UAAU,wBAAwB,SAAU,sBAAsB;AAGrE,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAe;AAC/C,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,WAAK,KAAK;AACV,aAAO,EAAE,KAAK,MAAM,KAAK,KAAK;AAAA,IAClC;AAIA,IAAAA,QAAO,UAAU,gCAAgC,WAAY;AACzD,UAAI,eAAe;AACnB,UAAI,gBAAgB,KAAK,cAAc;AACvC,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,gBAAQ,IAAI;AAAA,UACR,KAAK,IAAc;AAGf,iBAAK,KAAK;AACV,gBAAI,qBAAqB,KAAK,cAAc;AAC5C,gBAAI,CAAC,KAAK,UAAU,GAAG,GAAG;AACtB,qBAAO,KAAK,MAAM,UAAU,kCAAkC,eAAe,oBAAoB,KAAK,cAAc,CAAC,CAAC;AAAA,YAC1H;AACA,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,UACA,KAAK,KAAe;AAChB,4BAAgB;AAChB,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,UACA,KAAK,KAAe;AAChB,gBAAI,eAAe,GAAG;AAClB,8BAAgB;AAAA,YACpB,OACK;AACD,qBAAO;AAAA,gBACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,gBAC3D,KAAK;AAAA,cACT;AAAA,YACJ;AACA;AAAA,UACJ;AAAA,UACA;AACI,iBAAK,KAAK;AACV;AAAA,QACR;AAAA,MACJ;AACA,aAAO;AAAA,QACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,QAC3D,KAAK;AAAA,MACT;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,gCAAgC,SAAU,UAAU,UAAU;AAC3E,UAAI,SAAS,CAAC;AACd,UAAI;AACA,iBAAS,8BAA8B,QAAQ;AAAA,MACnD,SACO,GAAG;AACN,eAAO,KAAK,MAAM,UAAU,yBAAyB,QAAQ;AAAA,MACjE;AACA,aAAO;AAAA,QACH,KAAK;AAAA,UACD,MAAM,cAAc;AAAA,UACpB;AAAA,UACA;AAAA,UACA,eAAe,KAAK,uBACd,oBAAoB,MAAM,IAC1B,CAAC;AAAA,QACX;AAAA,QACA,KAAK;AAAA,MACT;AAAA,IACJ;AAWA,IAAAA,QAAO,UAAU,gCAAgC,SAAU,cAAc,eAAe,gBAAgB,uBAAuB;AAC3H,UAAIL;AACJ,UAAI,iBAAiB;AACrB,UAAI,UAAU,CAAC;AACf,UAAI,kBAAkB,oBAAI,IAAI;AAC9B,UAAI,WAAW,sBAAsB,OAAO,mBAAmB,sBAAsB;AAIrF,aAAO,MAAM;AACT,YAAI,SAAS,WAAW,GAAG;AACvB,cAAI,gBAAgB,KAAK,cAAc;AACvC,cAAI,kBAAkB,YAAY,KAAK,OAAO,GAAG,GAAG;AAEhD,gBAAI,SAAS,KAAK,uBAAuB,UAAU,iCAAiC,UAAU,gCAAgC;AAC9H,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AACA,+BAAmB,eAAe,eAAe,KAAK,cAAc,CAAC;AACrE,uBAAW,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,UACrE,OACK;AACD;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,gBAAgB,IAAI,QAAQ,GAAG;AAC/B,iBAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,qCACV,UAAU,oCAAoC,gBAAgB;AAAA,QACxE;AACA,YAAI,aAAa,SAAS;AACtB,2BAAiB;AAAA,QACrB;AAIA,aAAK,UAAU;AACf,YAAI,uBAAuB,KAAK,cAAc;AAC9C,YAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,iBAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,2CACV,UAAU,0CAA0C,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,QACxH;AACA,YAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,cAAc;AACtF,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,YAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,gBAAQ,KAAK;AAAA,UACT;AAAA,UACA;AAAA,YACI,OAAO,eAAe;AAAA,YACtB,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,UACvE;AAAA,QACJ,CAAC;AAED,wBAAgB,IAAI,QAAQ;AAE5B,aAAK,UAAU;AACf,QAACA,MAAK,KAAK,0BAA0B,GAAG,WAAWA,IAAG,OAAO,mBAAmBA,IAAG;AAAA,MACvF;AACA,UAAI,QAAQ,WAAW,GAAG;AACtB,eAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,kCACV,UAAU,iCAAiC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,MAC/G;AACA,UAAI,KAAK,uBAAuB,CAAC,gBAAgB;AAC7C,eAAO,KAAK,MAAM,UAAU,sBAAsB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,MAChH;AACA,aAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,IACrC;AACA,IAAAK,QAAO,UAAU,yBAAyB,SAAU,mBAAmB,oBAAoB;AACvF,UAAI,OAAO;AACX,UAAI,mBAAmB,KAAK,cAAc;AAC1C,UAAI,KAAK,OAAO,GAAG,GAAG;AAAA,MACtB,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,eAAO;AAAA,MACX;AACA,UAAI,YAAY;AAChB,UAAI,UAAU;AACd,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,YAAI,MAAM,MAAgB,MAAM,IAAc;AAC1C,sBAAY;AACZ,oBAAU,UAAU,MAAM,KAAK;AAC/B,eAAK,KAAK;AAAA,QACd,OACK;AACD;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,WAAW,eAAe,kBAAkB,KAAK,cAAc,CAAC;AACpE,UAAI,CAAC,WAAW;AACZ,eAAO,KAAK,MAAM,mBAAmB,QAAQ;AAAA,MACjD;AACA,iBAAW;AACX,UAAI,CAAC,cAAc,OAAO,GAAG;AACzB,eAAO,KAAK,MAAM,oBAAoB,QAAQ;AAAA,MAClD;AACA,aAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,IACrC;AACA,IAAAA,QAAO,UAAU,SAAS,WAAY;AAClC,aAAO,KAAK,SAAS;AAAA,IACzB;AACA,IAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,aAAO,KAAK,OAAO,MAAM,KAAK,QAAQ;AAAA,IAC1C;AACA,IAAAA,QAAO,UAAU,gBAAgB,WAAY;AAEzC,aAAO;AAAA,QACH,QAAQ,KAAK,SAAS;AAAA,QACtB,MAAM,KAAK,SAAS;AAAA,QACpB,QAAQ,KAAK,SAAS;AAAA,MAC1B;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,SAAS,KAAK,SAAS;AAC3B,UAAI,UAAU,KAAK,QAAQ,QAAQ;AAC/B,cAAM,MAAM,cAAc;AAAA,MAC9B;AACA,UAAI,OAAO,YAAY,KAAK,SAAS,MAAM;AAC3C,UAAI,SAAS,QAAW;AACpB,cAAM,MAAM,UAAU,OAAO,QAAQ,0CAA0C,CAAC;AAAA,MACpF;AACA,aAAO;AAAA,IACX;AACA,IAAAA,QAAO,UAAU,QAAQ,SAAU,MAAM,UAAU;AAC/C,aAAO;AAAA,QACH,KAAK;AAAA,QACL,KAAK;AAAA,UACD;AAAA,UACA,SAAS,KAAK;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,KAAK,MAAM,GAAG;AACd;AAAA,MACJ;AACA,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,SAAS,IAAe;AACxB,aAAK,SAAS,QAAQ;AACtB,aAAK,SAAS,SAAS;AACvB,aAAK,SAAS,UAAU;AAAA,MAC5B,OACK;AACD,aAAK,SAAS,UAAU;AAExB,aAAK,SAAS,UAAU,OAAO,QAAU,IAAI;AAAA,MACjD;AAAA,IACJ;AAOA,IAAAA,QAAO,UAAU,SAAS,SAAU,QAAQ;AACxC,UAAI,WAAW,KAAK,SAAS,QAAQ,KAAK,OAAO,CAAC,GAAG;AACjD,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,eAAK,KAAK;AAAA,QACd;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAKA,IAAAA,QAAO,UAAU,YAAY,SAAU,SAAS;AAC5C,UAAI,gBAAgB,KAAK,OAAO;AAChC,UAAI,QAAQ,KAAK,QAAQ,QAAQ,SAAS,aAAa;AACvD,UAAI,SAAS,GAAG;AACZ,aAAK,OAAO,KAAK;AACjB,eAAO;AAAA,MACX,OACK;AACD,aAAK,OAAO,KAAK,QAAQ,MAAM;AAC/B,eAAO;AAAA,MACX;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,SAAS,SAAU,cAAc;AAC9C,UAAI,KAAK,OAAO,IAAI,cAAc;AAC9B,cAAM,MAAM,gBAAgB,OAAO,cAAc,uDAAuD,EAAE,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,MACnI;AACA,qBAAe,KAAK,IAAI,cAAc,KAAK,QAAQ,MAAM;AACzD,aAAO,MAAM;AACT,YAAI,SAAS,KAAK,OAAO;AACzB,YAAI,WAAW,cAAc;AACzB;AAAA,QACJ;AACA,YAAI,SAAS,cAAc;AACvB,gBAAM,MAAM,gBAAgB,OAAO,cAAc,0CAA0C,CAAC;AAAA,QAChG;AACA,aAAK,KAAK;AACV,YAAI,KAAK,MAAM,GAAG;AACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAAA,QAAO,UAAU,YAAY,WAAY;AACrC,aAAO,CAAC,KAAK,MAAM,KAAK,cAAc,KAAK,KAAK,CAAC,GAAG;AAChD,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,KAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,WAAW,KAAK,QAAQ,WAAW,UAAU,QAAQ,QAAU,IAAI,EAAE;AACzE,aAAO,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,IACjE;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAOF,SAAS,SAAS,WAAW;AACzB,SAAS,aAAa,MAAM,aAAa,OACpC,aAAa,MAAM,aAAa;AACzC;AACA,SAAS,gBAAgB,WAAW;AAChC,SAAO,SAAS,SAAS,KAAK,cAAc;AAChD;AAEA,SAAS,4BAA4B,GAAG;AACpC,SAAQ,MAAM,MACV,MAAM,MACL,KAAK,MAAM,KAAK,MACjB,MAAM,MACL,KAAK,MAAM,KAAK,OAChB,KAAK,MAAM,KAAK,MACjB,KAAK,OACJ,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAS,KAAK,QACnB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAW,KAAK;AAC9B;AAKA,SAAS,cAAc,GAAG;AACtB,SAAS,KAAK,KAAU,KAAK,MACzB,MAAM,MACN,MAAM,OACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM;AACd;AAKA,SAAS,iBAAiB,GAAG;AACzB,SAAS,KAAK,MAAU,KAAK,MACzB,MAAM,MACL,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACL,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,OAAU,KAAK,OACrB,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,OACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK;AAC7B;;;ACvvCA,SAAS,cAAc,KAAK;AACxB,MAAI,QAAQ,SAAU,IAAI;AACtB,WAAO,GAAG;AACV,QAAI,gBAAgB,EAAE,KAAK,gBAAgB,EAAE,GAAG;AAC5C,eAAS,KAAK,GAAG,SAAS;AACtB,eAAO,GAAG,QAAQ,CAAC,EAAE;AACrB,sBAAc,GAAG,QAAQ,CAAC,EAAE,KAAK;AAAA,MACrC;AAAA,IACJ,WACS,gBAAgB,EAAE,KAAK,iBAAiB,GAAG,KAAK,GAAG;AACxD,aAAO,GAAG,MAAM;AAAA,IACpB,YACU,cAAc,EAAE,KAAK,cAAc,EAAE,MAC3C,mBAAmB,GAAG,KAAK,GAAG;AAC9B,aAAO,GAAG,MAAM;AAAA,IACpB,WACS,aAAa,EAAE,GAAG;AACvB,oBAAc,GAAG,QAAQ;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AACO,SAAS,MAAM,SAAS,MAAM;AACjC,MAAI,SAAS,QAAQ;AAAE,WAAO,CAAC;AAAA,EAAG;AAClC,SAAO,SAAS,EAAE,sBAAsB,MAAM,qBAAqB,KAAK,GAAG,IAAI;AAC/E,MAAI,SAAS,IAAI,OAAO,SAAS,IAAI,EAAE,MAAM;AAC7C,MAAI,OAAO,KAAK;AACZ,QAAI,QAAQ,YAAY,UAAU,OAAO,IAAI,IAAI,CAAC;AAElD,UAAM,WAAW,OAAO,IAAI;AAE5B,UAAM,kBAAkB,OAAO,IAAI;AACnC,UAAM;AAAA,EACV;AACA,MAAI,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,kBAAkB;AACrE,kBAAc,OAAO,GAAG;AAAA,EAC5B;AACA,SAAO,OAAO;AAClB;;;ACxCO,IAAI;AAAA,CACV,SAAUC,YAAW;AAElB,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,kBAAkB,IAAI;AACpC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,aAAY,KAAK,MAAM,iBAAiB;AAC7C,UAAI,QAAQ,OAAO,KAAK,MAAM,GAAG,KAAK;AACtC,YAAM,OAAO;AACb,YAAM,kBAAkB;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,aAAY,UAAU,WAAW,WAAY;AACzC,aAAO,oBAAoB,OAAO,KAAK,MAAM,IAAI,EAAE,OAAO,KAAK,OAAO;AAAA,IAC1E;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAEP,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,YAAY,OAAO,SAAS,iBAAiB;AACpE,aAAO,OAAO,KAAK,MAAM,uBAAwB,OAAO,YAAY,MAAQ,EAAE,OAAO,OAAO,kBAAoB,EAAE,OAAO,OAAO,KAAK,OAAO,EAAE,KAAK,MAAM,GAAG,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IACpN;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAEb,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AACzD,cAAUC,wBAAuB,MAAM;AACvC,aAASA,uBAAsB,OAAO,MAAM,iBAAiB;AACzD,aAAO,OAAO,KAAK,MAAM,cAAe,OAAO,OAAO,oBAAqB,EAAE,OAAO,IAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IAC5I;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAEb,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,YAAY,iBAAiB;AACpD,aAAO,OAAO,KAAK,MAAM,qCAAsC,OAAO,YAAY,oCAAsC,EAAE,OAAO,iBAAiB,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IAC1M;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;;;AC5CN,IAAI;AAAA,CACV,SAAUC,YAAW;AAClB,EAAAA,WAAUA,WAAU,SAAS,IAAI,CAAC,IAAI;AACtC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACzC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,SAAS,aAAa,OAAO;AACzB,MAAI,MAAM,SAAS,GAAG;AAClB,WAAO;AAAA,EACX;AACA,SAAO,MAAM,OAAO,SAAU,KAAK,MAAM;AACrC,QAAI,WAAW,IAAI,IAAI,SAAS,CAAC;AACjC,QAAI,CAAC,YACD,SAAS,SAAS,UAAU,WAC5B,KAAK,SAAS,UAAU,SAAS;AACjC,UAAI,KAAK,IAAI;AAAA,IACjB,OACK;AACD,eAAS,SAAS,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACO,SAAS,qBAAqB,IAAI;AACrC,SAAO,OAAO,OAAO;AACzB;AAEO,SAASC,eAAc,KAAK,SAAS,YAAY,SAAS,QAAQ,oBAEzE,iBAAiB;AAEb,MAAI,IAAI,WAAW,KAAK,iBAAiB,IAAI,CAAC,CAAC,GAAG;AAC9C,WAAO;AAAA,MACH;AAAA,QACI,MAAM,UAAU;AAAA,QAChB,OAAO,IAAI,CAAC,EAAE;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,QAAQ,KAAK,KAAK,MAAM,QAAQ,MAAM;AACnD,QAAI,KAAK,MAAM,EAAE;AAEjB,QAAI,iBAAiB,EAAE,GAAG;AACtB,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,GAAG;AAAA,MACd,CAAC;AACD;AAAA,IACJ;AAGA,QAAI,eAAe,EAAE,GAAG;AACpB,UAAI,OAAO,uBAAuB,UAAU;AACxC,eAAO,KAAK;AAAA,UACR,MAAM,UAAU;AAAA,UAChB,OAAO,WAAW,gBAAgB,OAAO,EAAE,OAAO,kBAAkB;AAAA,QACxE,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,QAAI,UAAU,GAAG;AAEjB,QAAI,EAAE,UAAU,WAAW,SAAS;AAChC,YAAM,IAAI,kBAAkB,SAAS,eAAe;AAAA,IACxD;AACA,QAAI,QAAQ,OAAO,OAAO;AAC1B,QAAI,kBAAkB,EAAE,GAAG;AACvB,UAAI,CAAC,SAAS,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAClE,gBACI,OAAO,UAAU,YAAY,OAAO,UAAU,WACxC,OAAO,KAAK,IACZ;AAAA,MACd;AACA,aAAO,KAAK;AAAA,QACR,MAAM,OAAO,UAAU,WAAW,UAAU,UAAU,UAAU;AAAA,QAChE;AAAA,MACJ,CAAC;AACD;AAAA,IACJ;AAIA,QAAI,cAAc,EAAE,GAAG;AACnB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,IACrB,mBAAmB,GAAG,KAAK,IACvB,GAAG,MAAM,gBACT;AACV,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,cAAc,EAAE,GAAG;AACnB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,IACrB,mBAAmB,GAAG,KAAK,IACvB,GAAG,MAAM,gBACT,QAAQ,KAAK;AACvB,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,OAAO,GAAG,KAAK,IACvB,iBAAiB,GAAG,KAAK,IACrB,GAAG,MAAM,gBACT;AACV,UAAI,SAAS,MAAM,OAAO;AACtB,gBACI,SACK,MAAM,SAAS;AAAA,MAC5B;AACA,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,gBAAgB,SAAS,KAAK,EAC9B,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,aAAa,EAAE,GAAG;AAClB,UAAI,WAAW,GAAG,UAAU,UAAU,GAAG;AACzC,UAAI,WAAW,OAAO,OAAO;AAC7B,UAAI,CAAC,qBAAqB,QAAQ,GAAG;AACjC,cAAM,IAAI,sBAAsB,SAAS,YAAY,eAAe;AAAA,MACxE;AACA,UAAI,QAAQA,eAAc,UAAU,SAAS,YAAY,SAAS,QAAQ,kBAAkB;AAC5F,UAAI,SAAS,SAAS,MAAM,IAAI,SAAU,GAAG;AAAE,eAAO,EAAE;AAAA,MAAO,CAAC,CAAC;AACjE,UAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,iBAAS,CAAC,MAAM;AAAA,MACpB;AACA,aAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,SAAU,GAAG;AAC9C,eAAO;AAAA,UACH,MAAM,OAAO,MAAM,WAAW,UAAU,UAAU,UAAU;AAAA,UAC5D,OAAO;AAAA,QACX;AAAA,MACJ,CAAC,CAAC;AAAA,IACN;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,MAAM,GAAG,QAAQ,KAAK,KAAK,GAAG,QAAQ;AAC1C,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,MACzF;AACA,aAAO,KAAK,MAAM,QAAQA,eAAc,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM,CAAC;AACxF;AAAA,IACJ;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,MAAM,GAAG,QAAQ,IAAI,OAAO,KAAK,CAAC;AACtC,UAAI,CAAC,KAAK;AACN,YAAI,CAAC,KAAK,aAAa;AACnB,gBAAM,IAAI,YAAY,mHAAqH,UAAU,kBAAkB,eAAe;AAAA,QAC1L;AACA,YAAI,OAAO,WACN,eAAe,SAAS,EAAE,MAAM,GAAG,WAAW,CAAC,EAC/C,OAAO,SAAS,GAAG,UAAU,EAAE;AACpC,cAAM,GAAG,QAAQ,IAAI,KAAK,GAAG,QAAQ;AAAA,MACzC;AACA,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,MACzF;AACA,aAAO,KAAK,MAAM,QAAQA,eAAc,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,SAAS,GAAG,UAAU,EAAE,CAAC;AAClH;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,aAAa,MAAM;AAC9B;;;ACtKA,SAAS,YAAY,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI;AACL,WAAO;AAAA,EACX;AACA,SAAO,SAAS,SAAS,SAAS,CAAC,GAAI,MAAM,CAAC,CAAE,GAAI,MAAM,CAAC,CAAE,GAAG,OAAO,KAAK,EAAE,EAAE,OAAO,SAAU,KAAK,GAAG;AACrG,QAAI,CAAC,IAAI,SAAS,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAI,GAAG,CAAC,KAAK,CAAC,CAAE;AACpD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,CAAC;AACV;AACA,SAAS,aAAa,eAAe,SAAS;AAC1C,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,SAAO,OAAO,KAAK,aAAa,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,QAAI,CAAC,IAAI,YAAY,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC;AACjD,WAAO;AAAA,EACX,GAAG,SAAS,CAAC,GAAG,aAAa,CAAC;AAClC;AACA,SAAS,uBAAuB,OAAO;AACnC,SAAO;AAAA,IACH,QAAQ,WAAY;AAChB,aAAO;AAAA,QACH,KAAK,SAAU,KAAK;AAChB,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AACvB,gBAAM,GAAG,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,wBAAwB,OAAO;AACpC,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,MAC5B,QAAQ,CAAC;AAAA,MACT,UAAU,CAAC;AAAA,MACX,aAAa,CAAC;AAAA,IAClB;AAAA,EAAG;AACH,SAAO;AAAA,IACH,iBAAiB,QAAQ,WAAY;AACjC,UAAIC;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,cAAc,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC/F,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,MAAM;AAAA,MAC1C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,mBAAmB,QAAQ,WAAY;AACnC,UAAIA;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,gBAAgB,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IACjG,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,QAAQ;AAAA,MAC5C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,gBAAgB,QAAQ,WAAY;AAChC,UAAIA;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,aAAa,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC9F,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,WAAW;AAAA,MAC/C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,EACL;AACJ;AACA,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,SAAS,SAAS,iBAAiB,MAAM;AAChE,UAAI,YAAY,QAAQ;AAAE,kBAAUA,mBAAkB;AAAA,MAAe;AACrE,UAAI,QAAQ;AACZ,WAAK,iBAAiB;AAAA,QAClB,QAAQ,CAAC;AAAA,QACT,UAAU,CAAC;AAAA,QACX,aAAa,CAAC;AAAA,MAClB;AACA,WAAK,SAAS,SAAU,QAAQ;AAC5B,YAAI,QAAQ,MAAM,cAAc,MAAM;AAEtC,YAAI,MAAM,WAAW,GAAG;AACpB,iBAAO,MAAM,CAAC,EAAE;AAAA,QACpB;AACA,YAAI,SAAS,MAAM,OAAO,SAAU,KAAK,MAAM;AAC3C,cAAI,CAAC,IAAI,UACL,KAAK,SAAS,UAAU,WACxB,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,UAAU;AACzC,gBAAI,KAAK,KAAK,KAAK;AAAA,UACvB,OACK;AACD,gBAAI,IAAI,SAAS,CAAC,KAAK,KAAK;AAAA,UAChC;AACA,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AACL,YAAI,OAAO,UAAU,GAAG;AACpB,iBAAO,OAAO,CAAC,KAAK;AAAA,QACxB;AACA,eAAO;AAAA,MACX;AACA,WAAK,gBAAgB,SAAU,QAAQ;AACnC,eAAOC,eAAc,MAAM,KAAK,MAAM,SAAS,MAAM,YAAY,MAAM,SAAS,QAAQ,QAAW,MAAM,OAAO;AAAA,MACpH;AACA,WAAK,kBAAkB,WAAY;AAC/B,YAAIF;AACJ,eAAQ;AAAA,UACJ,UAAUA,MAAK,MAAM,oBAAoB,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,MAClF,KAAK,aAAa,mBAAmB,MAAM,OAAO,EAAE,CAAC;AAAA,QAC7D;AAAA,MACJ;AACA,WAAK,SAAS,WAAY;AAAE,eAAO,MAAM;AAAA,MAAK;AAE9C,WAAK,UAAU;AACf,WAAK,iBAAiBC,mBAAkB,cAAc,OAAO;AAC7D,UAAI,OAAO,YAAY,UAAU;AAC7B,aAAK,UAAU;AACf,YAAI,CAACA,mBAAkB,SAAS;AAC5B,gBAAM,IAAI,UAAU,6EAA6E;AAAA,QACrG;AACA,YAAID,MAAK,QAAQ,CAAC,GAAG,aAAaA,IAAG,YAAY,YAAY,OAAOA,KAAI,CAAC,YAAY,CAAC;AAEtF,aAAK,MAAMC,mBAAkB,QAAQ,SAAS,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,QAAQ,KAAK,eAAe,CAAC,CAAC;AAAA,MACpH,OACK;AACD,aAAK,MAAM;AAAA,MACf;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,GAAG;AAC1B,cAAM,IAAI,UAAU,gDAAgD;AAAA,MACxE;AAGA,WAAK,UAAU,aAAaA,mBAAkB,SAAS,eAAe;AACtE,WAAK,aACA,QAAQ,KAAK,cAAe,wBAAwB,KAAK,cAAc;AAAA,IAChF;AACA,WAAO,eAAeA,oBAAmB,iBAAiB;AAAA,MACtD,KAAK,WAAY;AACb,YAAI,CAACA,mBAAkB,uBAAuB;AAC1C,UAAAA,mBAAkB,wBACd,IAAI,KAAK,aAAa,EAAE,gBAAgB,EAAE;AAAA,QAClD;AACA,eAAOA,mBAAkB;AAAA,MAC7B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,wBAAwB;AAC1C,IAAAA,mBAAkB,gBAAgB,SAAU,SAAS;AACjD,UAAI,OAAO,KAAK,WAAW,aAAa;AACpC;AAAA,MACJ;AACA,UAAI,mBAAmB,KAAK,aAAa,mBAAmB,OAAO;AACnE,UAAI,iBAAiB,SAAS,GAAG;AAC7B,eAAO,IAAI,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,MAC9C;AACA,aAAO,IAAI,KAAK,OAAO,OAAO,YAAY,WAAW,UAAU,QAAQ,CAAC,CAAC;AAAA,IAC7E;AACA,IAAAA,mBAAkB,UAAU;AAI5B,IAAAA,mBAAkB,UAAU;AAAA,MACxB,QAAQ;AAAA,QACJ,SAAS;AAAA,UACL,uBAAuB;AAAA,QAC3B;AAAA,QACA,UAAU;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA,SAAS;AAAA,UACL,OAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO;AAAA,UACH,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACJ,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACF,SAAS;AAAA,UACT,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACZ;AAAA,QACA,MAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;AC3OK,IAAI;AAAA,CACV,SAAUE,gBAAe;AACtB,EAAAA,eAAc,cAAc,IAAI;AAChC,EAAAA,eAAc,uBAAuB,IAAI;AACzC,EAAAA,eAAc,gBAAgB,IAAI;AAClC,EAAAA,eAAc,cAAc,IAAI;AAChC,EAAAA,eAAc,qBAAqB,IAAI;AAC3C,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,WAAU,MAAM,SAAS,WAAW;AACzC,UAAI,QAAQ;AACZ,UAAI,MAAM,YACJ,qBAAqB,QACjB,YACA,IAAI,MAAM,OAAO,SAAS,CAAC,IAC/B;AACN,cAAQ,OAAO,KAAK,MAAM,yBAAyB,OAAO,MAAM,IAAI,EAAE,OAAO,SAAS,IAAI,EAAE,OAAO,MAAM,KAAK,OAAO,IAAI,SAAS,IAAI,EAAE,OAAO,IAAI,KAAK,IAAI,EAAE,CAAC,KAAK;AACpK,YAAM,OAAO;AAEb,UAAI,OAAO,MAAM,sBAAsB,YAAY;AAE/C,cAAM,kBAAkB,OAAOA,UAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAEP,IAAI;AAAA;AAAA,EAA2C,SAAU,QAAQ;AAC7D,cAAUC,4BAA2B,MAAM;AAC3C,aAASA,2BAA0B,SAAS,WAAW;AACnD,aAAO,OAAO,KAAK,MAAM,cAAc,uBAAuB,SAAS,SAAS,KAAK;AAAA,IACzF;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUC,qBAAoB,MAAM;AACpC,aAASA,oBAAmB,SAAS,WAAW;AAC5C,aAAO,OAAO,KAAK,MAAM,cAAc,gBAAgB,SAAS,SAAS,KAAK;AAAA,IAClF;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAkC,SAAU,QAAQ;AACpD,cAAUC,mBAAkB,MAAM;AAClC,aAASA,kBAAiB,SAAS,WAAW;AAC1C,aAAO,OAAO,KAAK,MAAM,cAAc,cAAc,SAAS,SAAS,KAAK;AAAA,IAChF;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACnD,cAAUC,kBAAiB,MAAM;AACjC,aAASA,iBAAgB,SAAS,QAAQ,WAAW;AACjD,UAAI,QAAQ,OAAO,KAAK,MAAM,cAAc,cAAc,GAAG,OAAO,SAAS,YAAY,EAAE,OAAO,QAAQ,IAAI,GAAG,SAAS,KAAK;AAC/H,YAAM,SAAS;AACf,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUC,qBAAoB,MAAM;AACpC,aAASA,oBAAmB,SAAS,QAAQ,YAAY,WAAW;AAChE,UAAI,QAAQ,OAAO,KAAK,MAAM,GAAG,OAAO,SAAS,eAAe,EAAE,OAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,IAAI,qBAAqB,EAAE,OAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,gBAAgB,iBAAiB,EAAE,OAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,aAAa,IAAI,GAAG,QAAQ,SAAS,KAAK;AACxY,YAAM,aAAa;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,eAAe;AAAA;AAEjB,IAAI;AAAA;AAAA,EAAyC,SAAU,QAAQ;AAC3D,cAAUC,0BAAyB,MAAM;AACzC,aAASA,yBAAwB,YAAY,QAAQ;AACjD,UAAI,QAAQ,OAAO,KAAK,MAAM,cAAc,qBAAqB,qBAAsB,OAAO,WAAW,IAAI,gBAAkB,EAAE,OAAO,QAAQ,WAAY,EAAE,OAAO,WAAW,iBAC1K,oBAAoB,OAAO,OAAO,WAAW,mBAAmB,WAC5D,WAAW,iBACX,WAAW,eACR,IAAI,SAAU,GAAG;AAAE,YAAIC;AAAI,gBAAQA,MAAK,EAAE,WAAW,QAAQA,QAAO,SAASA,MAAK,KAAK,UAAU,CAAC;AAAA,MAAG,CAAC,EACtG,KAAK,GAAG,GAAG,IAClB,MAAM,eAAe,CAAC,KAAK;AACjC,YAAM,aAAa;AACnB,aAAO;AAAA,IACX;AACA,WAAOD;AAAA,EACX,EAAE,SAAS;AAAA;;;ACrFJ,SAAS,YAAY,OAAO,WAAW,UAAU;AACpD,MAAI,aAAa,QAAQ;AAAE,eAAW,CAAC;AAAA,EAAG;AAC1C,SAAO,UAAU,OAAO,SAAU,UAAU,MAAM;AAC9C,QAAI,QAAQ,OAAO;AACf,eAAS,IAAI,IAAI,MAAM,IAAI;AAAA,IAC/B,WACS,QAAQ,UAAU;AACvB,eAAS,IAAI,IAAI,SAAS,IAAI;AAAA,IAClC;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,IAAI,sBAAsB,SAAU,OAAO;AAEvC,MAAI,MAAuC;AACvC,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,IAAI,qBAAqB,SAAU,SAAS;AAExC,MAAI,MAAuC;AACvC,YAAQ,KAAK,OAAO;AAAA,EACxB;AACJ;AACO,IAAI,sBAAsB;AAAA,EAC7B,SAAS,CAAC;AAAA,EACV,UAAU,CAAC;AAAA,EACX,UAAU;AAAA,EACV,eAAe;AAAA,EACf,gBAAgB,CAAC;AAAA,EACjB,uBAAuB;AAAA,EACvB,SAAS;AAAA,EACT,QAAQ;AACZ;AACO,SAAS,kBAAkB;AAC9B,SAAO;AAAA,IACH,UAAU,CAAC;AAAA,IACX,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,IACd,MAAM,CAAC;AAAA,IACP,cAAc,CAAC;AAAA,EACnB;AACJ;AACA,SAASE,wBAAuB,OAAO;AACnC,SAAO;AAAA,IACH,QAAQ,WAAY;AAChB,aAAO;AAAA,QACH,KAAK,SAAU,KAAK;AAChB,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AACvB,gBAAM,GAAG,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAKO,SAAS,iBAAiB,OAAO;AACpC,MAAI,UAAU,QAAQ;AAAE,YAAQ,gBAAgB;AAAA,EAAG;AACnD,MAAI,qBAAqB,KAAK;AAC9B,MAAI,aAAa,KAAK;AACtB,MAAI,eAAe,KAAK;AACxB,MAAI,oBAAoB,QAAQ,WAAY;AACxC,QAAIC;AACJ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,MAAMA,MAAK,KAAK,gBAAgB,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,EACjG,GAAG;AAAA,IACC,OAAOD,wBAAuB,MAAM,QAAQ;AAAA,IAC5C,UAAU,WAAW;AAAA,EACzB,CAAC;AACD,MAAI,kBAAkB,QAAQ,WAAY;AACtC,QAAIC;AACJ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,MAAMA,MAAK,KAAK,cAAc,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,EAC/F,GAAG;AAAA,IACC,OAAOD,wBAAuB,MAAM,MAAM;AAAA,IAC1C,UAAU,WAAW;AAAA,EACzB,CAAC;AACD,MAAI,iBAAiB,QAAQ,WAAY;AACrC,QAAIC;AACJ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,MAAMA,MAAK,KAAK,aAAa,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,EAC9F,GAAG;AAAA,IACC,OAAOD,wBAAuB,MAAM,WAAW;AAAA,IAC/C,UAAU,WAAW;AAAA,EACzB,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,kBAAkB,QAAQ,SAAU,SAAS,SAAS,iBAAiB,MAAM;AACzE,aAAO,IAAI,kBAAkB,SAAS,SAAS,iBAAiB,SAAS,EAAE,YAAY;AAAA,QAC/E;AAAA,QACA;AAAA,QACA;AAAA,MACJ,EAAE,GAAI,QAAQ,CAAC,CAAE,CAAC;AAAA,IAC1B,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,OAAO;AAAA,MAC3C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,uBAAuB,QAAQ,WAAY;AACvC,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,KAAK,mBAAmB,KAAK,MAAM,oBAAoB,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IACzG,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,YAAY;AAAA,MAChD,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD;AAAA,IACA,eAAe,QAAQ,WAAY;AAC/B,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,KAAK,WAAW,KAAK,MAAM,YAAY,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IACzF,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,IAAI;AAAA,MACxC,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,iBAAiB,QAAQ,WAAY;AACjC,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,KAAK,aAAa,KAAK,MAAM,cAAc,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC7F,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,YAAY;AAAA,MAChD,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,EACL;AACJ;AACO,SAAS,eAAe,SAAS,MAAM,MAAM,SAAS;AACzD,MAAI,aAAa,WAAW,QAAQ,IAAI;AACxC,MAAI;AACJ,MAAI,YAAY;AACZ,aAAS,WAAW,IAAI;AAAA,EAC5B;AACA,MAAI,QAAQ;AACR,WAAO;AAAA,EACX;AACA,UAAQ,IAAI,0BAA0B,MAAM,OAAO,MAAM,iBAAiB,EAAE,OAAO,IAAI,CAAC,CAAC;AAC7F;;;AC5JA,SAAS,qBAAqB,MAAM,UAAU;AAC1C,SAAO,OAAO,KAAK,IAAI,EAAE,OAAO,SAAU,KAAK,GAAG;AAC9C,QAAI,CAAC,IAAI,SAAS,EAAE,SAAmB,GAAG,KAAK,CAAC,CAAC;AACjD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,SAAS,iBAAiB,OAAO,OAAO;AACpC,MAAI,OAAO,OAAO,KAAK,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AAC3D,SAAO,KAAK,OAAO,SAAU,KAAK,GAAG;AACjC,QAAI,CAAC,IAAI,SAAS,SAAS,CAAC,GAAI,MAAM,CAAC,KAAK,CAAC,CAAE,GAAI,MAAM,CAAC,KAAK,CAAC,CAAE;AAClE,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,SAAS,+BAA+B,IAAI,UAAU;AAClD,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,MAAI,YAAY,kBAAkB;AAClC,SAAO,SAAS,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,iBAAiB,qBAAqB,UAAU,MAAM,QAAQ,GAAG,qBAAqB,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,MAAM,iBAAiB,qBAAqB,UAAU,MAAM,QAAQ,GAAG,qBAAqB,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC3S;AACO,IAAI,gBAAgB,SAAUE,KAAI,OAAO,mBAAmB,QAAQ,MAAM;AAC7E,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,WAAWA,IAAG,UAAU,gBAAgBA,IAAG,eAAe,iBAAiBA,IAAG,gBAAgB,wBAAwBA,IAAG,uBAAuB,UAAUA,IAAG,SAAS,WAAWA,IAAG,UAAU,0BAA0BA,IAAG;AACzQ,MAAI,sBAAsB,QAAQ;AAAE,wBAAoB,EAAE,IAAI,GAAG;AAAA,EAAG;AACpE,MAAI,QAAQ,kBAAkB,IAAI,iBAAiB,kBAAkB;AAErE,YAAU,CAAC,CAAC,OAAO,oaAAoa;AACvb,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI;AAAA;AAAA;AAAA;AAAA,IAIJ,YACI,OAAO,UAAU,eAAe,KAAK,UAAU,EAAE,KACjD,SAAS,EAAE;AAAA;AAEf,MAAI,MAAM,QAAQ,OAAO,KACrB,QAAQ,WAAW,KACnB,QAAQ,CAAC,EAAE,SAAS,KAAK,SAAS;AAClC,WAAO,QAAQ,CAAC,EAAE;AAAA,EACtB;AAEA,MAAI,CAAC,UACD,WACA,OAAO,YAAY,YACnB,CAAC,yBAAyB;AAC1B,WAAO,QAAQ,QAAQ,iBAAiB,MAAM;AAAA,EAClD;AACA,WAAS,SAAS,SAAS,CAAC,GAAG,uBAAuB,GAAI,UAAU,CAAC,CAAE;AACvE,YAAU,+BAA+B,SAAS,QAAQ;AAC1D,mBAAiB,+BAA+B,gBAAgB,QAAQ;AACxE,MAAI,CAAC,SAAS;AACV,QAAI,0BAA0B,SAAS,YAAY,IAAI;AACnD,aAAO;AAAA,IACX;AACA,QAAI,CAAC,kBACA,UAAU,OAAO,YAAY,MAAM,cAAc,YAAY,GAAI;AAIlE,cAAQ,IAAI,wBAAwB,mBAAmB,MAAM,CAAC;AAAA,IAClE;AACA,QAAI,gBAAgB;AAChB,UAAI;AACA,YAAI,YAAY,MAAM,iBAAiB,gBAAgB,eAAe,gBAAgB,IAAI;AAC1F,eAAO,UAAU,OAAO,MAAM;AAAA,MAClC,SACO,GAAG;AACN,gBAAQ,IAAI,mBAAmB,0CAA2C,OAAO,IAAI,uCAAwC,GAAG,QAAQ,mBAAmB,CAAC,CAAC;AAC7J,eAAO,OAAO,mBAAmB,WAAW,iBAAiB;AAAA,MACjE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAEA,MAAI;AACA,QAAI,YAAY,MAAM,iBAAiB,SAAS,QAAQ,SAAS,SAAS,EAAE,YAAY,MAAM,GAAI,QAAQ,CAAC,CAAE,CAAC;AAC9G,WAAO,UAAU,OAAO,MAAM;AAAA,EAClC,SACO,GAAG;AACN,YAAQ,IAAI,mBAAmB,8BAA+B,OAAO,IAAI,WAAY,EAAE,OAAO,iBAAiB,oBAAoB,MAAM,eAAe,GAAG,QAAQ,mBAAmB,CAAC,CAAC;AAAA,EAC5L;AACA,MAAI,gBAAgB;AAChB,QAAI;AACA,UAAI,YAAY,MAAM,iBAAiB,gBAAgB,eAAe,gBAAgB,IAAI;AAC1F,aAAO,UAAU,OAAO,MAAM;AAAA,IAClC,SACO,GAAG;AACN,cAAQ,IAAI,mBAAmB,8CAA+C,OAAO,IAAI,+BAAgC,GAAG,QAAQ,mBAAmB,CAAC,CAAC;AAAA,IAC7J;AAAA,EACJ;AACA,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,mBAAmB,UAAU;AACpC,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACnGA,IAAI,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AACJ;AACO,SAAS,aAAaC,KAAI,MAAM,mBAAmB,SAAS;AAC/D,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,UAAUA,IAAG,SAAS,WAAWA,IAAG;AAClF,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAW,SAAS,SAAS,CAAC,GAAI,YAAY,EAAE,SAAmB,CAAE,GAAI,UAAU,eAAe,SAAS,MAAM,QAAQ,OAAO,CAAE;AACtI,MAAI,kBAAkB,YAAY,SAAS,0BAA0B,QAAQ;AAC7E,MAAI,SAAS,UACT,CAAC,gBAAgB,QACjB,CAAC,gBAAgB,UACjB,CAAC,gBAAgB,UACjB,CAAC,gBAAgB,aACjB,CAAC,gBAAgB,WAAW;AAE5B,sBAAkB,SAAS,SAAS,CAAC,GAAG,eAAe,GAAG,EAAE,MAAM,WAAW,QAAQ,UAAU,CAAC;AAAA,EACpG;AACA,SAAO,kBAAkB,QAAQ,eAAe;AACpD;AACO,SAAS,WAAW,QAAQ,mBAAmB;AAClD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,OAAO,IAAI;AAAA,EAC/E,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,OAAO,IAAI;AACtB;AACO,SAAS,WAAW,QAAQ,mBAAmB;AAClD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,OAAO,IAAI;AAAA,EAC/E,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,OAAO,IAAI;AACtB;AACO,SAAS,oBAAoB,QAAQ,mBAAmB;AAC3D,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,OAAOA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AACzE,MAAI,WAAW,OAAO,UAAU,SAAS,OAAO,QAAQ,UAAU,OAAO;AACzE,MAAI,kBAAkB,YAAY,SAAS,0BAA0B,WAAW,EAAE,SAAmB,IAAI,CAAC,CAAC;AAC3G,MAAI;AACA,WAAO,kBAAkB,QAAQ,eAAe,EAAE,YAAY,MAAM,EAAE;AAAA,EAC1E,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,qCAAqC,OAAO,QAAQ,CAAC,CAAC;AAAA,EACtF;AACA,SAAO,OAAO,IAAI;AACtB;AACO,SAAS,kBAAkB,QAAQ,mBAAmB;AACzD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,cAAc,IAAI;AAAA,EACtF,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,CAAC;AACZ;AACO,SAAS,kBAAkB,QAAQ,mBAAmB;AACzD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,cAAc,IAAI;AAAA,EACtF,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,CAAC;AACZ;;;ACjHA,IAAI,uBAAuB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,SAAS,kBAAkBC,KAAI,iBAAiB,OAAO,SAAS;AACnE,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG;AACrC,MAAI,eAAe,KAAK;AACxB,MAAI,CAAC,cAAc;AACf,YAAQ,IAAI,YAAY,qHAAuH,UAAU,gBAAgB,CAAC;AAAA,EAC9K;AACA,MAAI,kBAAkB,YAAY,SAAS,oBAAoB;AAC/D,MAAI;AACA,WAAO,gBAAgB,QAAQ,eAAe,EAAE,GAAG,KAAK;AAAA,EAC5D,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,kCAAkC,QAAQ,CAAC,CAAC;AAAA,EAC5E;AACJ;;;AClBA,IAAI,sBAAsB;AAAA,EACtB;AAAA,EACA;AACJ;AACA,IAAI,MAAM,KAAK,IAAI;AACnB,SAAS,cAAc,GAAG;AACtB,SAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG;AACxD;AACO,SAAS,WAAW,MAAM,eAAe,QAAQ,SAAS;AAC7D,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,UAAU,kBAAkB,MAAM,eAAe,QAAQ,OAAO,EAAE,OAAO,SAAU,KAAK,IAAI;AAC5F,QAAI,MAAM,GAAG;AACb,QAAI,OAAO,QAAQ,UAAU;AACzB,UAAI,KAAK,GAAG;AAAA,IAChB,WACS,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,UAAU;AAC9C,UAAI,IAAI,SAAS,CAAC,KAAK;AAAA,IAC3B,OACK;AACD,UAAI,KAAK,GAAG;AAAA,IAChB;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO,QAAQ,WAAW,IAAI,QAAQ,CAAC,IAAI,QAAQ,WAAW,IAAI,KAAK;AAC3E;AACO,SAAS,kBAAkBC,KAAI,eAAe,QAAQ,SAAS;AAClE,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG;AACrC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,aAAa,KAAK;AACtB,MAAI,CAAC,YAAY;AACb,YAAQ,IAAI,YAAY,iHAAmH,UAAU,gBAAgB,CAAC;AAAA,EAC1K;AACA,MAAI,kBAAkB,YAAY,SAAS,mBAAmB;AAC9D,MAAI;AACA,QAAI,eAAe,CAAC;AACpB,QAAI,mBAAmB,OAAO,IAAI,SAAU,GAAG,GAAG;AAC9C,UAAI,OAAO,MAAM,UAAU;AACvB,YAAI,KAAK,cAAc,CAAC;AACxB,qBAAa,EAAE,IAAI;AACnB,eAAO;AAAA,MACX;AACA,aAAO,OAAO,CAAC;AAAA,IACnB,CAAC;AACD,WAAO,cAAc,QAAQ,eAAe,EACvC,cAAc,gBAAgB,EAC9B,IAAI,SAAU,MAAM;AACrB,aAAO,KAAK,SAAS,YACf,OACA,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,aAAa,KAAK,KAAK,KAAK,KAAK,MAAM,CAAC;AAAA,IACxF,CAAC;AAAA,EACL,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,0BAA0B,QAAQ,CAAC,CAAC;AAAA,EACpE;AAEA,SAAO;AACX;;;ACzDA,IAAI,wBAAwB,CAAC,MAAM;AAC5B,SAAS,aAAaC,KAAI,gBAAgB,OAAO,SAAS;AAC7D,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG;AACrC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,CAAC,KAAK,aAAa;AACnB,YAAQ,IAAI,YAAY,mHAAqH,UAAU,gBAAgB,CAAC;AAAA,EAC5K;AACA,MAAI,kBAAkB,YAAY,SAAS,qBAAqB;AAChE,MAAI;AACA,WAAO,eAAe,QAAQ,eAAe,EAAE,OAAO,KAAK;AAAA,EAC/D,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,4BAA4B,QAAQ,CAAC,CAAC;AAAA,EACtE;AACA,SAAO;AACX;;;ACfA,IAAI,+BAA+B,CAAC,WAAW,OAAO;AACtD,SAASC,cAAaC,KAAI,uBAAuB,SAAS;AACtD,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,UAAUA,IAAG;AAC3D,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAY,CAAC,CAAC,UAAU,eAAe,SAAS,YAAY,QAAQ,OAAO,KAAM,CAAC;AACtF,MAAI,kBAAkB,YAAY,SAAS,8BAA8B,QAAQ;AACjF,SAAO,sBAAsB,QAAQ,eAAe;AACxD;AACO,SAAS,mBAAmB,QAAQ,uBAAuB,OAAO,MAAM,SAAS;AACpF,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,MAAI,qBAAqB,KAAK;AAC9B,MAAI,CAAC,oBAAoB;AACrB,WAAO,QAAQ,IAAI,YAAY,iIAAmI,UAAU,gBAAgB,CAAC;AAAA,EACjM;AACA,MAAI;AACA,WAAOD,cAAa,QAAQ,uBAAuB,OAAO,EAAE,OAAO,OAAO,IAAI;AAAA,EAClF,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,mCAAmC,OAAO,QAAQ,CAAC,CAAC;AAAA,EAC3F;AACA,SAAO,OAAO,KAAK;AACvB;;;AC1BA,IAAI,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,SAASE,cAAaC,KAAI,iBAAiB,SAAS;AACvD,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,UAAUA,IAAG;AAC3D,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAa,UACb,eAAe,SAAS,UAAU,QAAQ,OAAO,KACjD,CAAC;AACL,MAAI,kBAAkB,YAAY,SAAS,uBAAuB,QAAQ;AAC1E,SAAO,gBAAgB,QAAQ,eAAe;AAClD;AACO,SAAS,aAAa,QAAQ,iBAAiB,OAAO,SAAS;AAClE,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI;AACA,WAAOD,cAAa,QAAQ,iBAAiB,OAAO,EAAE,OAAO,KAAK;AAAA,EACtE,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,4BAA4B,OAAO,QAAQ,CAAC,CAAC;AAAA,EACpF;AACA,SAAO,OAAO,KAAK;AACvB;AACO,SAAS,oBAAoB,QAAQ,iBAAiB,OAAO,SAAS;AACzE,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI;AACA,WAAOA,cAAa,QAAQ,iBAAiB,OAAO,EAAE,cAAc,KAAK;AAAA,EAC7E,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,4BAA4B,OAAO,QAAQ,CAAC,CAAC;AAAA,EACpF;AACA,SAAO,CAAC;AACZ;;;AC/CA,SAAS,sBAAsB,UAAU;AACrC,MAAI,eAAe,WAAW,SAAS,OAAO,KAAK,QAAQ,EAAE,CAAC,CAAC,IAAI;AACnE,SAAO,OAAO,iBAAiB;AACnC;AACA,SAAS,qBAAqB,QAAQ;AAClC,MAAI,OAAO,UACP,OAAO,2BACP,sBAAsB,OAAO,YAAY,CAAC,CAAC,GAAG;AAC9C,WAAO,OAAO,wQAA8Q;AAAA,EAChS;AACJ;AAMO,SAAS,WAAW,QAAQ,OAAO;AACtC,MAAI,aAAa,iBAAiB,KAAK;AACvC,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,mBAAmB,GAAG,MAAM;AACvE,MAAI,SAAS,eAAe,QAAQ,gBAAgB,eAAe,eAAe,UAAU,eAAe;AAC3G,MAAI,CAAC,QAAQ;AACT,QAAI,SAAS;AACT,cAAQ,IAAI,mBAAmB,uCAA0C,OAAO,eAAe,uFAAwF,CAAC,CAAC;AAAA,IAC7L;AAMA,mBAAe,SAAS,eAAe,iBAAiB;AAAA,EAC5D,WACS,CAAC,KAAK,aAAa,mBAAmB,MAAM,EAAE,UAAU,SAAS;AACtE,YAAQ,IAAI,iBAAiB,oCAAqC,OAAO,QAAQ,iDAAmD,EAAE,OAAO,eAAe,8FAA+F,CAAC,CAAC;AAAA,EACjQ,WACS,CAAC,KAAK,eAAe,mBAAmB,MAAM,EAAE,UACrD,SAAS;AACT,YAAQ,IAAI,iBAAiB,oCAAqC,OAAO,QAAQ,mDAAqD,EAAE,OAAO,eAAe,8FAA+F,CAAC,CAAC;AAAA,EACnQ;AACA,uBAAqB,cAAc;AACnC,SAAO,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG;AAAA,IAAE;AAAA,IAAwB,cAAc,aAAa,KAAK,MAAM,gBAAgB,WAAW,eAAe;AAAA,IAAG,qBAAqB,oBAAoB,KAAK,MAAM,gBAAgB,WAAW,eAAe;AAAA,IAAG,oBAAoB,mBAAmB,KAAK,MAAM,gBAAgB,WAAW,qBAAqB;AAAA,IAAG,YAAY,WAAW,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,YAAY,WAAW,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,qBAAqB,oBAAoB,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,cAAc,aAAa,KAAK,MAAM,gBAAgB,WAAW,cAAc;AAAA;AAAA,IAE/2B,eAAe,cAAc,KAAK,MAAM,gBAAgB,UAAU;AAAA;AAAA,IAElE,IAAI,cAAc,KAAK,MAAM,gBAAgB,UAAU;AAAA,IAAG,YAAY,WAAW,KAAK,MAAM,gBAAgB,WAAW,aAAa;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,aAAa;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,eAAe;AAAA,EAAE,CAAC;AACvU;;;A/BlDO,SAAS,qBAAqB,MAAM;AACvC,YAAU,MAAM,8GAC8C;AAClE;AACO,IAAIE,uBAAsB,SAAS,SAAS,CAAC,GAAG,mBAAwB,GAAG,EAAE,eAAqB,eAAS,CAAC;AAO5G,SAAS,wBAAwB,oBAAoB;AACxD,SAAO,SAAU,OAAO;AAEpB,WAAO,mBAAyB,eAAS,QAAQ,KAAK,CAAC;AAAA,EAC3D;AACJ;AACO,SAAS,aAAa,MAAM,MAAM;AACrC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,MAAI,CAAC,QAAQ,CAAC,MAAM;AAChB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,WAAW,KAAK;AACtB,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,QAAI,MAAM,MAAM,CAAC;AACjB,QAAI,KAAK,GAAG,MAAM,KAAK,GAAG,KACtB,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AAClD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;AgCzCA,qCAAiC;AACjC,IAAAC,SAAuB;AAEvB,SAAS,eAAe,WAAW;AAC/B,SAAO,UAAU,eAAe,UAAU,QAAQ;AACtD;AAGA,IAAI,cAAc,OAAO,WAAW,eAAe,CAAC,OAAO,uCACrD,OAAO,2BACJ,OAAO,yBAA+B,qBAAc,IAAI,KACrD,qBAAc,IAAI;AAC9B,IAAI,eAAe,YAAY;AAA/B,IAAyC,eAAe,YAAY;AAC7D,IAAI,WAAW;AACf,IAAI,UAAU;AACN,SAAR,WAA4B,kBAAkB,SAAS;AAC1D,MAAIC,MAAK,WAAW,CAAC,GAAG,KAAKA,IAAG,cAAc,eAAe,OAAO,SAAS,SAAS,IAAI,KAAKA,IAAG,YAAYC,cAAa,OAAO,SAAS,QAAQ,IAAI,KAAKD,IAAG,gBAAgB,iBAAiB,OAAO,SAAS,OAAO;AACvN,MAAI,WAAW,SAAU,OAAO;AAAE,WAAc,qBAAc,cAAc,MAAM,SAAU,MAAM;AAC9F,UAAIA;AACJ,UAAI,gBAAgB;AAChB,6BAAqB,IAAI;AAAA,MAC7B;AACA,UAAI,YAAYA,MAAK,CAAC,GAAGA,IAAG,YAAY,IAAI,MAAMA;AAClD,aAAc,qBAAc,kBAAkB,SAAS,CAAC,GAAG,OAAO,UAAU,EAAE,KAAKC,cAAa,MAAM,eAAe,KAAK,CAAC,CAAC;AAAA,IAChI,CAAC;AAAA,EAAI;AACL,WAAS,cAAc,cAAc,OAAO,eAAe,gBAAgB,GAAG,GAAG;AACjF,WAAS,mBAAmB;AAC5B,MAAIA,aAAY;AACZ,eAAO,+BAAAC;AAAA;AAAA,MAED,kBAAW,SAAU,OAAO,KAAK;AAAE,eAAc,qBAAc,UAAU,SAAS,CAAC,GAAG,OAAO,EAAE,cAAc,IAAI,CAAC,CAAC;AAAA,MAAI,CAAC;AAAA,MAAG;AAAA,IAAgB;AAAA,EACrJ;AACA,aAAO,+BAAAA,SAAqB,UAAU,gBAAgB;AAC1D;;;AjC/Be,SAAR,UAA2B;AAC9B,MAAI,OAAa,kBAAW,OAAO;AACnC,uBAAqB,IAAI;AACzB,SAAO;AACX;;;ADJA,IAAI;AAAA,CACH,SAAUC,cAAa;AACpB,EAAAA,aAAY,YAAY,IAAI;AAC5B,EAAAA,aAAY,YAAY,IAAI;AAC5B,EAAAA,aAAY,cAAc,IAAI;AAC9B,EAAAA,aAAY,YAAY,IAAI;AAG5B,EAAAA,aAAY,mBAAmB,IAAI;AACvC,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAI;AAAA,CACH,SAAUC,mBAAkB;AACzB,EAAAA,kBAAiB,YAAY,IAAI;AACjC,EAAAA,kBAAiB,YAAY,IAAI;AACjC,EAAAA,kBAAiB,cAAc,IAAI;AACnC,EAAAA,kBAAiB,YAAY,IAAI;AACrC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI,uBAAuB,SAAU,OAAO;AAC/C,MAAI,OAAO,QAAQ;AACnB,MAAI,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,cAAc,OAAO,OAAO,CAAC,SAAS,UAAU,CAAC;AACrG,SAAO,SAAS,KAAK,oBAAoB,OAAO,WAAW,CAAC;AAChE;AACA,qBAAqB,cAAc;AAC5B,IAAI,qBAAqB,SAAU,OAAO;AAC7C,MAAI,OAAO,QAAQ;AACnB,MAAI,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,cAAc,OAAO,OAAO,CAAC,SAAS,UAAU,CAAC;AACrG,SAAO,SAAS,KAAK,kBAAkB,OAAO,WAAW,CAAC;AAC9D;AACA,qBAAqB,cAAc;AAC5B,SAAS,sCAAsC,MAAM;AACxD,MAAI,iBAAiB,SAAU,OAAO;AAClC,QAAI,OAAO,QAAQ;AACnB,QAAI,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,cAAc,OAAO,OAAO,CAAC,SAAS,UAAU,CAAC;AACrG,QAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,QAAI,iBAAiB,SAAS,eACxB,KAAK,kBAAkB,MAAM,WAAW,IACxC,KAAK,kBAAkB,MAAM,WAAW;AAC9C,WAAO,SAAS,cAAc;AAAA,EAClC;AACA,iBAAe,cAAc,iBAAiB,IAAI;AAClD,SAAO;AACX;AACO,SAAS,yBAAyB,MAAM;AAC3C,MAAI,YAAY,SAAU,OAAO;AAC7B,QAAI,OAAO,QAAQ;AACnB,QAAI,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,cAAc;AAAA,MAAO;AAAA,MAEvE,CAAC,SAAS,UAAU;AAAA,IAAC;AAEvB,QAAI,iBAAiB,KAAK,IAAI,EAAE,OAAO,WAAW;AAClD,QAAI,OAAO,aAAa,YAAY;AAChC,aAAO,SAAS,cAAc;AAAA,IAClC;AACA,QAAI,OAAO,KAAK,iBAAuB;AACvC,WAAa,qBAAc,MAAM,MAAM,cAAc;AAAA,EACzD;AACA,YAAU,cAAc,YAAY,IAAI;AACxC,SAAO;AACX;;;AmCtDA,IAAAC,SAAuB;;;ACAvB,IAAAC,SAAuB;AAGvB,SAAS,6CAA6C,QAAQ;AAC1D,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,GAAG;AAChD,QAAI,IAAI,OAAO,CAAC;AAChB,QAAI,CAAC,IAAI,qBAAqB,CAAC,IACzB,wBAAwB,CAAC,IACzB;AACN,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,IAAIC,iBAAgB,SAAU,QAAQ,YAAY,YAAY,WAAW;AACrE,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC/B;AACA,MAAI,SAAS,6CAA6C,SAAS;AACnE,MAAI,SAAS,cAAkB,MAAM,QAAQ,cAAc;AAAA,IAAC;AAAA,IACxD;AAAA,IACA;AAAA,IACA;AAAA,EAAM,GAAG,MAAM,KAAK,CAAC;AACzB,MAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,WAAa,gBAAS,QAAQ,MAAM;AAAA,EACxC;AACA,SAAO;AACX;AAMO,IAAIC,cAAa,SAAUC,KAAI,OAAO;AACzC,MAAI,6BAA6BA,IAAG,yBAAyB,SAAS,OAAOA,KAAI,CAAC,yBAAyB,CAAC;AAC5G,MAAI,0BAA0B,6CAA6C,0BAA0B;AACrG,MAAI,WAAW,WAAe,SAAS,SAAS,SAAS,CAAC,GAAGC,oBAAmB,GAAG,MAAM,GAAG,EAAE,wBAAiD,CAAC,GAAG,KAAK;AACxJ,MAAI,iBAAiB;AAAA,IACjB,QAAQ,SAAS;AAAA,IACjB,UAAU,SAAS;AAAA,IACnB,uBAAuB,SAAS;AAAA,IAChC,SAAS,SAAS;AAAA,IAClB,eAAe,SAAS;AAAA,IACxB,gBAAgB,SAAS;AAAA,IACzB,UAAU,SAAS;AAAA,IACnB,SAAS,SAAS;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG;AAAA,IAAE,eAAeH,eAAc;AAAA,MAAK;AAAA,MAAM;AAAA;AAAA,MAE9E,SAAS;AAAA,IAAU;AAAA;AAAA,IAEnB,IAAIA,eAAc,KAAK,MAAM,gBAAgB,SAAS,UAAU;AAAA,EAAE,CAAC;AAC3E;;;ADnDA,SAAS,kBAAkB,QAAQ;AAC/B,SAAO;AAAA,IACH,QAAQ,OAAO;AAAA,IACf,UAAU,OAAO;AAAA,IACjB,uBAAuB,OAAO;AAAA,IAC9B,SAAS,OAAO;AAAA,IAChB,eAAe,OAAO;AAAA,IACtB,UAAU,OAAO;AAAA,IACjB,eAAe,OAAO;AAAA,IACtB,gBAAgB,OAAO;AAAA,IACvB,SAAS,OAAO;AAAA,IAChB,QAAQ,OAAO;AAAA,IACf,8BAA8B,OAAO;AAAA,IACrC,yBAAyB,OAAO;AAAA,EACpC;AACJ;AACA,IAAII;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUA,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACpB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,QAAQ;AAAA,QACV,OAAO,MAAM;AAAA,QACb,MAAMC,YAAW,kBAAkB,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,QAC5D,YAAY,kBAAkB,MAAM,KAAK;AAAA,MAC7C;AACA,aAAO;AAAA,IACX;AACA,IAAAD,cAAa,2BAA2B,SAAU,OAAOE,KAAI;AACzD,UAAI,aAAaA,IAAG,YAAY,QAAQA,IAAG;AAC3C,UAAI,SAAS,kBAAkB,KAAK;AACpC,UAAI,CAAC,aAAa,YAAY,MAAM,GAAG;AACnC,eAAO;AAAA,UACH,MAAMD,YAAW,QAAQ,KAAK;AAAA,UAC9B,YAAY;AAAA,QAChB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAD,cAAa,UAAU,SAAS,WAAY;AACxC,2BAAqB,KAAK,MAAM,IAAI;AACpC,aAAa,qBAAc,UAAU,EAAE,OAAO,KAAK,MAAM,KAAK,GAAG,KAAK,MAAM,QAAQ;AAAA,IACxF;AACA,IAAAA,cAAa,cAAc;AAC3B,IAAAA,cAAa,eAAeG;AAC5B,WAAOH;AAAA,EACX,EAAQ,oBAAa;AAAA;AACrB,IAAO,mBAAQA;;;AEpDf,IAAAI,SAAuB;AAGvB,IAAI,SAAS;AACb,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK,KAAK;AACpB,SAAS,WAAW,SAAS;AACzB,MAAI,WAAW,KAAK,IAAI,OAAO;AAC/B,MAAI,WAAW,QAAQ;AACnB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,MAAM;AACjB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,KAAK;AAChB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM;AAChC,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,SAAS,eAAe,OAAO,MAAM;AACjC,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,QAAQ;AAAA,IACnB;AACI,aAAO,QAAQ;AAAA,EACvB;AACJ;AACA,IAAI,sBAAsB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,MAAI,SAAS,QAAQ;AAAE,WAAO;AAAA,EAAU;AACxC,SAAO,oBAAoB,QAAQ,IAAI,IAAI;AAC/C;AACA,IAAI,8BAA8B,SAAU,OAAO;AAC/C,MAAIC,MAAK,QAAQ,GAAGC,sBAAqBD,IAAG,oBAAoB,OAAOA,IAAG;AAC1E,MAAI,WAAW,MAAM,UAAU,QAAQ,MAAM,OAAO,OAAO,MAAM,MAAM,aAAa,OAAO,OAAO,CAAC,YAAY,SAAS,MAAM,CAAC;AAC/H,MAAI,wBAAwBC,oBAAmB,SAAS,GAAG,MAAM,UAAU;AAC3E,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,qBAAqB;AAAA,EACzC;AACA,MAAI,MAAM;AACN,WAAa,qBAAc,MAAM,MAAM,qBAAqB;AAAA,EAChE;AACA,SAAa,qBAAoB,iBAAU,MAAM,qBAAqB;AAC1E;AACA,IAAI,wBAAwB,SAAUD,KAAI;AACtC,MAAI,KAAKA,IAAG,OAAO,QAAQ,OAAO,SAAS,IAAI,IAAI,KAAKA,IAAG,MAAM,OAAO,OAAO,SAAS,WAAW,IAAI,0BAA0BA,IAAG,yBAAyB,aAAa,OAAOA,KAAI,CAAC,SAAS,QAAQ,yBAAyB,CAAC;AACjO,YAAU,CAAC,2BACP,CAAC,EAAE,2BAA2B,aAAa,IAAI,IAAI,mDAAmD;AAC1G,MAAI,KAAW,gBAAS,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AAC/D,MAAI,KAAW,gBAAS,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAClE,MAAI,KAAW,gBAAS,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC;AAC1F,MAAI;AACJ,MAAI,SAAS,YAAY,UAAU,WAAW;AAC1C,iBAAa,SAAS,CAAC;AACvB,gBAAY,IAAI;AAChB,6BAAyB,aAAa,IAAI,IAAI,eAAe,OAAO,IAAI,IAAI,CAAC;AAAA,EACjF;AACA,EAAM,iBAAU,WAAY;AACxB,aAAS,mBAAmB;AACxB,mBAAa,WAAW;AAAA,IAC5B;AACA,qBAAiB;AAEjB,QAAI,CAAC,2BAA2B,CAAC,aAAa,IAAI,GAAG;AACjD,aAAO;AAAA,IACX;AAEA,QAAI,qBAAqB,wBAAwB;AACjD,QAAI,WAAW,WAAW,kBAAkB;AAE5C,QAAI,aAAa,OAAO;AACpB,aAAO;AAAA,IACX;AACA,QAAIE,gBAAe,qBAAqB,QAAQ;AAChD,QAAI,YAAY,qBAAqBA;AACrC,QAAI,gCAAgC,qBAAqB;AACzD,QAAI,gCAAgC,iCAAiC,wBAC/D,gCAAgCA,gBAChC;AACN,QAAI,iBAAiB,KAAK,IAAI,gCAAgC,qBAAqB;AACnF,QAAI,0BAA0B,+BAA+B;AACzD,oBAAc,WAAW,WAAY;AAAE,eAAO,yBAAyB,6BAA6B;AAAA,MAAG,GAAG,iBAAiB,GAAG;AAAA,IAClI;AACA,WAAO;AAAA,EACX,GAAG,CAAC,uBAAuB,yBAAyB,IAAI,CAAC;AACzD,MAAI,eAAe,SAAS;AAC5B,MAAI,cAAc;AAClB,MAAI,aAAa,IAAI,KACjB,OAAO,0BAA0B,YACjC,yBAAyB;AACzB,kBAAc,WAAW,qBAAqB;AAC9C,QAAI,eAAe,qBAAqB,WAAW;AACnD,mBAAe,KAAK,MAAM,wBAAwB,YAAY;AAAA,EAClE;AACA,SAAc,qBAAc,6BAA6B,SAAS,EAAE,OAAO,cAAc,MAAM,YAAY,GAAG,UAAU,CAAC;AAC7H;AACA,sBAAsB,cAAc;AACpC,IAAO,mBAAQ;;;ACvHf,IAAAC,SAAuB;AAEvB,IAAI,kBAAkB,SAAU,OAAO;AACnC,MAAIC,MAAK,QAAQ,GAAGC,gBAAeD,IAAG,cAAc,OAAOA,IAAG;AAC9D,MAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,OAAO,WAAW,MAAM;AAC/D,MAAI,iBAAiBC,cAAa,OAAO,KAAK;AAC9C,MAAI,kBAAkB,MAAM,cAAc,KAAK;AAC/C,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,eAAe;AAAA,EACnC;AACA,MAAI,MAAM;AACN,WAAa,qBAAc,MAAM,MAAM,eAAe;AAAA,EAC1D;AAEA,SAAO;AACX;AACA,gBAAgB,cAAc;AAC9B,IAAO,iBAAQ;;;AChBf,IAAAC,SAAuB;AAGvB,SAAS,SAAS,WAAW,WAAW;AACpC,MAAI,SAAS,UAAU,QAAQ,aAAa,OAAO,WAAW,CAAC,QAAQ,CAAC;AACxE,MAAI,aAAa,UAAU,QAAQ,iBAAiB,OAAO,WAAW,CAAC,QAAQ,CAAC;AAChF,SAAQ,aAAa,YAAY,MAAM,KACnC,aAAa,YAAY,cAAc;AAC/C;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,OAAO,QAAQ;AACnB,MAAIC,iBAAgB,KAAK,eAAeC,MAAK,KAAK,eAAe,OAAOA,QAAO,SAAe,kBAAWA;AACzG,MAAI,KAAK,MAAM,IAAI,cAAc,MAAM,aAAa,iBAAiB,MAAM,gBAAgB,SAAS,MAAM,QAAQ,WAAW,MAAM,UAAU,KAAK,MAAM,SAAS,YAAY,OAAO,SAAS,OAAO,IAAI,YAAY,MAAM;AAC1N,MAAI,aAAa,EAAE,IAAQ,aAA0B,eAA+B;AACpF,MAAI,QAAQD,eAAc,YAAY,QAAQ;AAAA,IAC1C;AAAA,EACJ,CAAC;AACD,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;AAAA,EAC1D;AACA,MAAI,WAAW;AACX,WAAa,qBAAc,WAAW,MAAY,gBAAS,QAAQ,KAAK,CAAC;AAAA,EAC7E;AACA,SAAa,qBAAoB,iBAAU,MAAM,KAAK;AAC1D;AACA,iBAAiB,cAAc;AAC/B,IAAI,2BAAiC,YAAK,kBAAkB,QAAQ;AACpE,yBAAyB,cAAc;AACvC,IAAO,kBAAQ;;;ACjCf,IAAAE,UAAuB;AAEvB,IAAI,yBAAyB,SAAU,OAAO;AAC1C,MAAI,OAAO,QAAQ;AACnB,MAAI,OAAO,MAAM,MAAM,KAAK,MAAM,IAAI,WAAW,MAAM,UAAU,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,UAAU,CAAC;AACvH,MAAI,iBAAiB,KAAK,oBAAoB,MAAM,IAAI,WAAW;AACnE,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,cAAc;AAAA,EAClC;AACA,MAAI,OAAO,KAAK,iBAAuB;AACvC,SAAa,sBAAc,MAAM,MAAM,cAAc;AACzD;AACA,uBAAuB,cAAc;AACrC,IAAO,wBAAQ;;;ACHR,SAAS,eAAe,MAAM;AACjC,SAAO;AACX;AACO,SAAS,cAAc,KAAK;AAC/B,SAAO;AACX;AAEO,IAAI,gBAAgB,yBAAyB,YAAY;AACzD,IAAI,gBAAgB,yBAAyB,YAAY;AACzD,IAAI,kBAAkB,yBAAyB,cAAc;AAC7D,IAAI,gBAAgB,yBAAyB,YAAY;AACzD,IAAI,uBAAuB,yBAAyB,mBAAmB;AACvE,IAAI,qBAAqB,sCAAsC,YAAY;AAC3E,IAAI,qBAAqB,sCAAsC,YAAY;", "names": ["defineProperty", "hoistNonReactStatics", "d", "b", "__assign", "React", "React", "_a", "MissingLocaleDataError", "RangePatternType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TYPE", "SKELETON_TYPE", "_a", "opt", "startsWith", "fromCodePoint", "fromEntries", "_a", "codePointAt", "trimStart", "trimEnd", "matchIdentifierAtIndex", "<PERSON><PERSON><PERSON>", "ErrorCode", "FormatError", "InvalidValueError", "InvalidValueTypeError", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PART_TYPE", "formatToParts", "_a", "IntlMessageFormat", "formatToParts", "IntlErrorCode", "IntlError", "UnsupportedFormatterError", "InvalidConfigError", "MissingDataError", "IntlFormatError", "MessageFormatError", "MissingTranslationError", "_a", "createFastMemoizeCache", "_a", "_a", "_a", "_a", "_a", "_a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_a", "DEFAULT_INTL_CONFIG", "React", "_a", "forwardRef", "hoistNonReactStatics", "DisplayName", "DisplayNameParts", "React", "React", "formatMessage", "createIntl", "_a", "DEFAULT_INTL_CONFIG", "IntlProvider", "createIntl", "_a", "DEFAULT_INTL_CONFIG", "React", "_a", "formatRelativeTime", "unitDuration", "React", "_a", "formatPlural", "React", "formatMessage", "_a", "React"]}