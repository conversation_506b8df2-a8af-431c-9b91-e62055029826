{"version": 3, "sources": ["../../antd/es/layout/layout.js", "../../antd/es/config-provider/index.js", "../../antd/es/_util/warning.js", "../../antd/es/form/validateMessagesContext.js", "../../antd/es/locale/index.js", "../../rc-pagination/es/locale/en_US.js", "../../rc-picker/es/locale/en_US.js", "../../rc-picker/es/locale/common.js", "../../antd/es/time-picker/locale/en_US.js", "../../antd/es/date-picker/locale/en_US.js", "../../antd/es/calendar/locale/en_US.js", "../../antd/es/locale/en_US.js", "../../antd/es/modal/locale.js", "../../antd/es/locale/context.js", "../../antd/es/locale/useLocale.js", "../../antd/es/theme/context.js", "../../antd/es/theme/themes/seed.js", "../../antd/es/theme/themes/default/theme.js", "../../antd/es/theme/themes/default/index.js", "../../antd/es/theme/themes/shared/genColorMapToken.js", "../../antd/es/theme/themes/shared/genRadius.js", "../../antd/es/theme/themes/shared/genCommonMapToken.js", "../../antd/es/theme/themes/shared/genControlHeight.js", "../../antd/es/theme/themes/shared/genFontSizes.js", "../../antd/es/theme/themes/shared/genFontMapToken.js", "../../antd/es/theme/themes/shared/genSizeMapToken.js", "../../antd/es/theme/themes/default/colors.js", "../../antd/es/theme/themes/default/colorAlgorithm.js", "../../antd/es/config-provider/context.js", "../../antd/es/config-provider/cssVariables.js", "../../antd/es/config-provider/DisabledContext.js", "../../antd/es/config-provider/hooks/useConfig.js", "../../antd/es/config-provider/SizeContext.js", "../../antd/es/config-provider/hooks/useTheme.js", "../../antd/es/theme/internal.js", "../../antd/es/theme/interface/presetColors.js", "../../antd/es/theme/useToken.js", "../../antd/es/version/version.js", "../../antd/es/version/index.js", "../../antd/es/theme/util/alias.js", "../../antd/es/theme/util/getAlphaColor.js", "../../antd/es/theme/util/genStyleUtils.js", "../../antd/es/style/index.js", "../../antd/es/theme/util/genPresetColor.js", "../../antd/es/theme/util/useResetIconStyle.js", "../../antd/es/config-provider/hooks/useThemeKey.js", "../../antd/es/config-provider/MotionWrapper.js", "../../antd/es/config-provider/PropWarning.js", "../../antd/es/layout/context.js", "../../antd/es/layout/hooks/useHasSider.js", "../../antd/es/layout/Sider.js", "../../antd/es/_util/mediaQueryUtil.js", "../../antd/es/layout/style/sider.js", "../../antd/es/layout/style/index.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport { useComponentConfig } from '../config-provider/context';\nimport { LayoutContext } from './context';\nimport useHasSider from './hooks/useHasSider';\nimport useStyle from './style';\nfunction generator({\n  suffixCls,\n  tagName,\n  displayName\n}) {\n  return BasicComponent => {\n    const Adapter = /*#__PURE__*/React.forwardRef((props, ref) => (/*#__PURE__*/React.createElement(BasicComponent, Object.assign({\n      ref: ref,\n      suffixCls: suffixCls,\n      tagName: tagName\n    }, props))));\n    if (process.env.NODE_ENV !== 'production') {\n      Adapter.displayName = displayName;\n    }\n    return Adapter;\n  };\n}\nconst Basic = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      suffixCls,\n      className,\n      tagName: TagName\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"suffixCls\", \"className\", \"tagName\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('layout', customizePrefixCls);\n  const [wrapSSR, hashId, cssVarCls] = useStyle(prefixCls);\n  const prefixWithSuffixCls = suffixCls ? `${prefixCls}-${suffixCls}` : prefixCls;\n  return wrapSSR(/*#__PURE__*/React.createElement(TagName, Object.assign({\n    className: classNames(customizePrefixCls || prefixWithSuffixCls, className, hashId, cssVarCls),\n    ref: ref\n  }, others)));\n});\nconst BasicLayout = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    direction\n  } = React.useContext(ConfigContext);\n  const [siders, setSiders] = React.useState([]);\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      children,\n      hasSider,\n      tagName: Tag,\n      style\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"className\", \"rootClassName\", \"children\", \"hasSider\", \"tagName\", \"style\"]);\n  const passedProps = omit(others, ['suffixCls']);\n  const {\n    getPrefixCls,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('layout');\n  const prefixCls = getPrefixCls('layout', customizePrefixCls);\n  const mergedHasSider = useHasSider(siders, children, hasSider);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const classString = classNames(prefixCls, {\n    [`${prefixCls}-has-sider`]: mergedHasSider,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, hashId, cssVarCls);\n  const contextValue = React.useMemo(() => ({\n    siderHook: {\n      addSider: id => {\n        setSiders(prev => [].concat(_toConsumableArray(prev), [id]));\n      },\n      removeSider: id => {\n        setSiders(prev => prev.filter(currentId => currentId !== id));\n      }\n    }\n  }), []);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(LayoutContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(Tag, Object.assign({\n    ref: ref,\n    className: classString,\n    style: Object.assign(Object.assign({}, contextStyle), style)\n  }, passedProps), children)));\n});\nconst Layout = generator({\n  tagName: 'div',\n  displayName: 'Layout'\n})(BasicLayout);\nconst Header = generator({\n  suffixCls: 'header',\n  tagName: 'header',\n  displayName: 'Header'\n})(Basic);\nconst Footer = generator({\n  suffixCls: 'footer',\n  tagName: 'footer',\n  displayName: 'Footer'\n})(Basic);\nconst Content = generator({\n  suffixCls: 'content',\n  tagName: 'main',\n  displayName: 'Content'\n})(Basic);\nexport { Content, Footer, Header };\nexport default Layout;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { createTheme, StyleContext as CssInJsStyleContext } from '@ant-design/cssinjs';\nimport IconContext from \"@ant-design/icons/es/components/Context\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning, { devUseWarning, WarningContext } from '../_util/warning';\nimport ValidateMessagesContext from '../form/validateMessagesContext';\nimport LocaleProvider, { ANT_MARK } from '../locale';\nimport LocaleContext from '../locale/context';\nimport defaultLocale from '../locale/en_US';\nimport { defaultTheme, DesignTokenContext } from '../theme/context';\nimport defaultSeedToken from '../theme/themes/seed';\nimport { ConfigConsumer, ConfigContext, defaultIconPrefixCls, defaultPrefixCls, Variants } from './context';\nimport { registerTheme } from './cssVariables';\nimport { DisabledContextProvider } from './DisabledContext';\nimport useConfig from './hooks/useConfig';\nimport useTheme from './hooks/useTheme';\nimport MotionWrapper from './MotionWrapper';\nimport PropWarning from './PropWarning';\nimport SizeContext, { SizeContextProvider } from './SizeContext';\nimport useStyle from './style';\nexport { Variants };\n/**\n * Since too many feedback using static method like `Modal.confirm` not getting theme, we record the\n * theme register info here to help developer get warning info.\n */\nlet existThemeConfig = false;\nexport const warnContext = process.env.NODE_ENV !== 'production' ? componentName => {\n  process.env.NODE_ENV !== \"production\" ? warning(!existThemeConfig, componentName, `Static function can not consume context like dynamic theme. Please use 'App' component instead.`) : void 0;\n} : /* istanbul ignore next */\nnull;\nexport { ConfigConsumer, ConfigContext, defaultPrefixCls, defaultIconPrefixCls };\nexport const configConsumerProps = ['getTargetContainer', 'getPopupContainer', 'rootPrefixCls', 'getPrefixCls', 'renderEmpty', 'csp', 'autoInsertSpaceInButton', 'locale'];\n// These props is used by `useContext` directly in sub component\nconst PASSED_PROPS = ['getTargetContainer', 'getPopupContainer', 'renderEmpty', 'input', 'pagination', 'form', 'select', 'button'];\nlet globalPrefixCls;\nlet globalIconPrefixCls;\nlet globalTheme;\nlet globalHolderRender;\nfunction getGlobalPrefixCls() {\n  return globalPrefixCls || defaultPrefixCls;\n}\nfunction getGlobalIconPrefixCls() {\n  return globalIconPrefixCls || defaultIconPrefixCls;\n}\nfunction isLegacyTheme(theme) {\n  return Object.keys(theme).some(key => key.endsWith('Color'));\n}\nconst setGlobalConfig = props => {\n  const {\n    prefixCls,\n    iconPrefixCls,\n    theme,\n    holderRender\n  } = props;\n  if (prefixCls !== undefined) {\n    globalPrefixCls = prefixCls;\n  }\n  if (iconPrefixCls !== undefined) {\n    globalIconPrefixCls = iconPrefixCls;\n  }\n  if ('holderRender' in props) {\n    globalHolderRender = holderRender;\n  }\n  if (theme) {\n    if (isLegacyTheme(theme)) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', '`config` of css variable theme is not work in v5. Please use new `theme` config instead.') : void 0;\n      registerTheme(getGlobalPrefixCls(), theme);\n    } else {\n      globalTheme = theme;\n    }\n  }\n};\nexport const globalConfig = () => ({\n  getPrefixCls: (suffixCls, customizePrefixCls) => {\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    return suffixCls ? `${getGlobalPrefixCls()}-${suffixCls}` : getGlobalPrefixCls();\n  },\n  getIconPrefixCls: getGlobalIconPrefixCls,\n  getRootPrefixCls: () => {\n    // If Global prefixCls provided, use this\n    if (globalPrefixCls) {\n      return globalPrefixCls;\n    }\n    // Fallback to default prefixCls\n    return getGlobalPrefixCls();\n  },\n  getTheme: () => globalTheme,\n  holderRender: globalHolderRender\n});\nconst ProviderChildren = props => {\n  const {\n    children,\n    csp: customCsp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    form,\n    locale,\n    componentSize,\n    direction,\n    space,\n    splitter,\n    virtual,\n    dropdownMatchSelectWidth,\n    popupMatchSelectWidth,\n    popupOverflow,\n    legacyLocale,\n    parentContext,\n    iconPrefixCls: customIconPrefixCls,\n    theme,\n    componentDisabled,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    input,\n    textArea,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  } = props;\n  // =================================== Context ===================================\n  const getPrefixCls = React.useCallback((suffixCls, customizePrefixCls) => {\n    const {\n      prefixCls\n    } = props;\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    const mergedPrefixCls = prefixCls || parentContext.getPrefixCls('');\n    return suffixCls ? `${mergedPrefixCls}-${suffixCls}` : mergedPrefixCls;\n  }, [parentContext.getPrefixCls, props.prefixCls]);\n  const iconPrefixCls = customIconPrefixCls || parentContext.iconPrefixCls || defaultIconPrefixCls;\n  const csp = customCsp || parentContext.csp;\n  useStyle(iconPrefixCls, csp);\n  const mergedTheme = useTheme(theme, parentContext.theme, {\n    prefixCls: getPrefixCls('')\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    existThemeConfig = existThemeConfig || !!mergedTheme;\n  }\n  const baseConfig = {\n    csp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    locale: locale || legacyLocale,\n    direction,\n    space,\n    splitter,\n    virtual,\n    popupMatchSelectWidth: popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth,\n    popupOverflow,\n    getPrefixCls,\n    iconPrefixCls,\n    theme: mergedTheme,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    input,\n    textArea,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    const warningFn = devUseWarning('ConfigProvider');\n    warningFn(!('autoInsertSpaceInButton' in props), 'deprecated', '`autoInsertSpaceInButton` is deprecated. Please use `{ button: { autoInsertSpace: boolean }}` instead.');\n  }\n  const config = Object.assign({}, parentContext);\n  Object.keys(baseConfig).forEach(key => {\n    if (baseConfig[key] !== undefined) {\n      config[key] = baseConfig[key];\n    }\n  });\n  // Pass the props used by `useContext` directly with child component.\n  // These props should merged into `config`.\n  PASSED_PROPS.forEach(propName => {\n    const propValue = props[propName];\n    if (propValue) {\n      config[propName] = propValue;\n    }\n  });\n  if (typeof autoInsertSpaceInButton !== 'undefined') {\n    // merge deprecated api\n    config.button = Object.assign({\n      autoInsertSpace: autoInsertSpaceInButton\n    }, config.button);\n  }\n  // https://github.com/ant-design/ant-design/issues/27617\n  const memoedConfig = useMemo(() => config, config, (prevConfig, currentConfig) => {\n    const prevKeys = Object.keys(prevConfig);\n    const currentKeys = Object.keys(currentConfig);\n    return prevKeys.length !== currentKeys.length || prevKeys.some(key => prevConfig[key] !== currentConfig[key]);\n  });\n  const {\n    layer\n  } = React.useContext(CssInJsStyleContext);\n  const memoIconContextValue = React.useMemo(() => ({\n    prefixCls: iconPrefixCls,\n    csp,\n    layer: layer ? 'antd' : undefined\n  }), [iconPrefixCls, csp, layer]);\n  let childNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PropWarning, {\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }), children);\n  const validateMessages = React.useMemo(() => {\n    var _a, _b, _c, _d;\n    return merge(((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.defaultValidateMessages) || {}, ((_c = (_b = memoedConfig.locale) === null || _b === void 0 ? void 0 : _b.Form) === null || _c === void 0 ? void 0 : _c.defaultValidateMessages) || {}, ((_d = memoedConfig.form) === null || _d === void 0 ? void 0 : _d.validateMessages) || {}, (form === null || form === void 0 ? void 0 : form.validateMessages) || {});\n  }, [memoedConfig, form === null || form === void 0 ? void 0 : form.validateMessages]);\n  if (Object.keys(validateMessages).length > 0) {\n    childNode = /*#__PURE__*/React.createElement(ValidateMessagesContext.Provider, {\n      value: validateMessages\n    }, childNode);\n  }\n  if (locale) {\n    childNode = /*#__PURE__*/React.createElement(LocaleProvider, {\n      locale: locale,\n      _ANT_MARK__: ANT_MARK\n    }, childNode);\n  }\n  if (iconPrefixCls || csp) {\n    childNode = /*#__PURE__*/React.createElement(IconContext.Provider, {\n      value: memoIconContextValue\n    }, childNode);\n  }\n  if (componentSize) {\n    childNode = /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: componentSize\n    }, childNode);\n  }\n  // =================================== Motion ===================================\n  childNode = /*#__PURE__*/React.createElement(MotionWrapper, null, childNode);\n  // ================================ Dynamic theme ================================\n  const memoTheme = React.useMemo(() => {\n    const _a = mergedTheme || {},\n      {\n        algorithm,\n        token,\n        components,\n        cssVar\n      } = _a,\n      rest = __rest(_a, [\"algorithm\", \"token\", \"components\", \"cssVar\"]);\n    const themeObj = algorithm && (!Array.isArray(algorithm) || algorithm.length > 0) ? createTheme(algorithm) : defaultTheme;\n    const parsedComponents = {};\n    Object.entries(components || {}).forEach(([componentName, componentToken]) => {\n      const parsedToken = Object.assign({}, componentToken);\n      if ('algorithm' in parsedToken) {\n        if (parsedToken.algorithm === true) {\n          parsedToken.theme = themeObj;\n        } else if (Array.isArray(parsedToken.algorithm) || typeof parsedToken.algorithm === 'function') {\n          parsedToken.theme = createTheme(parsedToken.algorithm);\n        }\n        delete parsedToken.algorithm;\n      }\n      parsedComponents[componentName] = parsedToken;\n    });\n    const mergedToken = Object.assign(Object.assign({}, defaultSeedToken), token);\n    return Object.assign(Object.assign({}, rest), {\n      theme: themeObj,\n      token: mergedToken,\n      components: parsedComponents,\n      override: Object.assign({\n        override: mergedToken\n      }, parsedComponents),\n      cssVar: cssVar\n    });\n  }, [mergedTheme]);\n  if (theme) {\n    childNode = /*#__PURE__*/React.createElement(DesignTokenContext.Provider, {\n      value: memoTheme\n    }, childNode);\n  }\n  // ================================== Warning ===================================\n  if (memoedConfig.warning) {\n    childNode = /*#__PURE__*/React.createElement(WarningContext.Provider, {\n      value: memoedConfig.warning\n    }, childNode);\n  }\n  // =================================== Render ===================================\n  if (componentDisabled !== undefined) {\n    childNode = /*#__PURE__*/React.createElement(DisabledContextProvider, {\n      disabled: componentDisabled\n    }, childNode);\n  }\n  return /*#__PURE__*/React.createElement(ConfigContext.Provider, {\n    value: memoedConfig\n  }, childNode);\n};\nconst ConfigProvider = props => {\n  const context = React.useContext(ConfigContext);\n  const antLocale = React.useContext(LocaleContext);\n  return /*#__PURE__*/React.createElement(ProviderChildren, Object.assign({\n    parentContext: context,\n    legacyLocale: antLocale\n  }, props));\n};\nConfigProvider.ConfigContext = ConfigContext;\nConfigProvider.SizeContext = SizeContext;\nConfigProvider.config = setGlobalConfig;\nConfigProvider.useConfig = useConfig;\nObject.defineProperty(ConfigProvider, 'SizeContext', {\n  get: () => {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'ConfigProvider.SizeContext is deprecated. Please use `ConfigProvider.useConfig().componentSize` instead.') : void 0;\n    return SizeContext;\n  }\n});\nif (process.env.NODE_ENV !== 'production') {\n  ConfigProvider.displayName = 'ConfigProvider';\n}\nexport default ConfigProvider;", "import * as React from 'react';\nimport rcWarning, { resetWarned as rcResetWarned } from \"rc-util/es/warning\";\nexport function noop() {}\nlet deprecatedWarnList = null;\nexport function resetWarned() {\n  deprecatedWarnList = null;\n  rcResetWarned();\n}\nlet _warning = noop;\nif (process.env.NODE_ENV !== 'production') {\n  _warning = (valid, component, message) => {\n    rcWarning(valid, `[antd: ${component}] ${message}`);\n    // StrictMode will inject console which will not throw warning in React 17.\n    if (process.env.NODE_ENV === 'test') {\n      resetWarned();\n    }\n  };\n}\nconst warning = _warning;\nexport const WarningContext = /*#__PURE__*/React.createContext({});\n/**\n * This is a hook but we not named as `useWarning`\n * since this is only used in development.\n * We should always wrap this in `if (process.env.NODE_ENV !== 'production')` condition\n */\nexport const devUseWarning = process.env.NODE_ENV !== 'production' ? component => {\n  const {\n    strict\n  } = React.useContext(WarningContext);\n  const typeWarning = (valid, type, message) => {\n    if (!valid) {\n      if (strict === false && type === 'deprecated') {\n        const existWarning = deprecatedWarnList;\n        if (!deprecatedWarnList) {\n          deprecatedWarnList = {};\n        }\n        deprecatedWarnList[component] = deprecatedWarnList[component] || [];\n        if (!deprecatedWarnList[component].includes(message || '')) {\n          deprecatedWarnList[component].push(message || '');\n        }\n        // Warning for the first time\n        if (!existWarning) {\n          console.warn('[antd] There exists deprecated usage in your code:', deprecatedWarnList);\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(valid, component, message) : void 0;\n      }\n    }\n  };\n  typeWarning.deprecated = (valid, oldProp, newProp, message) => {\n    typeWarning(valid, 'deprecated', `\\`${oldProp}\\` is deprecated. Please use \\`${newProp}\\` instead.${message ? ` ${message}` : ''}`);\n  };\n  return typeWarning;\n} : () => {\n  const noopWarning = () => {};\n  noopWarning.deprecated = noop;\n  return noopWarning;\n};\nexport default warning;", "\"use client\";\n\nimport { createContext } from 'react';\n// ZombieJ: We export single file here since\n// ConfigProvider use this which will make loop deps\n// to import whole `rc-field-form`\nexport default /*#__PURE__*/createContext(undefined);", "\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport { changeConfirmLocale } from '../modal/locale';\nimport LocaleContext from './context';\nexport { default as useLocale } from './useLocale';\nexport const ANT_MARK = 'internalMark';\nconst LocaleProvider = props => {\n  const {\n    locale = {},\n    children,\n    _ANT_MARK__\n  } = props;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('LocaleProvider');\n    process.env.NODE_ENV !== \"production\" ? warning(_ANT_MARK__ === ANT_MARK, 'deprecated', '`LocaleProvider` is deprecated. Please use `locale` with `ConfigProvider` instead: http://u.ant.design/locale') : void 0;\n  }\n  React.useEffect(() => {\n    const clearLocale = changeConfirmLocale(locale === null || locale === void 0 ? void 0 : locale.Modal);\n    return clearLocale;\n  }, [locale]);\n  const getMemoizedContextValue = React.useMemo(() => Object.assign(Object.assign({}, locale), {\n    exist: true\n  }), [locale]);\n  return /*#__PURE__*/React.createElement(LocaleContext.Provider, {\n    value: getMemoizedContextValue\n  }, children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  LocaleProvider.displayName = 'LocaleProvider';\n}\nexport default LocaleProvider;", "var locale = {\n  // Options\n  items_per_page: '/ page',\n  jump_to: 'Go to',\n  jump_to_confirm: 'confirm',\n  page: 'Page',\n  // Pagination\n  prev_page: 'Previous Page',\n  next_page: 'Next Page',\n  prev_5: 'Previous 5 Pages',\n  next_5: 'Next 5 Pages',\n  prev_3: 'Previous 3 Pages',\n  next_3: 'Next 3 Pages',\n  page_size: 'Page Size'\n};\nexport default locale;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { commonLocale } from \"./common\";\nvar locale = _objectSpread(_objectSpread({}, commonLocale), {}, {\n  locale: 'en_US',\n  today: 'Today',\n  now: 'Now',\n  backToToday: 'Back to today',\n  ok: 'OK',\n  clear: 'Clear',\n  week: 'Week',\n  month: 'Month',\n  year: 'Year',\n  timeSelect: 'select time',\n  dateSelect: 'select date',\n  weekSelect: 'Choose a week',\n  monthSelect: 'Choose a month',\n  yearSelect: 'Choose a year',\n  decadeSelect: 'Choose a decade',\n  dateFormat: 'M/D/YYYY',\n  dateTimeFormat: 'M/D/YYYY HH:mm:ss',\n  previousMonth: 'Previous month (PageUp)',\n  nextMonth: 'Next month (PageDown)',\n  previousYear: 'Last year (Control + left)',\n  nextYear: 'Next year (Control + right)',\n  previousDecade: 'Last decade',\n  nextDecade: 'Next decade',\n  previousCentury: 'Last century',\n  nextCentury: 'Next century'\n});\nexport default locale;", "export var commonLocale = {\n  yearFormat: 'YYYY',\n  dayFormat: 'D',\n  cellMeridiemFormat: 'A',\n  monthBeforeYear: true\n};", "const locale = {\n  placeholder: 'Select time',\n  rangePlaceholder: ['Start time', 'End time']\n};\nexport default locale;", "import CalendarLocale from \"rc-picker/es/locale/en_US\";\nimport TimePickerLocale from '../../time-picker/locale/en_US';\n// Merge into a locale object\nconst locale = {\n  lang: Object.assign({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, CalendarLocale),\n  timePickerLocale: Object.assign({}, TimePickerLocale)\n};\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nexport default locale;", "import enUS from '../../date-picker/locale/en_US';\nexport default enUS;", "import Pagination from \"rc-pagination/es/locale/en_US\";\nimport Calendar from '../calendar/locale/en_US';\nimport DatePicker from '../date-picker/locale/en_US';\nimport TimePicker from '../time-picker/locale/en_US';\nconst typeTemplate = '${label} is not a valid ${type}';\nconst localeValues = {\n  locale: 'en',\n  Pagination,\n  DatePicker,\n  TimePicker,\n  Calendar,\n  global: {\n    placeholder: 'Please select',\n    close: 'Close'\n  },\n  Table: {\n    filterTitle: 'Filter menu',\n    filterConfirm: 'OK',\n    filterReset: 'Reset',\n    filterEmptyText: 'No filters',\n    filterCheckAll: 'Select all items',\n    filterSearchPlaceholder: 'Search in filters',\n    emptyText: 'No data',\n    selectAll: 'Select current page',\n    selectInvert: 'Invert current page',\n    selectNone: 'Clear all data',\n    selectionAll: 'Select all data',\n    sortTitle: 'Sort',\n    expand: 'Expand row',\n    collapse: 'Collapse row',\n    triggerDesc: 'Click to sort descending',\n    triggerAsc: 'Click to sort ascending',\n    cancelSort: 'Click to cancel sorting'\n  },\n  Tour: {\n    Next: 'Next',\n    Previous: 'Previous',\n    Finish: 'Finish'\n  },\n  Modal: {\n    okText: 'OK',\n    cancelText: 'Cancel',\n    justOkText: 'OK'\n  },\n  Popconfirm: {\n    okText: 'OK',\n    cancelText: 'Cancel'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: 'Search here',\n    itemUnit: 'item',\n    itemsUnit: 'items',\n    remove: 'Remove',\n    selectCurrent: 'Select current page',\n    removeCurrent: 'Remove current page',\n    selectAll: 'Select all data',\n    deselectAll: 'Deselect all data',\n    removeAll: 'Remove all data',\n    selectInvert: 'Invert current page'\n  },\n  Upload: {\n    uploading: 'Uploading...',\n    removeFile: 'Remove file',\n    uploadError: 'Upload error',\n    previewFile: 'Preview file',\n    downloadFile: 'Download file'\n  },\n  Empty: {\n    description: 'No data'\n  },\n  Icon: {\n    icon: 'icon'\n  },\n  Text: {\n    edit: 'Edit',\n    copy: 'Copy',\n    copied: 'Copied',\n    expand: 'Expand',\n    collapse: 'Collapse'\n  },\n  Form: {\n    optional: '(optional)',\n    defaultValidateMessages: {\n      default: 'Field validation error for ${label}',\n      required: 'Please enter ${label}',\n      enum: '${label} must be one of [${enum}]',\n      whitespace: '${label} cannot be a blank character',\n      date: {\n        format: '${label} date format is invalid',\n        parse: '${label} cannot be converted to a date',\n        invalid: '${label} is an invalid date'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label} must be ${len} characters',\n        min: '${label} must be at least ${min} characters',\n        max: '${label} must be up to ${max} characters',\n        range: '${label} must be between ${min}-${max} characters'\n      },\n      number: {\n        len: '${label} must be equal to ${len}',\n        min: '${label} must be minimum ${min}',\n        max: '${label} must be maximum ${max}',\n        range: '${label} must be between ${min}-${max}'\n      },\n      array: {\n        len: 'Must be ${len} ${label}',\n        min: 'At least ${min} ${label}',\n        max: 'At most ${max} ${label}',\n        range: 'The amount of ${label} must be between ${min}-${max}'\n      },\n      pattern: {\n        mismatch: '${label} does not match the pattern ${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: 'Preview'\n  },\n  QRCode: {\n    expired: 'QR code expired',\n    refresh: 'Refresh',\n    scanned: 'Scanned'\n  },\n  ColorPicker: {\n    presetEmpty: 'Empty',\n    transparent: 'Transparent',\n    singleColor: 'Single',\n    gradientColor: 'Gradient'\n  }\n};\nexport default localeValues;", "import defaultLocale from '../locale/en_US';\nlet runtimeLocale = Object.assign({}, defaultLocale.Modal);\nlet localeList = [];\nconst generateLocale = () => localeList.reduce((merged, locale) => Object.assign(Object.assign({}, merged), locale), defaultLocale.Modal);\nexport function changeConfirmLocale(newLocale) {\n  if (newLocale) {\n    const cloneLocale = Object.assign({}, newLocale);\n    localeList.push(cloneLocale);\n    runtimeLocale = generateLocale();\n    return () => {\n      localeList = localeList.filter(locale => locale !== cloneLocale);\n      runtimeLocale = generateLocale();\n    };\n  }\n  runtimeLocale = Object.assign({}, defaultLocale.Modal);\n}\nexport function getConfirmLocale() {\n  return runtimeLocale;\n}", "import { createContext } from 'react';\nconst LocaleContext = /*#__PURE__*/createContext(undefined);\nexport default LocaleContext;", "import * as React from 'react';\nimport LocaleContext from './context';\nimport defaultLocaleData from './en_US';\nconst useLocale = (componentName, defaultLocale) => {\n  const fullLocale = React.useContext(LocaleContext);\n  const getLocale = React.useMemo(() => {\n    var _a;\n    const locale = defaultLocale || defaultLocaleData[componentName];\n    const localeFromContext = (_a = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return Object.assign(Object.assign({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, fullLocale]);\n  const getLocaleCode = React.useMemo(() => {\n    const localeCode = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.locale;\n    // Had use LocaleProvide but didn't set locale\n    if ((fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.exist) && !localeCode) {\n      return defaultLocaleData.locale;\n    }\n    return localeCode;\n  }, [fullLocale]);\n  return [getLocale, getLocaleCode];\n};\nexport default useLocale;", "import React from 'react';\nimport defaultSeedToken from './themes/seed';\nexport { default as defaultTheme } from './themes/default/theme';\n// ================================ Context =================================\n// To ensure snapshot stable. We disable hashed in test env.\nexport const defaultConfig = {\n  token: defaultSeedToken,\n  override: {\n    override: defaultSeedToken\n  },\n  hashed: true\n};\nexport const DesignTokenContext = /*#__PURE__*/React.createContext(defaultConfig);", "export const defaultPresetColors = {\n  blue: '#1677FF',\n  purple: '#722ED1',\n  cyan: '#13C2C2',\n  green: '#52C41A',\n  magenta: '#EB2F96',\n  /**\n   * @deprecated Use magenta instead\n   */\n  pink: '#EB2F96',\n  red: '#F5222D',\n  orange: '#FA8C16',\n  yellow: '#FADB14',\n  volcano: '#FA541C',\n  geekblue: '#2F54EB',\n  gold: '#FAAD14',\n  lime: '#A0D911'\n};\nconst seedToken = Object.assign(Object.assign({}, defaultPresetColors), {\n  // Color\n  colorPrimary: '#1677ff',\n  colorSuccess: '#52c41a',\n  colorWarning: '#faad14',\n  colorError: '#ff4d4f',\n  colorInfo: '#1677ff',\n  colorLink: '',\n  colorTextBase: '',\n  colorBgBase: '',\n  // Font\n  fontFamily: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n'Noto Color Emoji'`,\n  fontFamilyCode: `'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace`,\n  fontSize: 14,\n  // Line\n  lineWidth: 1,\n  lineType: 'solid',\n  // Motion\n  motionUnit: 0.1,\n  motionBase: 0,\n  motionEaseOutCirc: 'cubic-bezier(0.08, 0.82, 0.17, 1)',\n  motionEaseInOutCirc: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',\n  motionEaseOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',\n  motionEaseInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',\n  motionEaseOutBack: 'cubic-bezier(0.12, 0.4, 0.29, 1.46)',\n  motionEaseInBack: 'cubic-bezier(0.71, -0.46, 0.88, 0.6)',\n  motionEaseInQuint: 'cubic-bezier(0.755, 0.05, 0.855, 0.06)',\n  motionEaseOutQuint: 'cubic-bezier(0.23, 1, 0.32, 1)',\n  // Radius\n  borderRadius: 6,\n  // Size\n  sizeUnit: 4,\n  sizeStep: 4,\n  sizePopupArrow: 16,\n  // Control Base\n  controlHeight: 32,\n  // zIndex\n  zIndexBase: 0,\n  zIndexPopupBase: 1000,\n  // Image\n  opacityImage: 1,\n  // Wireframe\n  wireframe: false,\n  // Motion\n  motion: true\n});\nexport default seedToken;", "import { createTheme } from '@ant-design/cssinjs';\nimport defaultDerivative from './index';\nconst defaultTheme = createTheme(defaultDerivative);\nexport default defaultTheme;", "import { generate, presetPalettes, presetPrimaryColors } from '@ant-design/colors';\nimport { defaultPresetColors } from '../seed';\nimport genColorMapToken from '../shared/genColorMapToken';\nimport genCommonMapToken from '../shared/genCommonMapToken';\nimport genControlHeight from '../shared/genControlHeight';\nimport genFontMapToken from '../shared/genFontMapToken';\nimport genSizeMapToken from '../shared/genSizeMapToken';\nimport { generateColorPalettes, generateNeutralColorPalettes } from './colors';\nexport default function derivative(token) {\n  // pink is deprecated name of magenta, keep this for backwards compatibility\n  presetPrimaryColors.pink = presetPrimaryColors.magenta;\n  presetPalettes.pink = presetPalettes.magenta;\n  const colorPalettes = Object.keys(defaultPresetColors).map(colorKey => {\n    const colors = token[colorKey] === presetPrimaryColors[colorKey] ? presetPalettes[colorKey] : generate(token[colorKey]);\n    return Array.from({\n      length: 10\n    }, () => 1).reduce((prev, _, i) => {\n      prev[`${colorKey}-${i + 1}`] = colors[i];\n      prev[`${colorKey}${i + 1}`] = colors[i];\n      return prev;\n    }, {});\n  }).reduce((prev, cur) => {\n    prev = Object.assign(Object.assign({}, prev), cur);\n    return prev;\n  }, {});\n  return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, token), colorPalettes), genColorMapToken(token, {\n    generateColorPalettes,\n    generateNeutralColorPalettes\n  })), genFontMapToken(token.fontSize)), genSizeMapToken(token)), genControlHeight(token)), genCommonMapToken(token));\n}", "import { FastColor } from '@ant-design/fast-color';\nexport default function genColorMapToken(seed, {\n  generateColorPalettes,\n  generateNeutralColorPalettes\n}) {\n  const {\n    colorSuccess: colorSuccessBase,\n    colorWarning: colorWarningBase,\n    colorError: colorErrorBase,\n    colorInfo: colorInfoBase,\n    colorPrimary: colorPrimaryBase,\n    colorBgBase,\n    colorTextBase\n  } = seed;\n  const primaryColors = generateColorPalettes(colorPrimaryBase);\n  const successColors = generateColorPalettes(colorSuccessBase);\n  const warningColors = generateColorPalettes(colorWarningBase);\n  const errorColors = generateColorPalettes(colorErrorBase);\n  const infoColors = generateColorPalettes(colorInfoBase);\n  const neutralColors = generateNeutralColorPalettes(colorBgBase, colorTextBase);\n  // Color Link\n  const colorLink = seed.colorLink || seed.colorInfo;\n  const linkColors = generateColorPalettes(colorLink);\n  const colorErrorBgFilledHover = new FastColor(errorColors[1]).mix(new FastColor(errorColors[3]), 50).toHexString();\n  return Object.assign(Object.assign({}, neutralColors), {\n    colorPrimaryBg: primaryColors[1],\n    colorPrimaryBgHover: primaryColors[2],\n    colorPrimaryBorder: primaryColors[3],\n    colorPrimaryBorderHover: primaryColors[4],\n    colorPrimaryHover: primaryColors[5],\n    colorPrimary: primaryColors[6],\n    colorPrimaryActive: primaryColors[7],\n    colorPrimaryTextHover: primaryColors[8],\n    colorPrimaryText: primaryColors[9],\n    colorPrimaryTextActive: primaryColors[10],\n    colorSuccessBg: successColors[1],\n    colorSuccessBgHover: successColors[2],\n    colorSuccessBorder: successColors[3],\n    colorSuccessBorderHover: successColors[4],\n    colorSuccessHover: successColors[4],\n    colorSuccess: successColors[6],\n    colorSuccessActive: successColors[7],\n    colorSuccessTextHover: successColors[8],\n    colorSuccessText: successColors[9],\n    colorSuccessTextActive: successColors[10],\n    colorErrorBg: errorColors[1],\n    colorErrorBgHover: errorColors[2],\n    colorErrorBgFilledHover,\n    colorErrorBgActive: errorColors[3],\n    colorErrorBorder: errorColors[3],\n    colorErrorBorderHover: errorColors[4],\n    colorErrorHover: errorColors[5],\n    colorError: errorColors[6],\n    colorErrorActive: errorColors[7],\n    colorErrorTextHover: errorColors[8],\n    colorErrorText: errorColors[9],\n    colorErrorTextActive: errorColors[10],\n    colorWarningBg: warningColors[1],\n    colorWarningBgHover: warningColors[2],\n    colorWarningBorder: warningColors[3],\n    colorWarningBorderHover: warningColors[4],\n    colorWarningHover: warningColors[4],\n    colorWarning: warningColors[6],\n    colorWarningActive: warningColors[7],\n    colorWarningTextHover: warningColors[8],\n    colorWarningText: warningColors[9],\n    colorWarningTextActive: warningColors[10],\n    colorInfoBg: infoColors[1],\n    colorInfoBgHover: infoColors[2],\n    colorInfoBorder: infoColors[3],\n    colorInfoBorderHover: infoColors[4],\n    colorInfoHover: infoColors[4],\n    colorInfo: infoColors[6],\n    colorInfoActive: infoColors[7],\n    colorInfoTextHover: infoColors[8],\n    colorInfoText: infoColors[9],\n    colorInfoTextActive: infoColors[10],\n    colorLinkHover: linkColors[4],\n    colorLink: linkColors[6],\n    colorLinkActive: linkColors[7],\n    colorBgMask: new FastColor('#000').setA(0.45).toRgbString(),\n    colorWhite: '#fff'\n  });\n}", "const genRadius = radiusBase => {\n  let radiusLG = radiusBase;\n  let radiusSM = radiusBase;\n  let radiusXS = radiusBase;\n  let radiusOuter = radiusBase;\n  // radiusLG\n  if (radiusBase < 6 && radiusBase >= 5) {\n    radiusLG = radiusBase + 1;\n  } else if (radiusBase < 16 && radiusBase >= 6) {\n    radiusLG = radiusBase + 2;\n  } else if (radiusBase >= 16) {\n    radiusLG = 16;\n  }\n  // radiusSM\n  if (radiusBase < 7 && radiusBase >= 5) {\n    radiusSM = 4;\n  } else if (radiusBase < 8 && radiusBase >= 7) {\n    radiusSM = 5;\n  } else if (radiusBase < 14 && radiusBase >= 8) {\n    radiusSM = 6;\n  } else if (radiusBase < 16 && radiusBase >= 14) {\n    radiusSM = 7;\n  } else if (radiusBase >= 16) {\n    radiusSM = 8;\n  }\n  // radiusXS\n  if (radiusBase < 6 && radiusBase >= 2) {\n    radiusXS = 1;\n  } else if (radiusBase >= 6) {\n    radiusXS = 2;\n  }\n  // radiusOuter\n  if (radiusBase > 4 && radiusBase < 8) {\n    radiusOuter = 4;\n  } else if (radiusBase >= 8) {\n    radiusOuter = 6;\n  }\n  return {\n    borderRadius: radiusBase,\n    borderRadiusXS: radiusXS,\n    borderRadiusSM: radiusSM,\n    borderRadiusLG: radiusLG,\n    borderRadiusOuter: radiusOuter\n  };\n};\nexport default genRadius;", "import genRadius from './genRadius';\nexport default function genCommonMapToken(token) {\n  const {\n    motionUnit,\n    motionBase,\n    borderRadius,\n    lineWidth\n  } = token;\n  return Object.assign({\n    // motion\n    motionDurationFast: `${(motionBase + motionUnit).toFixed(1)}s`,\n    motionDurationMid: `${(motionBase + motionUnit * 2).toFixed(1)}s`,\n    motionDurationSlow: `${(motionBase + motionUnit * 3).toFixed(1)}s`,\n    // line\n    lineWidthBold: lineWidth + 1\n  }, genRadius(borderRadius));\n}", "const genControlHeight = token => {\n  const {\n    controlHeight\n  } = token;\n  return {\n    controlHeightSM: controlHeight * 0.75,\n    controlHeightXS: controlHeight * 0.5,\n    controlHeightLG: controlHeight * 1.25\n  };\n};\nexport default genControlHeight;", "export function getLineHeight(fontSize) {\n  return (fontSize + 8) / fontSize;\n}\n// https://zhuanlan.zhihu.com/p/32746810\nexport default function getFontSizes(base) {\n  const fontSizes = Array.from({\n    length: 10\n  }).map((_, index) => {\n    const i = index - 1;\n    const baseSize = base * Math.pow(Math.E, i / 5);\n    const intSize = index > 1 ? Math.floor(baseSize) : Math.ceil(baseSize);\n    // Convert to even\n    return Math.floor(intSize / 2) * 2;\n  });\n  fontSizes[1] = base;\n  return fontSizes.map(size => ({\n    size,\n    lineHeight: getLineHeight(size)\n  }));\n}", "import genFontSizes from './genFontSizes';\nconst genFontMapToken = fontSize => {\n  const fontSizePairs = genFontSizes(fontSize);\n  const fontSizes = fontSizePairs.map(pair => pair.size);\n  const lineHeights = fontSizePairs.map(pair => pair.lineHeight);\n  const fontSizeMD = fontSizes[1];\n  const fontSizeSM = fontSizes[0];\n  const fontSizeLG = fontSizes[2];\n  const lineHeight = lineHeights[1];\n  const lineHeightSM = lineHeights[0];\n  const lineHeightLG = lineHeights[2];\n  return {\n    fontSizeSM,\n    fontSize: fontSizeMD,\n    fontSizeLG,\n    fontSizeXL: fontSizes[3],\n    fontSizeHeading1: fontSizes[6],\n    fontSizeHeading2: fontSizes[5],\n    fontSizeHeading3: fontSizes[4],\n    fontSizeHeading4: fontSizes[3],\n    fontSizeHeading5: fontSizes[2],\n    lineHeight,\n    lineHeightLG,\n    lineHeightSM,\n    fontHeight: Math.round(lineHeight * fontSizeMD),\n    fontHeightLG: Math.round(lineHeightLG * fontSizeLG),\n    fontHeightSM: Math.round(lineHeightSM * fontSizeSM),\n    lineHeightHeading1: lineHeights[6],\n    lineHeightHeading2: lineHeights[5],\n    lineHeightHeading3: lineHeights[4],\n    lineHeightHeading4: lineHeights[3],\n    lineHeightHeading5: lineHeights[2]\n  };\n};\nexport default genFontMapToken;", "export default function genSizeMapToken(token) {\n  const {\n    sizeUnit,\n    sizeStep\n  } = token;\n  return {\n    sizeXXL: sizeUnit * (sizeStep + 8),\n    // 48\n    sizeXL: sizeUnit * (sizeStep + 4),\n    // 32\n    sizeLG: sizeUnit * (sizeStep + 2),\n    // 24\n    sizeMD: sizeUnit * (sizeStep + 1),\n    // 20\n    sizeMS: sizeUnit * sizeStep,\n    // 16\n    size: sizeUnit * sizeStep,\n    // 16\n    sizeSM: sizeUnit * (sizeStep - 1),\n    // 12\n    sizeXS: sizeUnit * (sizeStep - 2),\n    // 8\n    sizeXXS: sizeUnit * (sizeStep - 3) // 4\n  };\n}", "import { generate } from '@ant-design/colors';\nimport { getAlphaColor, getSolidColor } from './colorAlgorithm';\nexport const generateColorPalettes = baseColor => {\n  const colors = generate(baseColor);\n  return {\n    1: colors[0],\n    2: colors[1],\n    3: colors[2],\n    4: colors[3],\n    5: colors[4],\n    6: colors[5],\n    7: colors[6],\n    8: colors[4],\n    9: colors[5],\n    10: colors[6]\n    // 8: colors[7],\n    // 9: colors[8],\n    // 10: colors[9],\n  };\n};\nexport const generateNeutralColorPalettes = (bgBaseColor, textBaseColor) => {\n  const colorBgBase = bgBaseColor || '#fff';\n  const colorTextBase = textBaseColor || '#000';\n  return {\n    colorBgBase,\n    colorTextBase,\n    colorText: getAlphaColor(colorTextBase, 0.88),\n    colorTextSecondary: getAlphaColor(colorTextBase, 0.65),\n    colorTextTertiary: getAlphaColor(colorTextBase, 0.45),\n    colorTextQuaternary: getAlphaColor(colorTextBase, 0.25),\n    colorFill: getAlphaColor(colorTextBase, 0.15),\n    colorFillSecondary: getAlphaColor(colorTextBase, 0.06),\n    colorFillTertiary: getAlphaColor(colorTextBase, 0.04),\n    colorFillQuaternary: getAlphaColor(colorTextBase, 0.02),\n    colorBgSolid: getAlphaColor(colorTextBase, 1),\n    colorBgSolidHover: getAlphaColor(colorTextBase, 0.75),\n    colorBgSolidActive: getAlphaColor(colorTextBase, 0.95),\n    colorBgLayout: getSolidColor(colorBgBase, 4),\n    colorBgContainer: getSolidColor(colorBgBase, 0),\n    colorBgElevated: getSolidColor(colorBgBase, 0),\n    colorBgSpotlight: getAlphaColor(colorTextBase, 0.85),\n    colorBgBlur: 'transparent',\n    colorBorder: getSolidColor(colorBgBase, 15),\n    colorBorderSecondary: getSolidColor(colorBgBase, 6)\n  };\n};", "import { FastColor } from '@ant-design/fast-color';\nexport const getAlphaColor = (baseColor, alpha) => new FastColor(baseColor).setA(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new FastColor(baseColor);\n  return instance.darken(brightness).toHexString();\n};", "import * as React from 'react';\nexport const defaultPrefixCls = 'ant';\nexport const defaultIconPrefixCls = 'anticon';\nexport const Variants = ['outlined', 'borderless', 'filled', 'underlined'];\nconst defaultGetPrefixCls = (suffixCls, customizePrefixCls) => {\n  if (customizePrefixCls) {\n    return customizePrefixCls;\n  }\n  return suffixCls ? `${defaultPrefixCls}-${suffixCls}` : defaultPrefixCls;\n};\n// zombieJ: 🚨 Do not pass `defaultRenderEmpty` here since it will cause circular dependency.\nexport const ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls,\n  iconPrefixCls: defaultIconPrefixCls\n});\nexport const {\n  Consumer: ConfigConsumer\n} = ConfigContext;\nconst EMPTY_OBJECT = {};\n/**\n * Get ConfigProvider configured component props.\n * This help to reduce bundle size for saving `?.` operator.\n * Do not use as `useMemo` deps since we do not cache the object here.\n *\n * NOTE: not refactor this with `useMemo` since memo will cost another memory space,\n * which will waste both compare calculation & memory.\n */\nexport function useComponentConfig(propName) {\n  const context = React.useContext(ConfigContext);\n  const {\n    getPrefixCls,\n    direction,\n    getPopupContainer\n  } = context;\n  const propValue = context[propName];\n  return Object.assign(Object.assign({\n    classNames: EMPTY_OBJECT,\n    styles: EMPTY_OBJECT\n  }, propValue), {\n    getPrefixCls,\n    direction,\n    getPopupContainer\n  });\n}", "import { generate } from '@ant-design/colors';\nimport { FastColor } from '@ant-design/fast-color';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport warning from '../_util/warning';\nconst dynamicStyleMark = `-ant-${Date.now()}-${Math.random()}`;\nexport function getStyle(globalPrefixCls, theme) {\n  const variables = {};\n  const formatColor = (color, updater) => {\n    let clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n  const fillColor = (colorVal, type) => {\n    const baseColor = new FastColor(colorVal);\n    const colorPalettes = generate(baseColor.toRgbString());\n    variables[`${type}-color`] = formatColor(baseColor);\n    variables[`${type}-color-disabled`] = colorPalettes[1];\n    variables[`${type}-color-hover`] = colorPalettes[4];\n    variables[`${type}-color-active`] = colorPalettes[6];\n    variables[`${type}-color-outline`] = baseColor.clone().setA(0.2).toRgbString();\n    variables[`${type}-color-deprecated-bg`] = colorPalettes[0];\n    variables[`${type}-color-deprecated-border`] = colorPalettes[2];\n  };\n  // ================ Primary Color ================\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    const primaryColor = new FastColor(theme.primaryColor);\n    const primaryColors = generate(primaryColor.toRgbString());\n    // Legacy - We should use semantic naming standard\n    primaryColors.forEach((color, index) => {\n      variables[`primary-${index + 1}`] = color;\n    });\n    // Deprecated\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, c => c.lighten(35));\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, c => c.lighten(20));\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, c => c.tint(20));\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, c => c.tint(50));\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, c => c.setA(c.a * 0.12));\n    const primaryActiveColor = new FastColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, c => c.setA(c.a * 0.3));\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, c => c.darken(2));\n  }\n  // ================ Success Color ================\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  }\n  // ================ Warning Color ================\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  }\n  // ================= Error Color =================\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  }\n  // ================= Info Color ==================\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  }\n  // Convert to css variables\n  const cssList = Object.keys(variables).map(key => `--${globalPrefixCls}-${key}: ${variables[key]};`);\n  return `\n  :root {\n    ${cssList.join('\\n')}\n  }\n  `.trim();\n}\nexport function registerTheme(globalPrefixCls, theme) {\n  const style = getStyle(globalPrefixCls, theme);\n  if (canUseDom()) {\n    updateCSS(style, `${dynamicStyleMark}-dynamic-theme`);\n  } else {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.') : void 0;\n  }\n}", "\"use client\";\n\nimport * as React from 'react';\nconst DisabledContext = /*#__PURE__*/React.createContext(false);\nexport const DisabledContextProvider = ({\n  children,\n  disabled\n}) => {\n  const originDisabled = React.useContext(DisabledContext);\n  return /*#__PURE__*/React.createElement(DisabledContext.Provider, {\n    value: disabled !== null && disabled !== void 0 ? disabled : originDisabled\n  }, children);\n};\nexport default DisabledContext;", "import { useContext } from 'react';\nimport DisabledContext from '../DisabledContext';\nimport SizeContext from '../SizeContext';\nfunction useConfig() {\n  const componentDisabled = useContext(DisabledContext);\n  const componentSize = useContext(SizeContext);\n  return {\n    componentDisabled,\n    componentSize\n  };\n}\nexport default useConfig;", "\"use client\";\n\nimport * as React from 'react';\nconst SizeContext = /*#__PURE__*/React.createContext(undefined);\nexport const SizeContextProvider = ({\n  children,\n  size\n}) => {\n  const originSize = React.useContext(SizeContext);\n  return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n    value: size || originSize\n  }, children);\n};\nexport default SizeContext;", "import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { devUseWarning } from '../../_util/warning';\nimport { defaultConfig } from '../../theme/internal';\nimport useThemeKey from './useThemeKey';\nexport default function useTheme(theme, parentTheme, config) {\n  var _a, _b;\n  const warning = devUseWarning('ConfigProvider');\n  const themeConfig = theme || {};\n  const parentThemeConfig = themeConfig.inherit === false || !parentTheme ? Object.assign(Object.assign({}, defaultConfig), {\n    hashed: (_a = parentTheme === null || parentTheme === void 0 ? void 0 : parentTheme.hashed) !== null && _a !== void 0 ? _a : defaultConfig.hashed,\n    cssVar: parentTheme === null || parentTheme === void 0 ? void 0 : parentTheme.cssVar\n  }) : parentTheme;\n  const themeKey = useThemeKey();\n  if (process.env.NODE_ENV !== 'production') {\n    const cssVarEnabled = themeConfig.cssVar || parentThemeConfig.cssVar;\n    const validKey = !!(typeof themeConfig.cssVar === 'object' && ((_b = themeConfig.cssVar) === null || _b === void 0 ? void 0 : _b.key) || themeKey);\n    process.env.NODE_ENV !== \"production\" ? warning(!cssVarEnabled || validKey, 'breaking', 'Missing key in `cssVar` config. Please upgrade to React 18 or set `cssVar.key` manually in each ConfigProvider inside `cssVar` enabled ConfigProvider.') : void 0;\n  }\n  return useMemo(() => {\n    var _a, _b;\n    if (!theme) {\n      return parentTheme;\n    }\n    // Override\n    const mergedComponents = Object.assign({}, parentThemeConfig.components);\n    Object.keys(theme.components || {}).forEach(componentName => {\n      mergedComponents[componentName] = Object.assign(Object.assign({}, mergedComponents[componentName]), theme.components[componentName]);\n    });\n    const cssVarKey = `css-var-${themeKey.replace(/:/g, '')}`;\n    const mergedCssVar = ((_a = themeConfig.cssVar) !== null && _a !== void 0 ? _a : parentThemeConfig.cssVar) && Object.assign(Object.assign(Object.assign({\n      prefix: config === null || config === void 0 ? void 0 : config.prefixCls\n    }, typeof parentThemeConfig.cssVar === 'object' ? parentThemeConfig.cssVar : {}), typeof themeConfig.cssVar === 'object' ? themeConfig.cssVar : {}), {\n      key: typeof themeConfig.cssVar === 'object' && ((_b = themeConfig.cssVar) === null || _b === void 0 ? void 0 : _b.key) || cssVarKey\n    });\n    // Base token\n    return Object.assign(Object.assign(Object.assign({}, parentThemeConfig), themeConfig), {\n      token: Object.assign(Object.assign({}, parentThemeConfig.token), themeConfig.token),\n      components: mergedComponents,\n      cssVar: mergedCssVar\n    });\n  }, [themeConfig, parentThemeConfig], (prev, next) => prev.some((prevTheme, index) => {\n    const nextTheme = next[index];\n    return !isEqual(prevTheme, nextTheme, true);\n  }));\n}", "import { useStyleRegister } from '@ant-design/cssinjs';\nimport { genCalc as calc, mergeToken, statisticToken, statistic } from '@ant-design/cssinjs-utils';\nimport { PresetColors } from './interface';\nimport { getLineHeight } from './themes/shared/genFontSizes';\nimport useToken from './useToken';\nimport { genComponentStyleHook, genStyleHooks, genSubStyleComponent } from './util/genStyleUtils';\nimport genPresetColor from './util/genPresetColor';\nimport useResetIconStyle from './util/useResetIconStyle';\nexport { DesignTokenContext, defaultConfig } from './context';\nexport {\n// generators\ngenComponentStyleHook, genSubStyleComponent, genPresetColor, genStyleHooks,\n// utils\nmergeToken, statisticToken, calc, getLineHeight,\n// hooks\nuseResetIconStyle, useStyleRegister, useToken,\n// constant\nPresetColors, statistic };", "export const PresetColors = ['blue', 'purple', 'cyan', 'green', 'magenta', 'pink', 'red', 'orange', 'yellow', 'volcano', 'geekblue', 'lime', 'gold'];", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport { useCacheToken } from '@ant-design/cssinjs';\nimport version from '../version';\nimport { defaultTheme, DesignTokenContext } from './context';\nimport defaultSeedToken from './themes/seed';\nimport formatToken from './util/alias';\nexport const unitless = {\n  lineHeight: true,\n  lineHeightSM: true,\n  lineHeightLG: true,\n  lineHeightHeading1: true,\n  lineHeightHeading2: true,\n  lineHeightHeading3: true,\n  lineHeightHeading4: true,\n  lineHeightHeading5: true,\n  opacityLoading: true,\n  fontWeightStrong: true,\n  zIndexPopupBase: true,\n  zIndexBase: true,\n  opacityImage: true\n};\nexport const ignore = {\n  size: true,\n  sizeSM: true,\n  sizeLG: true,\n  sizeMD: true,\n  sizeXS: true,\n  sizeXXS: true,\n  sizeMS: true,\n  sizeXL: true,\n  sizeXXL: true,\n  sizeUnit: true,\n  sizeStep: true,\n  motionBase: true,\n  motionUnit: true\n};\nconst preserve = {\n  screenXS: true,\n  screenXSMin: true,\n  screenXSMax: true,\n  screenSM: true,\n  screenSMMin: true,\n  screenSMMax: true,\n  screenMD: true,\n  screenMDMin: true,\n  screenMDMax: true,\n  screenLG: true,\n  screenLGMin: true,\n  screenLGMax: true,\n  screenXL: true,\n  screenXLMin: true,\n  screenXLMax: true,\n  screenXXL: true,\n  screenXXLMin: true\n};\nexport const getComputedToken = (originToken, overrideToken, theme) => {\n  const derivativeToken = theme.getDerivativeToken(originToken);\n  const {\n      override\n    } = overrideToken,\n    components = __rest(overrideToken, [\"override\"]);\n  // Merge with override\n  let mergedDerivativeToken = Object.assign(Object.assign({}, derivativeToken), {\n    override\n  });\n  // Format if needed\n  mergedDerivativeToken = formatToken(mergedDerivativeToken);\n  if (components) {\n    Object.entries(components).forEach(([key, value]) => {\n      const {\n          theme: componentTheme\n        } = value,\n        componentTokens = __rest(value, [\"theme\"]);\n      let mergedComponentToken = componentTokens;\n      if (componentTheme) {\n        mergedComponentToken = getComputedToken(Object.assign(Object.assign({}, mergedDerivativeToken), componentTokens), {\n          override: componentTokens\n        }, componentTheme);\n      }\n      mergedDerivativeToken[key] = mergedComponentToken;\n    });\n  }\n  return mergedDerivativeToken;\n};\n// ================================== Hook ==================================\nexport default function useToken() {\n  const {\n    token: rootDesignToken,\n    hashed,\n    theme,\n    override,\n    cssVar\n  } = React.useContext(DesignTokenContext);\n  const salt = `${version}-${hashed || ''}`;\n  const mergedTheme = theme || defaultTheme;\n  const [token, hashId, realToken] = useCacheToken(mergedTheme, [defaultSeedToken, rootDesignToken], {\n    salt,\n    override,\n    getComputedToken,\n    // formatToken will not be consumed after 1.15.0 with getComputedToken.\n    // But token will break if @ant-design/cssinjs is under 1.15.0 without it\n    formatToken,\n    cssVar: cssVar && {\n      prefix: cssVar.prefix,\n      key: cssVar.key,\n      unitless,\n      ignore,\n      preserve\n    }\n  });\n  return [mergedTheme, realToken, hashed ? hashId : '', token, cssVar];\n}", "export default '5.26.5';", "\"use client\";\n\n// @ts-ignore\nimport version from './version';\nexport default version;", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { FastColor } from '@ant-design/fast-color';\nimport seedToken from '../themes/seed';\nimport getAlphaColor from './getAlphaColor';\n/**\n * Seed (designer) > Derivative (designer) > <PERSON><PERSON> (developer).\n *\n * Merge seed & derivative & override token and generate alias token for developer.\n */\nexport default function formatToken(derivativeToken) {\n  const {\n      override\n    } = derivativeToken,\n    restToken = __rest(derivativeToken, [\"override\"]);\n  const overrideTokens = Object.assign({}, override);\n  Object.keys(seedToken).forEach(token => {\n    delete overrideTokens[token];\n  });\n  const mergedToken = Object.assign(Object.assign({}, restToken), overrideTokens);\n  const screenXS = 480;\n  const screenSM = 576;\n  const screenMD = 768;\n  const screenLG = 992;\n  const screenXL = 1200;\n  const screenXXL = 1600;\n  // Motion\n  if (mergedToken.motion === false) {\n    const fastDuration = '0s';\n    mergedToken.motionDurationFast = fastDuration;\n    mergedToken.motionDurationMid = fastDuration;\n    mergedToken.motionDurationSlow = fastDuration;\n  }\n  // Generate alias token\n  const aliasToken = Object.assign(Object.assign(Object.assign({}, mergedToken), {\n    // ============== Background ============== //\n    colorFillContent: mergedToken.colorFillSecondary,\n    colorFillContentHover: mergedToken.colorFill,\n    colorFillAlter: mergedToken.colorFillQuaternary,\n    colorBgContainerDisabled: mergedToken.colorFillTertiary,\n    // ============== Split ============== //\n    colorBorderBg: mergedToken.colorBgContainer,\n    colorSplit: getAlphaColor(mergedToken.colorBorderSecondary, mergedToken.colorBgContainer),\n    // ============== Text ============== //\n    colorTextPlaceholder: mergedToken.colorTextQuaternary,\n    colorTextDisabled: mergedToken.colorTextQuaternary,\n    colorTextHeading: mergedToken.colorText,\n    colorTextLabel: mergedToken.colorTextSecondary,\n    colorTextDescription: mergedToken.colorTextTertiary,\n    colorTextLightSolid: mergedToken.colorWhite,\n    colorHighlight: mergedToken.colorError,\n    colorBgTextHover: mergedToken.colorFillSecondary,\n    colorBgTextActive: mergedToken.colorFill,\n    colorIcon: mergedToken.colorTextTertiary,\n    colorIconHover: mergedToken.colorText,\n    colorErrorOutline: getAlphaColor(mergedToken.colorErrorBg, mergedToken.colorBgContainer),\n    colorWarningOutline: getAlphaColor(mergedToken.colorWarningBg, mergedToken.colorBgContainer),\n    // Font\n    fontSizeIcon: mergedToken.fontSizeSM,\n    // Line\n    lineWidthFocus: mergedToken.lineWidth * 3,\n    // Control\n    lineWidth: mergedToken.lineWidth,\n    controlOutlineWidth: mergedToken.lineWidth * 2,\n    // Checkbox size and expand icon size\n    controlInteractiveSize: mergedToken.controlHeight / 2,\n    controlItemBgHover: mergedToken.colorFillTertiary,\n    controlItemBgActive: mergedToken.colorPrimaryBg,\n    controlItemBgActiveHover: mergedToken.colorPrimaryBgHover,\n    controlItemBgActiveDisabled: mergedToken.colorFill,\n    controlTmpOutline: mergedToken.colorFillQuaternary,\n    controlOutline: getAlphaColor(mergedToken.colorPrimaryBg, mergedToken.colorBgContainer),\n    lineType: mergedToken.lineType,\n    borderRadius: mergedToken.borderRadius,\n    borderRadiusXS: mergedToken.borderRadiusXS,\n    borderRadiusSM: mergedToken.borderRadiusSM,\n    borderRadiusLG: mergedToken.borderRadiusLG,\n    fontWeightStrong: 600,\n    opacityLoading: 0.65,\n    linkDecoration: 'none',\n    linkHoverDecoration: 'none',\n    linkFocusDecoration: 'none',\n    controlPaddingHorizontal: 12,\n    controlPaddingHorizontalSM: 8,\n    paddingXXS: mergedToken.sizeXXS,\n    paddingXS: mergedToken.sizeXS,\n    paddingSM: mergedToken.sizeSM,\n    padding: mergedToken.size,\n    paddingMD: mergedToken.sizeMD,\n    paddingLG: mergedToken.sizeLG,\n    paddingXL: mergedToken.sizeXL,\n    paddingContentHorizontalLG: mergedToken.sizeLG,\n    paddingContentVerticalLG: mergedToken.sizeMS,\n    paddingContentHorizontal: mergedToken.sizeMS,\n    paddingContentVertical: mergedToken.sizeSM,\n    paddingContentHorizontalSM: mergedToken.size,\n    paddingContentVerticalSM: mergedToken.sizeXS,\n    marginXXS: mergedToken.sizeXXS,\n    marginXS: mergedToken.sizeXS,\n    marginSM: mergedToken.sizeSM,\n    margin: mergedToken.size,\n    marginMD: mergedToken.sizeMD,\n    marginLG: mergedToken.sizeLG,\n    marginXL: mergedToken.sizeXL,\n    marginXXL: mergedToken.sizeXXL,\n    boxShadow: `\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    `,\n    boxShadowSecondary: `\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    `,\n    boxShadowTertiary: `\n      0 1px 2px 0 rgba(0, 0, 0, 0.03),\n      0 1px 6px -1px rgba(0, 0, 0, 0.02),\n      0 2px 4px 0 rgba(0, 0, 0, 0.02)\n    `,\n    screenXS,\n    screenXSMin: screenXS,\n    screenXSMax: screenSM - 1,\n    screenSM,\n    screenSMMin: screenSM,\n    screenSMMax: screenMD - 1,\n    screenMD,\n    screenMDMin: screenMD,\n    screenMDMax: screenLG - 1,\n    screenLG,\n    screenLGMin: screenLG,\n    screenLGMax: screenXL - 1,\n    screenXL,\n    screenXLMin: screenXL,\n    screenXLMax: screenXXL - 1,\n    screenXXL,\n    screenXXLMin: screenXXL,\n    boxShadowPopoverArrow: '2px 2px 5px rgba(0, 0, 0, 0.05)',\n    boxShadowCard: `\n      0 1px 2px -2px ${new FastColor('rgba(0, 0, 0, 0.16)').toRgbString()},\n      0 3px 6px 0 ${new FastColor('rgba(0, 0, 0, 0.12)').toRgbString()},\n      0 5px 12px 4px ${new FastColor('rgba(0, 0, 0, 0.09)').toRgbString()}\n    `,\n    boxShadowDrawerRight: `\n      -6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      -3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      -9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    `,\n    boxShadowDrawerLeft: `\n      6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    `,\n    boxShadowDrawerUp: `\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    `,\n    boxShadowDrawerDown: `\n      0 -6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 -3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 -9px 28px 8px rgba(0, 0, 0, 0.05)\n    `,\n    boxShadowTabsOverflowLeft: 'inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)',\n    boxShadowTabsOverflowRight: 'inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)',\n    boxShadowTabsOverflowTop: 'inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)',\n    boxShadowTabsOverflowBottom: 'inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)'\n  }), overrideTokens);\n  return aliasToken;\n}", "import { FastColor } from '@ant-design/fast-color';\nfunction isStableColor(color) {\n  return color >= 0 && color <= 255;\n}\nfunction getAlphaColor(frontColor, backgroundColor) {\n  const {\n    r: fR,\n    g: fG,\n    b: fB,\n    a: originAlpha\n  } = new FastColor(frontColor).toRgb();\n  if (originAlpha < 1) {\n    return frontColor;\n  }\n  const {\n    r: bR,\n    g: bG,\n    b: bB\n  } = new FastColor(backgroundColor).toRgb();\n  for (let fA = 0.01; fA <= 1; fA += 0.01) {\n    const r = Math.round((fR - bR * (1 - fA)) / fA);\n    const g = Math.round((fG - bG * (1 - fA)) / fA);\n    const b = Math.round((fB - bB * (1 - fA)) / fA);\n    if (isStableColor(r) && isStableColor(g) && isStableColor(b)) {\n      return new FastColor({\n        r,\n        g,\n        b,\n        a: Math.round(fA * 100) / 100\n      }).toRgbString();\n    }\n  }\n  // fallback\n  /* istanbul ignore next */\n  return new FastColor({\n    r: fR,\n    g: fG,\n    b: fB,\n    a: 1\n  }).toRgbString();\n}\nexport default getAlphaColor;", "import { useContext } from 'react';\nimport { genStyleUtils } from '@ant-design/cssinjs-utils';\nimport { ConfigContext, defaultIconPrefixCls } from '../../config-provider/context';\nimport { genCommonStyle, genIconStyle, genLinkStyle } from '../../style';\nimport useLocalToken, { unitless } from '../useToken';\nexport const {\n  genStyleHooks,\n  genComponentStyleHook,\n  genSubStyleComponent\n} = genStyleUtils({\n  usePrefix: () => {\n    const {\n      getPrefixCls,\n      iconPrefixCls\n    } = useContext(ConfigContext);\n    const rootPrefixCls = getPrefixCls();\n    return {\n      rootPrefixCls,\n      iconPrefixCls\n    };\n  },\n  useToken: () => {\n    const [theme, realToken, hashId, token, cssVar] = useLocalToken();\n    return {\n      theme,\n      realToken,\n      hashId,\n      token,\n      cssVar\n    };\n  },\n  useCSP: () => {\n    const {\n      csp\n    } = useContext(ConfigContext);\n    return csp !== null && csp !== void 0 ? csp : {};\n  },\n  getResetStyles: (token, config) => {\n    var _a;\n    const linkStyle = genLinkStyle(token);\n    return [linkStyle, {\n      '&': linkStyle\n    }, genIconStyle((_a = config === null || config === void 0 ? void 0 : config.prefix.iconPrefixCls) !== null && _a !== void 0 ? _a : defaultIconPrefixCls)];\n  },\n  getCommonStyle: genCommonStyle,\n  getCompUnitless: () => unitless\n});", "\"use client\";\n\nimport { unit } from '@ant-design/cssinjs';\nexport const textEllipsis = {\n  overflow: 'hidden',\n  whiteSpace: 'nowrap',\n  textOverflow: 'ellipsis'\n};\nexport const resetComponent = (token, needInheritFontFamily = false) => ({\n  boxSizing: 'border-box',\n  margin: 0,\n  padding: 0,\n  color: token.colorText,\n  fontSize: token.fontSize,\n  // font-variant: @font-variant-base;\n  lineHeight: token.lineHeight,\n  listStyle: 'none',\n  // font-feature-settings: @font-feature-settings-base;\n  fontFamily: needInheritFontFamily ? 'inherit' : token.fontFamily\n});\nexport const resetIcon = () => ({\n  display: 'inline-flex',\n  alignItems: 'center',\n  color: 'inherit',\n  fontStyle: 'normal',\n  lineHeight: 0,\n  textAlign: 'center',\n  textTransform: 'none',\n  // for SVG icon, see https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\n  verticalAlign: '-0.125em',\n  textRendering: 'optimizeLegibility',\n  '-webkit-font-smoothing': 'antialiased',\n  '-moz-osx-font-smoothing': 'grayscale',\n  '> *': {\n    lineHeight: 1\n  },\n  svg: {\n    display: 'inline-block'\n  }\n});\nexport const clearFix = () => ({\n  // https://github.com/ant-design/ant-design/issues/21301#issuecomment-583955229\n  '&::before': {\n    display: 'table',\n    content: '\"\"'\n  },\n  '&::after': {\n    // https://github.com/ant-design/ant-design/issues/21864\n    display: 'table',\n    clear: 'both',\n    content: '\"\"'\n  }\n});\nexport const genLinkStyle = token => ({\n  a: {\n    color: token.colorLink,\n    textDecoration: token.linkDecoration,\n    backgroundColor: 'transparent',\n    // remove the gray background on active links in IE 10.\n    outline: 'none',\n    cursor: 'pointer',\n    transition: `color ${token.motionDurationSlow}`,\n    '-webkit-text-decoration-skip': 'objects',\n    // remove gaps in links underline in iOS 8+ and Safari 8+.\n    '&:hover': {\n      color: token.colorLinkHover\n    },\n    '&:active': {\n      color: token.colorLinkActive\n    },\n    '&:active, &:hover': {\n      textDecoration: token.linkHoverDecoration,\n      outline: 0\n    },\n    // https://github.com/ant-design/ant-design/issues/22503\n    '&:focus': {\n      textDecoration: token.linkFocusDecoration,\n      outline: 0\n    },\n    '&[disabled]': {\n      color: token.colorTextDisabled,\n      cursor: 'not-allowed'\n    }\n  }\n});\nexport const genCommonStyle = (token, componentPrefixCls, rootCls, resetFont) => {\n  const prefixSelector = `[class^=\"${componentPrefixCls}\"], [class*=\" ${componentPrefixCls}\"]`;\n  const rootPrefixSelector = rootCls ? `.${rootCls}` : prefixSelector;\n  const resetStyle = {\n    boxSizing: 'border-box',\n    '&::before, &::after': {\n      boxSizing: 'border-box'\n    }\n  };\n  let resetFontStyle = {};\n  if (resetFont !== false) {\n    resetFontStyle = {\n      fontFamily: token.fontFamily,\n      fontSize: token.fontSize\n    };\n  }\n  return {\n    [rootPrefixSelector]: Object.assign(Object.assign(Object.assign({}, resetFontStyle), resetStyle), {\n      [prefixSelector]: resetStyle\n    })\n  };\n};\nexport const genFocusOutline = (token, offset) => ({\n  outline: `${unit(token.lineWidthFocus)} solid ${token.colorPrimaryBorder}`,\n  outlineOffset: offset !== null && offset !== void 0 ? offset : 1,\n  transition: 'outline-offset 0s, outline 0s'\n});\nexport const genFocusStyle = (token, offset) => ({\n  '&:focus-visible': Object.assign({}, genFocusOutline(token, offset))\n});\nexport const genIconStyle = iconPrefixCls => ({\n  [`.${iconPrefixCls}`]: Object.assign(Object.assign({}, resetIcon()), {\n    [`.${iconPrefixCls} .${iconPrefixCls}-icon`]: {\n      display: 'block'\n    }\n  })\n});\nexport const operationUnit = token => Object.assign(Object.assign({\n  // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.\n  // And Typography use this to generate link style which should not do this.\n  color: token.colorLink,\n  textDecoration: token.linkDecoration,\n  outline: 'none',\n  cursor: 'pointer',\n  transition: `all ${token.motionDurationSlow}`,\n  border: 0,\n  padding: 0,\n  background: 'none',\n  userSelect: 'none'\n}, genFocusStyle(token)), {\n  '&:focus, &:hover': {\n    color: token.colorLinkHover\n  },\n  '&:active': {\n    color: token.colorLinkActive\n  }\n});", "import { PresetColors } from '../interface';\nexport default function genPresetColor(token, genCss) {\n  return PresetColors.reduce((prev, colorKey) => {\n    const lightColor = token[`${colorKey}1`];\n    const lightBorderColor = token[`${colorKey}3`];\n    const darkColor = token[`${colorKey}6`];\n    const textColor = token[`${colorKey}7`];\n    return Object.assign(Object.assign({}, prev), genCss(colorKey, {\n      lightColor,\n      lightBorderColor,\n      darkColor,\n      textColor\n    }));\n  }, {});\n}", "import { useStyleRegister } from '@ant-design/cssinjs';\nimport { genIconStyle } from '../../style';\nimport useToken from '../useToken';\nconst useResetIconStyle = (iconPrefixCls, csp) => {\n  const [theme, token] = useToken();\n  // Generate style for icons\n  return useStyleRegister({\n    theme,\n    token,\n    hashId: '',\n    path: ['ant-design-icons', iconPrefixCls],\n    nonce: () => csp === null || csp === void 0 ? void 0 : csp.nonce,\n    layer: {\n      name: 'antd'\n    }\n  }, () => [genIconStyle(iconPrefixCls)]);\n};\nexport default useResetIconStyle;", "import * as React from 'react';\nconst fullClone = Object.assign({}, React);\nconst {\n  useId\n} = fullClone;\nconst useEmptyId = () => '';\nconst useThemeKey = typeof useId === 'undefined' ? useEmptyId : useId;\nexport default useThemeKey;", "\"use client\";\n\nimport * as React from 'react';\nimport { Provider as MotionProvider } from 'rc-motion';\nimport { useToken } from '../theme/internal';\nconst MotionCacheContext = /*#__PURE__*/React.createContext(true);\nif (process.env.NODE_ENV !== 'production') {\n  MotionCacheContext.displayName = 'MotionCacheContext';\n}\nexport default function MotionWrapper(props) {\n  const parentMotion = React.useContext(MotionCacheContext);\n  const {\n    children\n  } = props;\n  const [, token] = useToken();\n  const {\n    motion\n  } = token;\n  const needWrapMotionProviderRef = React.useRef(false);\n  needWrapMotionProviderRef.current || (needWrapMotionProviderRef.current = parentMotion !== motion);\n  if (needWrapMotionProviderRef.current) {\n    return /*#__PURE__*/React.createElement(MotionCacheContext.Provider, {\n      value: motion\n    }, /*#__PURE__*/React.createElement(MotionProvider, {\n      motion: motion\n    }, children));\n  }\n  return children;\n}", "\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\n/**\n * Warning for ConfigProviderProps.\n * This will be empty function in production.\n */\nconst PropWarning = /*#__PURE__*/React.memo(({\n  dropdownMatchSelectWidth\n}) => {\n  const warning = devUseWarning('ConfigProvider');\n  warning.deprecated(dropdownMatchSelectWidth === undefined, 'dropdownMatchSelectWidth', 'popupMatchSelectWidth');\n  return null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  PropWarning.displayName = 'PropWarning';\n}\nexport default process.env.NODE_ENV !== 'production' ? PropWarning : () => null;", "import * as React from 'react';\nexport const LayoutContext = /*#__PURE__*/React.createContext({\n  siderHook: {\n    addSider: () => null,\n    removeSider: () => null\n  }\n});", "import toArray from \"rc-util/es/Children/toArray\";\nimport Sider from '../Sider';\nexport default function useHasSider(siders, children, hasSider) {\n  if (typeof hasSider === 'boolean') {\n    return hasSider;\n  }\n  if (siders.length) {\n    return true;\n  }\n  const childNodes = toArray(children);\n  return childNodes.some(node => node.type === Sider);\n}", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useContext, useEffect, useRef, useState } from 'react';\nimport BarsOutlined from \"@ant-design/icons/es/icons/BarsOutlined\";\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { addMediaQueryListener, removeMediaQueryListener } from '../_util/mediaQueryUtil';\nimport { ConfigContext } from '../config-provider';\nimport { LayoutContext } from './context';\nimport useStyle from './style/sider';\nconst dimensionMaxMap = {\n  xs: '479.98px',\n  sm: '575.98px',\n  md: '767.98px',\n  lg: '991.98px',\n  xl: '1199.98px',\n  xxl: '1599.98px'\n};\nconst isNumeric = value => !Number.isNaN(Number.parseFloat(value)) && isFinite(value);\nexport const SiderContext = /*#__PURE__*/React.createContext({});\nconst generateId = (() => {\n  let i = 0;\n  return (prefix = '') => {\n    i += 1;\n    return `${prefix}${i}`;\n  };\n})();\nconst Sider = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      trigger,\n      children,\n      defaultCollapsed = false,\n      theme = 'dark',\n      style = {},\n      collapsible = false,\n      reverseArrow = false,\n      width = 200,\n      collapsedWidth = 80,\n      zeroWidthTriggerStyle,\n      breakpoint,\n      onCollapse,\n      onBreakpoint\n    } = props,\n    otherProps = __rest(props, [\"prefixCls\", \"className\", \"trigger\", \"children\", \"defaultCollapsed\", \"theme\", \"style\", \"collapsible\", \"reverseArrow\", \"width\", \"collapsedWidth\", \"zeroWidthTriggerStyle\", \"breakpoint\", \"onCollapse\", \"onBreakpoint\"]);\n  const {\n    siderHook\n  } = useContext(LayoutContext);\n  const [collapsed, setCollapsed] = useState('collapsed' in props ? props.collapsed : defaultCollapsed);\n  const [below, setBelow] = useState(false);\n  useEffect(() => {\n    if ('collapsed' in props) {\n      setCollapsed(props.collapsed);\n    }\n  }, [props.collapsed]);\n  const handleSetCollapsed = (value, type) => {\n    if (!('collapsed' in props)) {\n      setCollapsed(value);\n    }\n    onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(value, type);\n  };\n  // =========================== Prefix ===========================\n  const {\n    getPrefixCls,\n    direction\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls('layout-sider', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // ========================= Responsive =========================\n  const responsiveHandlerRef = useRef(null);\n  responsiveHandlerRef.current = mql => {\n    setBelow(mql.matches);\n    onBreakpoint === null || onBreakpoint === void 0 ? void 0 : onBreakpoint(mql.matches);\n    if (collapsed !== mql.matches) {\n      handleSetCollapsed(mql.matches, 'responsive');\n    }\n  };\n  useEffect(() => {\n    function responsiveHandler(mql) {\n      var _a;\n      return (_a = responsiveHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(responsiveHandlerRef, mql);\n    }\n    let mql;\n    if (typeof (window === null || window === void 0 ? void 0 : window.matchMedia) !== 'undefined' && breakpoint && breakpoint in dimensionMaxMap) {\n      mql = window.matchMedia(`screen and (max-width: ${dimensionMaxMap[breakpoint]})`);\n      addMediaQueryListener(mql, responsiveHandler);\n      responsiveHandler(mql);\n    }\n    return () => {\n      removeMediaQueryListener(mql, responsiveHandler);\n    };\n  }, [breakpoint]); // in order to accept dynamic 'breakpoint' property, we need to add 'breakpoint' into dependency array.\n  useEffect(() => {\n    const uniqueId = generateId('ant-sider-');\n    siderHook.addSider(uniqueId);\n    return () => siderHook.removeSider(uniqueId);\n  }, []);\n  const toggle = () => {\n    handleSetCollapsed(!collapsed, 'clickTrigger');\n  };\n  const divProps = omit(otherProps, ['collapsed']);\n  const rawWidth = collapsed ? collapsedWidth : width;\n  // use \"px\" as fallback unit for width\n  const siderWidth = isNumeric(rawWidth) ? `${rawWidth}px` : String(rawWidth);\n  // special trigger when collapsedWidth == 0\n  const zeroWidthTrigger = parseFloat(String(collapsedWidth || 0)) === 0 ? (/*#__PURE__*/React.createElement(\"span\", {\n    onClick: toggle,\n    className: classNames(`${prefixCls}-zero-width-trigger`, `${prefixCls}-zero-width-trigger-${reverseArrow ? 'right' : 'left'}`),\n    style: zeroWidthTriggerStyle\n  }, trigger || /*#__PURE__*/React.createElement(BarsOutlined, null))) : null;\n  const reverseIcon = direction === 'rtl' === !reverseArrow;\n  const iconObj = {\n    expanded: reverseIcon ? /*#__PURE__*/React.createElement(RightOutlined, null) : /*#__PURE__*/React.createElement(LeftOutlined, null),\n    collapsed: reverseIcon ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null)\n  };\n  const status = collapsed ? 'collapsed' : 'expanded';\n  const defaultTrigger = iconObj[status];\n  const triggerDom = trigger !== null ? zeroWidthTrigger || (/*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-trigger`,\n    onClick: toggle,\n    style: {\n      width: siderWidth\n    }\n  }, trigger || defaultTrigger)) : null;\n  const divStyle = Object.assign(Object.assign({}, style), {\n    flex: `0 0 ${siderWidth}`,\n    maxWidth: siderWidth,\n    minWidth: siderWidth,\n    width: siderWidth\n  });\n  const siderCls = classNames(prefixCls, `${prefixCls}-${theme}`, {\n    [`${prefixCls}-collapsed`]: !!collapsed,\n    [`${prefixCls}-has-trigger`]: collapsible && trigger !== null && !zeroWidthTrigger,\n    [`${prefixCls}-below`]: !!below,\n    [`${prefixCls}-zero-width`]: parseFloat(siderWidth) === 0\n  }, className, hashId, cssVarCls);\n  const contextValue = React.useMemo(() => ({\n    siderCollapsed: collapsed\n  }), [collapsed]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(SiderContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(\"aside\", Object.assign({\n    className: siderCls\n  }, divProps, {\n    style: divStyle,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-children`\n  }, children), collapsible || below && zeroWidthTrigger ? triggerDom : null)));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Sider.displayName = 'Sider';\n}\nexport default Sider;", "export const addMediaQueryListener = (mql, handler) => {\n  // Don't delete here, please keep the code compatible\n  if (typeof (mql === null || mql === void 0 ? void 0 : mql.addEventListener) !== 'undefined') {\n    mql.addEventListener('change', handler);\n  } else if (typeof (mql === null || mql === void 0 ? void 0 : mql.addListener) !== 'undefined') {\n    mql.addListener(handler);\n  }\n};\nexport const removeMediaQueryListener = (mql, handler) => {\n  // Don't delete here, please keep the code compatible\n  if (typeof (mql === null || mql === void 0 ? void 0 : mql.removeEventListener) !== 'undefined') {\n    mql.removeEventListener('change', handler);\n  } else if (typeof (mql === null || mql === void 0 ? void 0 : mql.removeListener) !== 'undefined') {\n    mql.removeListener(handler);\n  }\n};", "import { unit } from '@ant-design/cssinjs';\nimport { DEPRECATED_TOKENS, prepareComponentToken } from '.';\nimport { genStyleHooks } from '../../theme/internal';\nconst genSiderStyle = token => {\n  const {\n    componentCls,\n    siderBg,\n    motionDurationMid,\n    motionDurationSlow,\n    antCls,\n    triggerHeight,\n    triggerColor,\n    triggerBg,\n    headerHeight,\n    zeroTriggerWidth,\n    zeroTriggerHeight,\n    borderRadiusLG,\n    lightSiderBg,\n    lightTriggerColor,\n    lightTriggerBg,\n    bodyBg\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'relative',\n      // fix firefox can't set width smaller than content on flex item\n      minWidth: 0,\n      background: siderBg,\n      transition: `all ${motionDurationMid}, background 0s`,\n      '&-has-trigger': {\n        paddingBottom: triggerHeight\n      },\n      '&-right': {\n        order: 1\n      },\n      [`${componentCls}-children`]: {\n        height: '100%',\n        // Hack for fixing margin collapse bug\n        // https://github.com/ant-design/ant-design/issues/7967\n        // solution from https://stackoverflow.com/a/33132624/3040605\n        marginTop: -0.1,\n        paddingTop: 0.1,\n        [`${antCls}-menu${antCls}-menu-inline-collapsed`]: {\n          width: 'auto'\n        }\n      },\n      [`&-zero-width ${componentCls}-children`]: {\n        overflow: 'hidden'\n      },\n      [`${componentCls}-trigger`]: {\n        position: 'fixed',\n        bottom: 0,\n        zIndex: 1,\n        height: triggerHeight,\n        color: triggerColor,\n        lineHeight: unit(triggerHeight),\n        textAlign: 'center',\n        background: triggerBg,\n        cursor: 'pointer',\n        transition: `all ${motionDurationMid}`\n      },\n      [`${componentCls}-zero-width-trigger`]: {\n        position: 'absolute',\n        top: headerHeight,\n        insetInlineEnd: token.calc(zeroTriggerWidth).mul(-1).equal(),\n        zIndex: 1,\n        width: zeroTriggerWidth,\n        height: zeroTriggerHeight,\n        color: triggerColor,\n        fontSize: token.fontSizeXL,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        background: siderBg,\n        borderRadius: `0 ${unit(borderRadiusLG)} ${unit(borderRadiusLG)} 0`,\n        cursor: 'pointer',\n        transition: `background ${motionDurationSlow} ease`,\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          background: 'transparent',\n          transition: `all ${motionDurationSlow}`,\n          content: '\"\"'\n        },\n        '&:hover::after': {\n          background: `rgba(255, 255, 255, 0.2)`\n        },\n        '&-right': {\n          insetInlineStart: token.calc(zeroTriggerWidth).mul(-1).equal(),\n          borderRadius: `${unit(borderRadiusLG)} 0 0 ${unit(borderRadiusLG)}`\n        }\n      },\n      // Light\n      '&-light': {\n        background: lightSiderBg,\n        [`${componentCls}-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg\n        },\n        [`${componentCls}-zero-width-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg,\n          border: `1px solid ${bodyBg}`,\n          // Safe to modify to any other color\n          borderInlineStart: 0\n        }\n      }\n    }\n  };\n};\nexport default genStyleHooks(['Layout', 'Sider'], token => [genSiderStyle(token)], prepareComponentToken, {\n  deprecatedTokens: DEPRECATED_TOKENS\n});", "import { unit } from '@ant-design/cssinjs';\nimport { genStyleHooks } from '../../theme/internal';\nconst genLayoutStyle = token => {\n  const {\n    antCls,\n    // .ant\n    componentCls,\n    // .ant-layout\n    colorText,\n    footerBg,\n    headerHeight,\n    headerPadding,\n    headerColor,\n    footerPadding,\n    fontSize,\n    bodyBg,\n    headerBg\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      flex: 'auto',\n      flexDirection: 'column',\n      /* fix firefox can't set height smaller than content on flex item */\n      minHeight: 0,\n      background: bodyBg,\n      '&, *': {\n        boxSizing: 'border-box'\n      },\n      [`&${componentCls}-has-sider`]: {\n        flexDirection: 'row',\n        [`> ${componentCls}, > ${componentCls}-content`]: {\n          // https://segmentfault.com/a/1190000019498300\n          width: 0\n        }\n      },\n      [`${componentCls}-header, &${componentCls}-footer`]: {\n        flex: '0 0 auto'\n      },\n      // RTL\n      '&-rtl': {\n        direction: 'rtl'\n      }\n    },\n    // ==================== Header ====================\n    [`${componentCls}-header`]: {\n      height: headerHeight,\n      padding: headerPadding,\n      color: headerColor,\n      lineHeight: unit(headerHeight),\n      background: headerBg,\n      // Other components/menu/style/index.less line:686\n      // Integration with header element so menu items have the same height\n      [`${antCls}-menu`]: {\n        lineHeight: 'inherit'\n      }\n    },\n    // ==================== Footer ====================\n    [`${componentCls}-footer`]: {\n      padding: footerPadding,\n      color: colorText,\n      fontSize,\n      background: footerBg\n    },\n    // =================== Content ====================\n    [`${componentCls}-content`]: {\n      flex: 'auto',\n      color: colorText,\n      // fix firefox can't set height smaller than content on flex item\n      minHeight: 0\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    colorBgLayout,\n    controlHeight,\n    controlHeightLG,\n    colorText,\n    controlHeightSM,\n    marginXXS,\n    colorTextLightSolid,\n    colorBgContainer\n  } = token;\n  const paddingInline = controlHeightLG * 1.25;\n  return {\n    // Deprecated\n    colorBgHeader: '#001529',\n    colorBgBody: colorBgLayout,\n    colorBgTrigger: '#002140',\n    bodyBg: colorBgLayout,\n    headerBg: '#001529',\n    headerHeight: controlHeight * 2,\n    headerPadding: `0 ${paddingInline}px`,\n    headerColor: colorText,\n    footerPadding: `${controlHeightSM}px ${paddingInline}px`,\n    footerBg: colorBgLayout,\n    siderBg: '#001529',\n    triggerHeight: controlHeightLG + marginXXS * 2,\n    triggerBg: '#002140',\n    triggerColor: colorTextLightSolid,\n    zeroTriggerWidth: controlHeightLG,\n    zeroTriggerHeight: controlHeightLG,\n    lightSiderBg: colorBgContainer,\n    lightTriggerBg: colorBgContainer,\n    lightTriggerColor: colorText\n  };\n};\n// ============================== Export ==============================\nexport const DEPRECATED_TOKENS = [['colorBgBody', 'bodyBg'], ['colorBgHeader', 'headerBg'], ['colorBgTrigger', 'triggerBg']];\nexport default genStyleHooks('Layout', token => [genLayoutStyle(token)], prepareComponentToken, {\n  deprecatedTokens: DEPRECATED_TOKENS\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AASA,IAAAA,UAAuB;AACvB,IAAAC,qBAAuB;AACvB;;;ACHA,IAAAC,UAAuB;AACvB;AAEA;AACA;;;ACdA,YAAuB;AACvB;AACO,SAAS,OAAO;AAAC;AACxB,IAAI,qBAAqB;AAKzB,IAAI,WAAW;AACf,IAAI,MAAuC;AACzC,aAAW,CAAC,OAAO,WAAW,YAAY;AACxC,oBAAU,OAAO,UAAU,SAAS,KAAK,OAAO,EAAE;AAElD,QAAI,OAAiC;AACnC,kBAAY;AAAA,IACd;AAAA,EACF;AACF;AACA,IAAM,UAAU;AACT,IAAM,iBAAoC,oBAAc,CAAC,CAAC;AAM1D,IAAM,gBAAgB,OAAwC,eAAa;AAChF,QAAM;AAAA,IACJ;AAAA,EACF,IAAU,iBAAW,cAAc;AACnC,QAAM,cAAc,CAAC,OAAO,MAAM,YAAY;AAC5C,QAAI,CAAC,OAAO;AACV,UAAI,WAAW,SAAS,SAAS,cAAc;AAC7C,cAAM,eAAe;AACrB,YAAI,CAAC,oBAAoB;AACvB,+BAAqB,CAAC;AAAA,QACxB;AACA,2BAAmB,SAAS,IAAI,mBAAmB,SAAS,KAAK,CAAC;AAClE,YAAI,CAAC,mBAAmB,SAAS,EAAE,SAAS,WAAW,EAAE,GAAG;AAC1D,6BAAmB,SAAS,EAAE,KAAK,WAAW,EAAE;AAAA,QAClD;AAEA,YAAI,CAAC,cAAc;AACjB,kBAAQ,KAAK,sDAAsD,kBAAkB;AAAA,QACvF;AAAA,MACF,OAAO;AACL,eAAwC,QAAQ,OAAO,WAAW,OAAO,IAAI;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AACA,cAAY,aAAa,CAAC,OAAO,SAAS,SAAS,YAAY;AAC7D,gBAAY,OAAO,cAAc,KAAK,OAAO,kCAAkC,OAAO,cAAc,UAAU,IAAI,OAAO,KAAK,EAAE,EAAE;AAAA,EACpI;AACA,SAAO;AACT,IAAI,MAAM;AACR,QAAM,cAAc,MAAM;AAAA,EAAC;AAC3B,cAAY,aAAa;AACzB,SAAO;AACT;AACA,IAAOC,mBAAQ;;;ACxDf,mBAA8B;AAI9B,IAAO,sCAAqB,4BAAc,MAAS;;;ACJnD,IAAAC,SAAuB;;;ACFvB,IAAI,SAAS;AAAA;AAAA,EAEX,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,MAAM;AAAA;AAAA,EAEN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AACb;AACA,IAAO,gBAAQ;;;ACff;;;ACAO,IAAI,eAAe;AAAA,EACxB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,iBAAiB;AACnB;;;ADHA,IAAIC,UAAS,eAAc,eAAc,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,EAC9D,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,aAAa;AAAA,EACb,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,aAAa;AACf,CAAC;AACD,IAAOC,iBAAQD;;;AE7Bf,IAAME,UAAS;AAAA,EACb,aAAa;AAAA,EACb,kBAAkB,CAAC,cAAc,UAAU;AAC7C;AACA,IAAOC,iBAAQD;;;ACDf,IAAME,UAAS;AAAA,EACb,MAAM,OAAO,OAAO;AAAA,IAClB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,kBAAkB,CAAC,cAAc,UAAU;AAAA,IAC3C,sBAAsB,CAAC,cAAc,UAAU;AAAA,IAC/C,yBAAyB,CAAC,iBAAiB,aAAa;AAAA,IACxD,uBAAuB,CAAC,eAAe,WAAW;AAAA,IAClD,sBAAsB,CAAC,cAAc,UAAU;AAAA,EACjD,GAAGC,cAAc;AAAA,EACjB,kBAAkB,OAAO,OAAO,CAAC,GAAGA,cAAgB;AACtD;AAGA,IAAOA,iBAAQD;;;ACnBf,IAAOE,iBAAQA;;;ACGf,IAAM,eAAe;AACrB,IAAM,eAAe;AAAA,EACnB,QAAQ;AAAA,EACR;AAAA,EACA,YAAAC;AAAA,EACA,YAAAA;AAAA,EACA,UAAAA;AAAA,EACA,QAAQ;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,IAAI,EAAE;AAAA,IACf,mBAAmB;AAAA,IACnB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,MAAM;AAAA,IACJ,UAAU;AAAA,IACV,yBAAyB;AAAA,MACvB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,eAAe;AAAA,EACjB;AACF;AACA,IAAOA,iBAAQ;;;ACjJf,IAAI,gBAAgB,OAAO,OAAO,CAAC,GAAGC,eAAc,KAAK;AACzD,IAAI,aAAa,CAAC;AAClB,IAAM,iBAAiB,MAAM,WAAW,OAAO,CAAC,QAAQC,YAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAGA,OAAM,GAAGD,eAAc,KAAK;AACjI,SAAS,oBAAoB,WAAW;AAC7C,MAAI,WAAW;AACb,UAAM,cAAc,OAAO,OAAO,CAAC,GAAG,SAAS;AAC/C,eAAW,KAAK,WAAW;AAC3B,oBAAgB,eAAe;AAC/B,WAAO,MAAM;AACX,mBAAa,WAAW,OAAO,CAAAC,YAAUA,YAAW,WAAW;AAC/D,sBAAgB,eAAe;AAAA,IACjC;AAAA,EACF;AACA,kBAAgB,OAAO,OAAO,CAAC,GAAGD,eAAc,KAAK;AACvD;AACO,SAAS,mBAAmB;AACjC,SAAO;AACT;;;AClBA,IAAAE,gBAA8B;AAC9B,IAAM,oBAA6B,6BAAc,MAAS;AAC1D,IAAO,kBAAQ;;;ACFf,IAAAC,SAAuB;AAGvB,IAAM,YAAY,CAAC,eAAe,kBAAkB;AAClD,QAAM,aAAmB,kBAAW,eAAa;AACjD,QAAM,YAAkB,eAAQ,MAAM;AACpC,QAAI;AACJ,UAAMC,UAAS,iBAAiBC,eAAkB,aAAa;AAC/D,UAAM,qBAAqB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,aAAa,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AACrJ,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAOD,YAAW,aAAaA,QAAO,IAAIA,OAAM,GAAG,qBAAqB,CAAC,CAAC;AAAA,EACnH,GAAG,CAAC,eAAe,eAAe,UAAU,CAAC;AAC7C,QAAM,gBAAsB,eAAQ,MAAM;AACxC,UAAM,aAAa,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW;AAEtF,SAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,UAAU,CAAC,YAAY;AAC7F,aAAOC,eAAkB;AAAA,IAC3B;AACA,WAAO;AAAA,EACT,GAAG,CAAC,UAAU,CAAC;AACf,SAAO,CAAC,WAAW,aAAa;AAClC;AACA,IAAO,oBAAQ;;;AVdR,IAAM,WAAW;AACxB,IAAM,iBAAiB,WAAS;AAC9B,QAAM;AAAA,IACJ,QAAAC,UAAS,CAAC;AAAA,IACV;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,MAAuC;AACzC,UAAMC,WAAU,cAAc,gBAAgB;AAC9C,WAAwCA,SAAQ,gBAAgB,UAAU,cAAc,+GAA+G,IAAI;AAAA,EAC7M;AACA,EAAM,iBAAU,MAAM;AACpB,UAAM,cAAc,oBAAoBD,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,KAAK;AACpG,WAAO;AAAA,EACT,GAAG,CAACA,OAAM,CAAC;AACX,QAAM,0BAAgC,eAAQ,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGA,OAAM,GAAG;AAAA,IAC3F,OAAO;AAAA,EACT,CAAC,GAAG,CAACA,OAAM,CAAC;AACZ,SAA0B,qBAAc,gBAAc,UAAU;AAAA,IAC9D,OAAO;AAAA,EACT,GAAG,QAAQ;AACb;AACA,IAAI,MAAuC;AACzC,iBAAe,cAAc;AAC/B;AACA,IAAO,iBAAQ;;;AWhCf,IAAAE,gBAAkB;;;ACAX,IAAM,sBAAsB;AAAA,EACjC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA;AAAA;AAAA;AAAA,EAIT,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AACR;AACA,IAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,GAAG;AAAA;AAAA,EAEtE,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,eAAe;AAAA,EACf,aAAa;AAAA;AAAA,EAEb,YAAY;AAAA;AAAA;AAAA,EAGZ,gBAAgB;AAAA,EAChB,UAAU;AAAA;AAAA,EAEV,WAAW;AAAA,EACX,UAAU;AAAA;AAAA,EAEV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,gBAAgB;AAAA;AAAA,EAEhB,eAAe;AAAA;AAAA,EAEf,YAAY;AAAA,EACZ,iBAAiB;AAAA;AAAA,EAEjB,cAAc;AAAA;AAAA,EAEd,WAAW;AAAA;AAAA,EAEX,QAAQ;AACV,CAAC;AACD,IAAO,eAAQ;;;AClEf;;;ACAAC;;;ACAAC;AACe,SAAR,iBAAkC,MAAM;AAAA,EAC7C,uBAAAC;AAAA,EACA,8BAAAC;AACF,GAAG;AACD,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgBD,uBAAsB,gBAAgB;AAC5D,QAAM,gBAAgBA,uBAAsB,gBAAgB;AAC5D,QAAM,gBAAgBA,uBAAsB,gBAAgB;AAC5D,QAAM,cAAcA,uBAAsB,cAAc;AACxD,QAAM,aAAaA,uBAAsB,aAAa;AACtD,QAAM,gBAAgBC,8BAA6B,aAAa,aAAa;AAE7E,QAAM,YAAY,KAAK,aAAa,KAAK;AACzC,QAAM,aAAaD,uBAAsB,SAAS;AAClD,QAAM,0BAA0B,IAAI,UAAU,YAAY,CAAC,CAAC,EAAE,IAAI,IAAI,UAAU,YAAY,CAAC,CAAC,GAAG,EAAE,EAAE,YAAY;AACjH,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG;AAAA,IACrD,gBAAgB,cAAc,CAAC;AAAA,IAC/B,qBAAqB,cAAc,CAAC;AAAA,IACpC,oBAAoB,cAAc,CAAC;AAAA,IACnC,yBAAyB,cAAc,CAAC;AAAA,IACxC,mBAAmB,cAAc,CAAC;AAAA,IAClC,cAAc,cAAc,CAAC;AAAA,IAC7B,oBAAoB,cAAc,CAAC;AAAA,IACnC,uBAAuB,cAAc,CAAC;AAAA,IACtC,kBAAkB,cAAc,CAAC;AAAA,IACjC,wBAAwB,cAAc,EAAE;AAAA,IACxC,gBAAgB,cAAc,CAAC;AAAA,IAC/B,qBAAqB,cAAc,CAAC;AAAA,IACpC,oBAAoB,cAAc,CAAC;AAAA,IACnC,yBAAyB,cAAc,CAAC;AAAA,IACxC,mBAAmB,cAAc,CAAC;AAAA,IAClC,cAAc,cAAc,CAAC;AAAA,IAC7B,oBAAoB,cAAc,CAAC;AAAA,IACnC,uBAAuB,cAAc,CAAC;AAAA,IACtC,kBAAkB,cAAc,CAAC;AAAA,IACjC,wBAAwB,cAAc,EAAE;AAAA,IACxC,cAAc,YAAY,CAAC;AAAA,IAC3B,mBAAmB,YAAY,CAAC;AAAA,IAChC;AAAA,IACA,oBAAoB,YAAY,CAAC;AAAA,IACjC,kBAAkB,YAAY,CAAC;AAAA,IAC/B,uBAAuB,YAAY,CAAC;AAAA,IACpC,iBAAiB,YAAY,CAAC;AAAA,IAC9B,YAAY,YAAY,CAAC;AAAA,IACzB,kBAAkB,YAAY,CAAC;AAAA,IAC/B,qBAAqB,YAAY,CAAC;AAAA,IAClC,gBAAgB,YAAY,CAAC;AAAA,IAC7B,sBAAsB,YAAY,EAAE;AAAA,IACpC,gBAAgB,cAAc,CAAC;AAAA,IAC/B,qBAAqB,cAAc,CAAC;AAAA,IACpC,oBAAoB,cAAc,CAAC;AAAA,IACnC,yBAAyB,cAAc,CAAC;AAAA,IACxC,mBAAmB,cAAc,CAAC;AAAA,IAClC,cAAc,cAAc,CAAC;AAAA,IAC7B,oBAAoB,cAAc,CAAC;AAAA,IACnC,uBAAuB,cAAc,CAAC;AAAA,IACtC,kBAAkB,cAAc,CAAC;AAAA,IACjC,wBAAwB,cAAc,EAAE;AAAA,IACxC,aAAa,WAAW,CAAC;AAAA,IACzB,kBAAkB,WAAW,CAAC;AAAA,IAC9B,iBAAiB,WAAW,CAAC;AAAA,IAC7B,sBAAsB,WAAW,CAAC;AAAA,IAClC,gBAAgB,WAAW,CAAC;AAAA,IAC5B,WAAW,WAAW,CAAC;AAAA,IACvB,iBAAiB,WAAW,CAAC;AAAA,IAC7B,oBAAoB,WAAW,CAAC;AAAA,IAChC,eAAe,WAAW,CAAC;AAAA,IAC3B,qBAAqB,WAAW,EAAE;AAAA,IAClC,gBAAgB,WAAW,CAAC;AAAA,IAC5B,WAAW,WAAW,CAAC;AAAA,IACvB,iBAAiB,WAAW,CAAC;AAAA,IAC7B,aAAa,IAAI,UAAU,MAAM,EAAE,KAAK,IAAI,EAAE,YAAY;AAAA,IAC1D,YAAY;AAAA,EACd,CAAC;AACH;;;ACnFA,IAAM,YAAY,gBAAc;AAC9B,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,cAAc;AAElB,MAAI,aAAa,KAAK,cAAc,GAAG;AACrC,eAAW,aAAa;AAAA,EAC1B,WAAW,aAAa,MAAM,cAAc,GAAG;AAC7C,eAAW,aAAa;AAAA,EAC1B,WAAW,cAAc,IAAI;AAC3B,eAAW;AAAA,EACb;AAEA,MAAI,aAAa,KAAK,cAAc,GAAG;AACrC,eAAW;AAAA,EACb,WAAW,aAAa,KAAK,cAAc,GAAG;AAC5C,eAAW;AAAA,EACb,WAAW,aAAa,MAAM,cAAc,GAAG;AAC7C,eAAW;AAAA,EACb,WAAW,aAAa,MAAM,cAAc,IAAI;AAC9C,eAAW;AAAA,EACb,WAAW,cAAc,IAAI;AAC3B,eAAW;AAAA,EACb;AAEA,MAAI,aAAa,KAAK,cAAc,GAAG;AACrC,eAAW;AAAA,EACb,WAAW,cAAc,GAAG;AAC1B,eAAW;AAAA,EACb;AAEA,MAAI,aAAa,KAAK,aAAa,GAAG;AACpC,kBAAc;AAAA,EAChB,WAAW,cAAc,GAAG;AAC1B,kBAAc;AAAA,EAChB;AACA,SAAO;AAAA,IACL,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,EACrB;AACF;AACA,IAAO,oBAAQ;;;AC5CA,SAAR,kBAAmC,OAAO;AAC/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,OAAO,OAAO;AAAA;AAAA,IAEnB,oBAAoB,IAAI,aAAa,YAAY,QAAQ,CAAC,CAAC;AAAA,IAC3D,mBAAmB,IAAI,aAAa,aAAa,GAAG,QAAQ,CAAC,CAAC;AAAA,IAC9D,oBAAoB,IAAI,aAAa,aAAa,GAAG,QAAQ,CAAC,CAAC;AAAA;AAAA,IAE/D,eAAe,YAAY;AAAA,EAC7B,GAAG,kBAAU,YAAY,CAAC;AAC5B;;;AChBA,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,iBAAiB,gBAAgB;AAAA,IACjC,iBAAiB,gBAAgB;AAAA,IACjC,iBAAiB,gBAAgB;AAAA,EACnC;AACF;AACA,IAAO,2BAAQ;;;ACVR,SAAS,cAAc,UAAU;AACtC,UAAQ,WAAW,KAAK;AAC1B;AAEe,SAAR,aAA8B,MAAM;AACzC,QAAM,YAAY,MAAM,KAAK;AAAA,IAC3B,QAAQ;AAAA,EACV,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACnB,UAAM,IAAI,QAAQ;AAClB,UAAM,WAAW,OAAO,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC;AAC9C,UAAM,UAAU,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAErE,WAAO,KAAK,MAAM,UAAU,CAAC,IAAI;AAAA,EACnC,CAAC;AACD,YAAU,CAAC,IAAI;AACf,SAAO,UAAU,IAAI,WAAS;AAAA,IAC5B;AAAA,IACA,YAAY,cAAc,IAAI;AAAA,EAChC,EAAE;AACJ;;;AClBA,IAAM,kBAAkB,cAAY;AAClC,QAAM,gBAAgB,aAAa,QAAQ;AAC3C,QAAM,YAAY,cAAc,IAAI,UAAQ,KAAK,IAAI;AACrD,QAAM,cAAc,cAAc,IAAI,UAAQ,KAAK,UAAU;AAC7D,QAAM,aAAa,UAAU,CAAC;AAC9B,QAAM,aAAa,UAAU,CAAC;AAC9B,QAAM,aAAa,UAAU,CAAC;AAC9B,QAAM,aAAa,YAAY,CAAC;AAChC,QAAM,eAAe,YAAY,CAAC;AAClC,QAAM,eAAe,YAAY,CAAC;AAClC,SAAO;AAAA,IACL;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,YAAY,UAAU,CAAC;AAAA,IACvB,kBAAkB,UAAU,CAAC;AAAA,IAC7B,kBAAkB,UAAU,CAAC;AAAA,IAC7B,kBAAkB,UAAU,CAAC;AAAA,IAC7B,kBAAkB,UAAU,CAAC;AAAA,IAC7B,kBAAkB,UAAU,CAAC;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,KAAK,MAAM,aAAa,UAAU;AAAA,IAC9C,cAAc,KAAK,MAAM,eAAe,UAAU;AAAA,IAClD,cAAc,KAAK,MAAM,eAAe,UAAU;AAAA,IAClD,oBAAoB,YAAY,CAAC;AAAA,IACjC,oBAAoB,YAAY,CAAC;AAAA,IACjC,oBAAoB,YAAY,CAAC;AAAA,IACjC,oBAAoB,YAAY,CAAC;AAAA,IACjC,oBAAoB,YAAY,CAAC;AAAA,EACnC;AACF;AACA,IAAO,0BAAQ;;;AClCA,SAAR,gBAAiC,OAAO;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,SAAS,YAAY,WAAW;AAAA;AAAA,IAEhC,QAAQ,YAAY,WAAW;AAAA;AAAA,IAE/B,QAAQ,YAAY,WAAW;AAAA;AAAA,IAE/B,QAAQ,YAAY,WAAW;AAAA;AAAA,IAE/B,QAAQ,WAAW;AAAA;AAAA,IAEnB,MAAM,WAAW;AAAA;AAAA,IAEjB,QAAQ,YAAY,WAAW;AAAA;AAAA,IAE/B,QAAQ,YAAY,WAAW;AAAA;AAAA,IAE/B,SAAS,YAAY,WAAW;AAAA;AAAA,EAClC;AACF;;;ACxBAE;;;ACAAC;AACO,IAAM,gBAAgB,CAAC,WAAW,UAAU,IAAI,UAAU,SAAS,EAAE,KAAK,KAAK,EAAE,YAAY;AAC7F,IAAM,gBAAgB,CAAC,WAAW,eAAe;AACtD,QAAM,WAAW,IAAI,UAAU,SAAS;AACxC,SAAO,SAAS,OAAO,UAAU,EAAE,YAAY;AACjD;;;ADHO,IAAM,wBAAwB,eAAa;AAChD,QAAM,SAAS,SAAS,SAAS;AACjC,SAAO;AAAA,IACL,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,IACX,IAAI,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,EAId;AACF;AACO,IAAM,+BAA+B,CAAC,aAAa,kBAAkB;AAC1E,QAAM,cAAc,eAAe;AACnC,QAAM,gBAAgB,iBAAiB;AACvC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,WAAW,cAAc,eAAe,IAAI;AAAA,IAC5C,oBAAoB,cAAc,eAAe,IAAI;AAAA,IACrD,mBAAmB,cAAc,eAAe,IAAI;AAAA,IACpD,qBAAqB,cAAc,eAAe,IAAI;AAAA,IACtD,WAAW,cAAc,eAAe,IAAI;AAAA,IAC5C,oBAAoB,cAAc,eAAe,IAAI;AAAA,IACrD,mBAAmB,cAAc,eAAe,IAAI;AAAA,IACpD,qBAAqB,cAAc,eAAe,IAAI;AAAA,IACtD,cAAc,cAAc,eAAe,CAAC;AAAA,IAC5C,mBAAmB,cAAc,eAAe,IAAI;AAAA,IACpD,oBAAoB,cAAc,eAAe,IAAI;AAAA,IACrD,eAAe,cAAc,aAAa,CAAC;AAAA,IAC3C,kBAAkB,cAAc,aAAa,CAAC;AAAA,IAC9C,iBAAiB,cAAc,aAAa,CAAC;AAAA,IAC7C,kBAAkB,cAAc,eAAe,IAAI;AAAA,IACnD,aAAa;AAAA,IACb,aAAa,cAAc,aAAa,EAAE;AAAA,IAC1C,sBAAsB,cAAc,aAAa,CAAC;AAAA,EACpD;AACF;;;ARrCe,SAAR,WAA4B,OAAO;AAExC,sBAAoB,OAAO,oBAAoB;AAC/C,iBAAe,OAAO,eAAe;AACrC,QAAM,gBAAgB,OAAO,KAAK,mBAAmB,EAAE,IAAI,cAAY;AACrE,UAAM,SAAS,MAAM,QAAQ,MAAM,oBAAoB,QAAQ,IAAI,eAAe,QAAQ,IAAI,SAAS,MAAM,QAAQ,CAAC;AACtH,WAAO,MAAM,KAAK;AAAA,MAChB,QAAQ;AAAA,IACV,GAAG,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,MAAM;AACjC,WAAK,GAAG,QAAQ,IAAI,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC;AACvC,WAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC;AACtC,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP,CAAC,EAAE,OAAO,CAAC,MAAM,QAAQ;AACvB,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,GAAG;AACjD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,aAAa,GAAG,iBAAiB,OAAO;AAAA,IAC3J;AAAA,IACA;AAAA,EACF,CAAC,CAAC,GAAG,wBAAgB,MAAM,QAAQ,CAAC,GAAG,gBAAgB,KAAK,CAAC,GAAG,yBAAiB,KAAK,CAAC,GAAG,kBAAkB,KAAK,CAAC;AACpH;;;AD3BA,IAAM,eAAe,YAAY,UAAiB;AAClD,IAAO,gBAAQ;;;AFER,IAAM,gBAAgB;AAAA,EAC3B,OAAO;AAAA,EACP,UAAU;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,QAAQ;AACV;AACO,IAAM,qBAAkC,cAAAC,QAAM,cAAc,aAAa;;;AaZhF,IAAAC,SAAuB;AAChB,IAAM,mBAAmB;AACzB,IAAM,uBAAuB;AAC7B,IAAM,WAAW,CAAC,YAAY,cAAc,UAAU,YAAY;AACzE,IAAM,sBAAsB,CAAC,WAAW,uBAAuB;AAC7D,MAAI,oBAAoB;AACtB,WAAO;AAAA,EACT;AACA,SAAO,YAAY,GAAG,gBAAgB,IAAI,SAAS,KAAK;AAC1D;AAEO,IAAM,gBAAmC,qBAAc;AAAA;AAAA,EAE5D,cAAc;AAAA,EACd,eAAe;AACjB,CAAC;AACM,IAAM;AAAA,EACX,UAAU;AACZ,IAAI;AACJ,IAAM,eAAe,CAAC;AASf,SAAS,mBAAmB,UAAU;AAC3C,QAAM,UAAgB,kBAAW,aAAa;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,QAAQ,QAAQ;AAClC,SAAO,OAAO,OAAO,OAAO,OAAO;AAAA,IACjC,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV,GAAG,SAAS,GAAG;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC5CAC;AACAA;AACA;AACA;AAEA,IAAM,mBAAmB,QAAQ,KAAK,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;AACrD,SAAS,SAASC,kBAAiB,OAAO;AAC/C,QAAM,YAAY,CAAC;AACnB,QAAM,cAAc,CAAC,OAAO,YAAY;AACtC,QAAI,QAAQ,MAAM,MAAM;AACxB,aAAS,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,MAAM;AAC9E,WAAO,MAAM,YAAY;AAAA,EAC3B;AACA,QAAM,YAAY,CAAC,UAAU,SAAS;AACpC,UAAM,YAAY,IAAI,UAAU,QAAQ;AACxC,UAAM,gBAAgB,SAAS,UAAU,YAAY,CAAC;AACtD,cAAU,GAAG,IAAI,QAAQ,IAAI,YAAY,SAAS;AAClD,cAAU,GAAG,IAAI,iBAAiB,IAAI,cAAc,CAAC;AACrD,cAAU,GAAG,IAAI,cAAc,IAAI,cAAc,CAAC;AAClD,cAAU,GAAG,IAAI,eAAe,IAAI,cAAc,CAAC;AACnD,cAAU,GAAG,IAAI,gBAAgB,IAAI,UAAU,MAAM,EAAE,KAAK,GAAG,EAAE,YAAY;AAC7E,cAAU,GAAG,IAAI,sBAAsB,IAAI,cAAc,CAAC;AAC1D,cAAU,GAAG,IAAI,0BAA0B,IAAI,cAAc,CAAC;AAAA,EAChE;AAEA,MAAI,MAAM,cAAc;AACtB,cAAU,MAAM,cAAc,SAAS;AACvC,UAAM,eAAe,IAAI,UAAU,MAAM,YAAY;AACrD,UAAM,gBAAgB,SAAS,aAAa,YAAY,CAAC;AAEzD,kBAAc,QAAQ,CAAC,OAAO,UAAU;AACtC,gBAAU,WAAW,QAAQ,CAAC,EAAE,IAAI;AAAA,IACtC,CAAC;AAED,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,QAAQ,EAAE,CAAC;AACzF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,QAAQ,EAAE,CAAC;AACzF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,KAAK,EAAE,CAAC;AACtF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,KAAK,EAAE,CAAC;AACtF,cAAU,+BAA+B,IAAI,YAAY,cAAc,OAAK,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC;AAC9F,UAAM,qBAAqB,IAAI,UAAU,cAAc,CAAC,CAAC;AACzD,cAAU,sCAAsC,IAAI,YAAY,oBAAoB,OAAK,EAAE,KAAK,EAAE,IAAI,GAAG,CAAC;AAC1G,cAAU,sCAAsC,IAAI,YAAY,oBAAoB,OAAK,EAAE,OAAO,CAAC,CAAC;AAAA,EACtG;AAEA,MAAI,MAAM,cAAc;AACtB,cAAU,MAAM,cAAc,SAAS;AAAA,EACzC;AAEA,MAAI,MAAM,cAAc;AACtB,cAAU,MAAM,cAAc,SAAS;AAAA,EACzC;AAEA,MAAI,MAAM,YAAY;AACpB,cAAU,MAAM,YAAY,OAAO;AAAA,EACrC;AAEA,MAAI,MAAM,WAAW;AACnB,cAAU,MAAM,WAAW,MAAM;AAAA,EACnC;AAEA,QAAM,UAAU,OAAO,KAAK,SAAS,EAAE,IAAI,SAAO,KAAKA,gBAAe,IAAI,GAAG,KAAK,UAAU,GAAG,CAAC,GAAG;AACnG,SAAO;AAAA;AAAA,MAEH,QAAQ,KAAK,IAAI,CAAC;AAAA;AAAA,IAEpB,KAAK;AACT;AACO,SAAS,cAAcA,kBAAiB,OAAO;AACpD,QAAM,QAAQ,SAASA,kBAAiB,KAAK;AAC7C,MAAI,UAAU,GAAG;AACf,cAAU,OAAO,GAAG,gBAAgB,gBAAgB;AAAA,EACtD,OAAO;AACL,WAAwCC,iBAAQ,OAAO,kBAAkB,sDAAsD,IAAI;AAAA,EACrI;AACF;;;ACxEA,IAAAC,SAAuB;AACvB,IAAM,kBAAqC,qBAAc,KAAK;AACvD,IAAM,0BAA0B,CAAC;AAAA,EACtC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,iBAAuB,kBAAW,eAAe;AACvD,SAA0B,qBAAc,gBAAgB,UAAU;AAAA,IAChE,OAAO,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,EAC/D,GAAG,QAAQ;AACb;AACA,IAAO,0BAAQ;;;ACbf,IAAAC,gBAA2B;;;ACE3B,IAAAC,SAAuB;AACvB,IAAM,cAAiC,qBAAc,MAAS;AACvD,IAAM,sBAAsB,CAAC;AAAA,EAClC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,aAAmB,kBAAW,WAAW;AAC/C,SAA0B,qBAAc,YAAY,UAAU;AAAA,IAC5D,OAAO,QAAQ;AAAA,EACjB,GAAG,QAAQ;AACb;AACA,IAAO,sBAAQ;;;ADVf,SAAS,YAAY;AACnB,QAAM,wBAAoB,0BAAW,uBAAe;AACpD,QAAM,oBAAgB,0BAAW,mBAAW;AAC5C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAO,oBAAQ;;;AEXf;AACA;;;ACDA;AACAC;;;ACDO,IAAM,eAAe,CAAC,QAAQ,UAAU,QAAQ,SAAS,WAAW,QAAQ,OAAO,UAAU,UAAU,WAAW,YAAY,QAAQ,MAAM;;;ACQnJ,IAAAC,gBAAkB;AAClB;;;ACTA,IAAO,kBAAQ;;;ACIf,IAAOC,mBAAQ;;;ACIfC;;;ACRAC;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,SAAS,KAAK,SAAS;AAChC;AACA,SAASC,eAAc,YAAY,iBAAiB;AAClD,QAAM;AAAA,IACJ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI,IAAI,UAAU,UAAU,EAAE,MAAM;AACpC,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI,IAAI,UAAU,eAAe,EAAE,MAAM;AACzC,WAAS,KAAK,MAAM,MAAM,GAAG,MAAM,MAAM;AACvC,UAAM,IAAI,KAAK,OAAO,KAAK,MAAM,IAAI,OAAO,EAAE;AAC9C,UAAM,IAAI,KAAK,OAAO,KAAK,MAAM,IAAI,OAAO,EAAE;AAC9C,UAAM,IAAI,KAAK,OAAO,KAAK,MAAM,IAAI,OAAO,EAAE;AAC9C,QAAI,cAAc,CAAC,KAAK,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AAC5D,aAAO,IAAI,UAAU;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG,KAAK,MAAM,KAAK,GAAG,IAAI;AAAA,MAC5B,CAAC,EAAE,YAAY;AAAA,IACjB;AAAA,EACF;AAGA,SAAO,IAAI,UAAU;AAAA,IACnB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC,EAAE,YAAY;AACjB;AACA,IAAO,wBAAQA;;;ADzCf,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AASe,SAAR,YAA6B,iBAAiB;AACnD,QAAM;AAAA,IACF;AAAA,EACF,IAAI,iBACJ,YAAY,OAAO,iBAAiB,CAAC,UAAU,CAAC;AAClD,QAAM,iBAAiB,OAAO,OAAO,CAAC,GAAG,QAAQ;AACjD,SAAO,KAAK,YAAS,EAAE,QAAQ,WAAS;AACtC,WAAO,eAAe,KAAK;AAAA,EAC7B,CAAC;AACD,QAAM,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,GAAG,cAAc;AAC9E,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,YAAY;AAElB,MAAI,YAAY,WAAW,OAAO;AAChC,UAAM,eAAe;AACrB,gBAAY,qBAAqB;AACjC,gBAAY,oBAAoB;AAChC,gBAAY,qBAAqB;AAAA,EACnC;AAEA,QAAM,aAAa,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG;AAAA;AAAA,IAE7E,kBAAkB,YAAY;AAAA,IAC9B,uBAAuB,YAAY;AAAA,IACnC,gBAAgB,YAAY;AAAA,IAC5B,0BAA0B,YAAY;AAAA;AAAA,IAEtC,eAAe,YAAY;AAAA,IAC3B,YAAY,sBAAc,YAAY,sBAAsB,YAAY,gBAAgB;AAAA;AAAA,IAExF,sBAAsB,YAAY;AAAA,IAClC,mBAAmB,YAAY;AAAA,IAC/B,kBAAkB,YAAY;AAAA,IAC9B,gBAAgB,YAAY;AAAA,IAC5B,sBAAsB,YAAY;AAAA,IAClC,qBAAqB,YAAY;AAAA,IACjC,gBAAgB,YAAY;AAAA,IAC5B,kBAAkB,YAAY;AAAA,IAC9B,mBAAmB,YAAY;AAAA,IAC/B,WAAW,YAAY;AAAA,IACvB,gBAAgB,YAAY;AAAA,IAC5B,mBAAmB,sBAAc,YAAY,cAAc,YAAY,gBAAgB;AAAA,IACvF,qBAAqB,sBAAc,YAAY,gBAAgB,YAAY,gBAAgB;AAAA;AAAA,IAE3F,cAAc,YAAY;AAAA;AAAA,IAE1B,gBAAgB,YAAY,YAAY;AAAA;AAAA,IAExC,WAAW,YAAY;AAAA,IACvB,qBAAqB,YAAY,YAAY;AAAA;AAAA,IAE7C,wBAAwB,YAAY,gBAAgB;AAAA,IACpD,oBAAoB,YAAY;AAAA,IAChC,qBAAqB,YAAY;AAAA,IACjC,0BAA0B,YAAY;AAAA,IACtC,6BAA6B,YAAY;AAAA,IACzC,mBAAmB,YAAY;AAAA,IAC/B,gBAAgB,sBAAc,YAAY,gBAAgB,YAAY,gBAAgB;AAAA,IACtF,UAAU,YAAY;AAAA,IACtB,cAAc,YAAY;AAAA,IAC1B,gBAAgB,YAAY;AAAA,IAC5B,gBAAgB,YAAY;AAAA,IAC5B,gBAAgB,YAAY;AAAA,IAC5B,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,YAAY,YAAY;AAAA,IACxB,WAAW,YAAY;AAAA,IACvB,WAAW,YAAY;AAAA,IACvB,SAAS,YAAY;AAAA,IACrB,WAAW,YAAY;AAAA,IACvB,WAAW,YAAY;AAAA,IACvB,WAAW,YAAY;AAAA,IACvB,4BAA4B,YAAY;AAAA,IACxC,0BAA0B,YAAY;AAAA,IACtC,0BAA0B,YAAY;AAAA,IACtC,wBAAwB,YAAY;AAAA,IACpC,4BAA4B,YAAY;AAAA,IACxC,0BAA0B,YAAY;AAAA,IACtC,WAAW,YAAY;AAAA,IACvB,UAAU,YAAY;AAAA,IACtB,UAAU,YAAY;AAAA,IACtB,QAAQ,YAAY;AAAA,IACpB,UAAU,YAAY;AAAA,IACtB,UAAU,YAAY;AAAA,IACtB,UAAU,YAAY;AAAA,IACtB,WAAW,YAAY;AAAA,IACvB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,IAKX,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKpB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKnB;AAAA,IACA,aAAa;AAAA,IACb,aAAa,WAAW;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb,aAAa,WAAW;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb,aAAa,WAAW;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb,aAAa,WAAW;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb,aAAa,YAAY;AAAA,IACzB;AAAA,IACA,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,eAAe;AAAA,uBACI,IAAI,UAAU,qBAAqB,EAAE,YAAY,CAAC;AAAA,oBACrD,IAAI,UAAU,qBAAqB,EAAE,YAAY,CAAC;AAAA,uBAC/C,IAAI,UAAU,qBAAqB,EAAE,YAAY,CAAC;AAAA;AAAA,IAErE,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKtB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKrB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKnB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKrB,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,6BAA6B;AAAA,EAC/B,CAAC,GAAG,cAAc;AAClB,SAAO;AACT;;;AH/KA,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAOO,IAAM,WAAW;AAAA,EACtB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,cAAc;AAChB;AACO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AACd;AACA,IAAM,WAAW;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,cAAc;AAChB;AACO,IAAM,mBAAmB,CAAC,aAAa,eAAe,UAAU;AACrE,QAAM,kBAAkB,MAAM,mBAAmB,WAAW;AAC5D,QAAM;AAAA,IACF;AAAA,EACF,IAAI,eACJ,aAAaA,QAAO,eAAe,CAAC,UAAU,CAAC;AAEjD,MAAI,wBAAwB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,eAAe,GAAG;AAAA,IAC5E;AAAA,EACF,CAAC;AAED,0BAAwB,YAAY,qBAAqB;AACzD,MAAI,YAAY;AACd,WAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACnD,YAAM;AAAA,QACF,OAAO;AAAA,MACT,IAAI,OACJ,kBAAkBA,QAAO,OAAO,CAAC,OAAO,CAAC;AAC3C,UAAI,uBAAuB;AAC3B,UAAI,gBAAgB;AAClB,+BAAuB,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,qBAAqB,GAAG,eAAe,GAAG;AAAA,UAChH,UAAU;AAAA,QACZ,GAAG,cAAc;AAAA,MACnB;AACA,4BAAsB,GAAG,IAAI;AAAA,IAC/B,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEe,SAAR,WAA4B;AACjC,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,cAAAC,QAAM,WAAW,kBAAkB;AACvC,QAAM,OAAO,GAAGC,gBAAO,IAAI,UAAU,EAAE;AACvC,QAAM,cAAc,SAAS;AAC7B,QAAM,CAAC,OAAO,QAAQ,SAAS,IAAI,cAAc,aAAa,CAAC,cAAkB,eAAe,GAAG;AAAA,IACjG;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,IAGA;AAAA,IACA,QAAQ,UAAU;AAAA,MAChB,QAAQ,OAAO;AAAA,MACf,KAAK,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,CAAC,aAAa,WAAW,SAAS,SAAS,IAAI,OAAO,MAAM;AACrE;;;AKvHA,IAAAC,gBAA2B;AAC3BC;;;ACCA;AACO,IAAM,eAAe;AAAA,EAC1B,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAChB;AACO,IAAM,iBAAiB,CAAC,OAAO,wBAAwB,WAAW;AAAA,EACvE,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,OAAO,MAAM;AAAA,EACb,UAAU,MAAM;AAAA;AAAA,EAEhB,YAAY,MAAM;AAAA,EAClB,WAAW;AAAA;AAAA,EAEX,YAAY,wBAAwB,YAAY,MAAM;AACxD;AACO,IAAM,YAAY,OAAO;AAAA,EAC9B,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,eAAe;AAAA;AAAA,EAEf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,OAAO;AAAA,IACL,YAAY;AAAA,EACd;AAAA,EACA,KAAK;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACO,IAAM,WAAW,OAAO;AAAA;AAAA,EAE7B,aAAa;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA;AAAA,IAEV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACO,IAAM,eAAe,YAAU;AAAA,EACpC,GAAG;AAAA,IACD,OAAO,MAAM;AAAA,IACb,gBAAgB,MAAM;AAAA,IACtB,iBAAiB;AAAA;AAAA,IAEjB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY,SAAS,MAAM,kBAAkB;AAAA,IAC7C,gCAAgC;AAAA;AAAA,IAEhC,WAAW;AAAA,MACT,OAAO,MAAM;AAAA,IACf;AAAA,IACA,YAAY;AAAA,MACV,OAAO,MAAM;AAAA,IACf;AAAA,IACA,qBAAqB;AAAA,MACnB,gBAAgB,MAAM;AAAA,MACtB,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,WAAW;AAAA,MACT,gBAAgB,MAAM;AAAA,MACtB,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,OAAO,MAAM;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AACO,IAAM,iBAAiB,CAAC,OAAO,oBAAoB,SAAS,cAAc;AAC/E,QAAM,iBAAiB,YAAY,kBAAkB,iBAAiB,kBAAkB;AACxF,QAAM,qBAAqB,UAAU,IAAI,OAAO,KAAK;AACrD,QAAM,aAAa;AAAA,IACjB,WAAW;AAAA,IACX,uBAAuB;AAAA,MACrB,WAAW;AAAA,IACb;AAAA,EACF;AACA,MAAI,iBAAiB,CAAC;AACtB,MAAI,cAAc,OAAO;AACvB,qBAAiB;AAAA,MACf,YAAY,MAAM;AAAA,MAClB,UAAU,MAAM;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AAAA,IACL,CAAC,kBAAkB,GAAG,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG,UAAU,GAAG;AAAA,MAChG,CAAC,cAAc,GAAG;AAAA,IACpB,CAAC;AAAA,EACH;AACF;AACO,IAAM,kBAAkB,CAAC,OAAO,YAAY;AAAA,EACjD,SAAS,GAAG,KAAK,MAAM,cAAc,CAAC,UAAU,MAAM,kBAAkB;AAAA,EACxE,eAAe,WAAW,QAAQ,WAAW,SAAS,SAAS;AAAA,EAC/D,YAAY;AACd;AACO,IAAM,gBAAgB,CAAC,OAAO,YAAY;AAAA,EAC/C,mBAAmB,OAAO,OAAO,CAAC,GAAG,gBAAgB,OAAO,MAAM,CAAC;AACrE;AACO,IAAM,eAAe,oBAAkB;AAAA,EAC5C,CAAC,IAAI,aAAa,EAAE,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;AAAA,IACnE,CAAC,IAAI,aAAa,KAAK,aAAa,OAAO,GAAG;AAAA,MAC5C,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH;AACO,IAAM,gBAAgB,WAAS,OAAO,OAAO,OAAO,OAAO;AAAA;AAAA;AAAA,EAGhE,OAAO,MAAM;AAAA,EACb,gBAAgB,MAAM;AAAA,EACtB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,YAAY,OAAO,MAAM,kBAAkB;AAAA,EAC3C,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AACd,GAAG,cAAc,KAAK,CAAC,GAAG;AAAA,EACxB,oBAAoB;AAAA,IAClB,OAAO,MAAM;AAAA,EACf;AAAA,EACA,YAAY;AAAA,IACV,OAAO,MAAM;AAAA,EACf;AACF,CAAC;;;ADxIM,IAAM;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACF,IAAI,sBAAc;AAAA,EAChB,WAAW,MAAM;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,QAAI,0BAAW,aAAa;AAC5B,UAAM,gBAAgB,aAAa;AACnC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,MAAM;AACd,UAAM,CAAC,OAAO,WAAW,QAAQ,OAAO,MAAM,IAAI,SAAc;AAChE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,MAAM;AACZ,UAAM;AAAA,MACJ;AAAA,IACF,QAAI,0BAAW,aAAa;AAC5B,WAAO,QAAQ,QAAQ,QAAQ,SAAS,MAAM,CAAC;AAAA,EACjD;AAAA,EACA,gBAAgB,CAAC,OAAO,WAAW;AACjC,QAAI;AACJ,UAAM,YAAY,aAAa,KAAK;AACpC,WAAO,CAAC,WAAW;AAAA,MACjB,KAAK;AAAA,IACP,GAAG,cAAc,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,OAAO,mBAAmB,QAAQ,OAAO,SAAS,KAAK,oBAAoB,CAAC;AAAA,EAC3J;AAAA,EACA,gBAAgB;AAAA,EAChB,iBAAiB,MAAM;AACzB,CAAC;;;AE7Cc,SAAR,eAAgC,OAAO,QAAQ;AACpD,SAAO,aAAa,OAAO,CAAC,MAAM,aAAa;AAC7C,UAAM,aAAa,MAAM,GAAG,QAAQ,GAAG;AACvC,UAAM,mBAAmB,MAAM,GAAG,QAAQ,GAAG;AAC7C,UAAM,YAAY,MAAM,GAAG,QAAQ,GAAG;AACtC,UAAM,YAAY,MAAM,GAAG,QAAQ,GAAG;AACtC,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,UAAU;AAAA,MAC7D;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,CAAC;AACP;;;ACdA;AAGA,IAAM,oBAAoB,CAAC,eAAe,QAAQ;AAChD,QAAM,CAAC,OAAO,KAAK,IAAI,SAAS;AAEhC,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,MAAM,CAAC,oBAAoB,aAAa;AAAA,IACxC,OAAO,MAAM,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI;AAAA,IAC3D,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF,GAAG,MAAM,CAAC,aAAa,aAAa,CAAC,CAAC;AACxC;AACA,IAAO,4BAAQ;;;ACjBf,IAAAC,SAAuB;AACvB,IAAM,YAAY,OAAO,OAAO,CAAC,GAAGA,MAAK;AACzC,IAAM;AAAA,EACJ;AACF,IAAI;AACJ,IAAM,aAAa,MAAM;AACzB,IAAM,cAAc,OAAO,UAAU,cAAc,aAAa;AAChE,IAAO,sBAAQ;;;AZFA,SAAR,SAA0B,OAAO,aAAa,QAAQ;AAC3D,MAAI,IAAI;AACR,QAAMC,WAAU,cAAc,gBAAgB;AAC9C,QAAM,cAAc,SAAS,CAAC;AAC9B,QAAM,oBAAoB,YAAY,YAAY,SAAS,CAAC,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG;AAAA,IACxH,SAAS,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,YAAY,QAAQ,OAAO,SAAS,KAAK,cAAc;AAAA,IAC3I,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,EAChF,CAAC,IAAI;AACL,QAAM,WAAW,oBAAY;AAC7B,MAAI,MAAuC;AACzC,UAAM,gBAAgB,YAAY,UAAU,kBAAkB;AAC9D,UAAM,WAAW,CAAC,EAAE,OAAO,YAAY,WAAW,cAAc,KAAK,YAAY,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AACzI,WAAwCA,SAAQ,CAAC,iBAAiB,UAAU,YAAY,wJAAwJ,IAAI;AAAA,EACtP;AACA,SAAO,QAAQ,MAAM;AACnB,QAAIC,KAAIC;AACR,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AAEA,UAAM,mBAAmB,OAAO,OAAO,CAAC,GAAG,kBAAkB,UAAU;AACvE,WAAO,KAAK,MAAM,cAAc,CAAC,CAAC,EAAE,QAAQ,mBAAiB;AAC3D,uBAAiB,aAAa,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,aAAa,CAAC,GAAG,MAAM,WAAW,aAAa,CAAC;AAAA,IACrI,CAAC;AACD,UAAM,YAAY,WAAW,SAAS,QAAQ,MAAM,EAAE,CAAC;AACvD,UAAM,iBAAiBD,MAAK,YAAY,YAAY,QAAQA,QAAO,SAASA,MAAK,kBAAkB,WAAW,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,MACtJ,QAAQ,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,IACjE,GAAG,OAAO,kBAAkB,WAAW,WAAW,kBAAkB,SAAS,CAAC,CAAC,GAAG,OAAO,YAAY,WAAW,WAAW,YAAY,SAAS,CAAC,CAAC,GAAG;AAAA,MACnJ,KAAK,OAAO,YAAY,WAAW,cAAcC,MAAK,YAAY,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,QAAQ;AAAA,IAC5H,CAAC;AAED,WAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,GAAG,WAAW,GAAG;AAAA,MACrF,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,KAAK,GAAG,YAAY,KAAK;AAAA,MAClF,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,GAAG,CAAC,aAAa,iBAAiB,GAAG,CAAC,MAAM,SAAS,KAAK,KAAK,CAAC,WAAW,UAAU;AACnF,UAAM,YAAY,KAAK,KAAK;AAC5B,WAAO,CAAC,gBAAQ,WAAW,WAAW,IAAI;AAAA,EAC5C,CAAC,CAAC;AACJ;;;Aa3CA,IAAAC,UAAuB;AACvBC;AAEA,IAAM,qBAAwC,sBAAc,IAAI;AAChE,IAAI,MAAuC;AACzC,qBAAmB,cAAc;AACnC;AACe,SAAR,cAA+B,OAAO;AAC3C,QAAM,eAAqB,mBAAW,kBAAkB;AACxD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,EAAE,KAAK,IAAI,SAAS;AAC3B,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,4BAAkC,eAAO,KAAK;AACpD,4BAA0B,YAAY,0BAA0B,UAAU,iBAAiB;AAC3F,MAAI,0BAA0B,SAAS;AACrC,WAA0B,sBAAc,mBAAmB,UAAU;AAAA,MACnE,OAAO;AAAA,IACT,GAAsB,sBAAc,gBAAgB;AAAA,MAClD;AAAA,IACF,GAAG,QAAQ,CAAC;AAAA,EACd;AACA,SAAO;AACT;;;AC1BA,IAAAC,UAAuB;AAMvB,IAAM,cAAiC,aAAK,CAAC;AAAA,EAC3C;AACF,MAAM;AACJ,QAAMC,WAAU,cAAc,gBAAgB;AAC9C,EAAAA,SAAQ,WAAW,6BAA6B,QAAW,4BAA4B,uBAAuB;AAC9G,SAAO;AACT,CAAC;AACD,IAAI,MAAuC;AACzC,cAAY,cAAc;AAC5B;AACA,IAAO,sBAAQ,OAAwC,cAAc,MAAM;;;A9ChB3E,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AA2BA,IAAI,mBAAmB;AAChB,IAAM,cAAc,OAAwC,mBAAiB;AAClF,SAAwCC,iBAAQ,CAAC,kBAAkB,eAAe,iGAAiG,IAAI;AACzL;AAAA;AAAA,EACA;AAAA;AAIA,IAAM,eAAe,CAAC,sBAAsB,qBAAqB,eAAe,SAAS,cAAc,QAAQ,UAAU,QAAQ;AACjI,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,qBAAqB;AAC5B,SAAO,mBAAmB;AAC5B;AACA,SAAS,yBAAyB;AAChC,SAAO,uBAAuB;AAChC;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,OAAO,KAAK,KAAK,EAAE,KAAK,SAAO,IAAI,SAAS,OAAO,CAAC;AAC7D;AACA,IAAM,kBAAkB,WAAS;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,cAAc,QAAW;AAC3B,sBAAkB;AAAA,EACpB;AACA,MAAI,kBAAkB,QAAW;AAC/B,0BAAsB;AAAA,EACxB;AACA,MAAI,kBAAkB,OAAO;AAC3B,yBAAqB;AAAA,EACvB;AACA,MAAI,OAAO;AACT,QAAI,cAAc,KAAK,GAAG;AACxB,aAAwCC,iBAAQ,OAAO,kBAAkB,0FAA0F,IAAI;AACvK,oBAAc,mBAAmB,GAAG,KAAK;AAAA,IAC3C,OAAO;AACL,oBAAc;AAAA,IAChB;AAAA,EACF;AACF;AACO,IAAM,eAAe,OAAO;AAAA,EACjC,cAAc,CAAC,WAAW,uBAAuB;AAC/C,QAAI,oBAAoB;AACtB,aAAO;AAAA,IACT;AACA,WAAO,YAAY,GAAG,mBAAmB,CAAC,IAAI,SAAS,KAAK,mBAAmB;AAAA,EACjF;AAAA,EACA,kBAAkB;AAAA,EAClB,kBAAkB,MAAM;AAEtB,QAAI,iBAAiB;AACnB,aAAO;AAAA,IACT;AAEA,WAAO,mBAAmB;AAAA,EAC5B;AAAA,EACA,UAAU,MAAM;AAAA,EAChB,cAAc;AAChB;AACA,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,eAAqB,oBAAY,CAAC,WAAW,uBAAuB;AACxE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,oBAAoB;AACtB,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,aAAa,cAAc,aAAa,EAAE;AAClE,WAAO,YAAY,GAAG,eAAe,IAAI,SAAS,KAAK;AAAA,EACzD,GAAG,CAAC,cAAc,cAAc,MAAM,SAAS,CAAC;AAChD,QAAM,gBAAgB,uBAAuB,cAAc,iBAAiB;AAC5E,QAAM,MAAM,aAAa,cAAc;AACvC,4BAAS,eAAe,GAAG;AAC3B,QAAM,cAAc,SAAS,OAAO,cAAc,OAAO;AAAA,IACvD,WAAW,aAAa,EAAE;AAAA,EAC5B,CAAC;AACD,MAAI,MAAuC;AACzC,uBAAmB,oBAAoB,CAAC,CAAC;AAAA,EAC3C;AACA,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQD,WAAU;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,uBAAuB,0BAA0B,QAAQ,0BAA0B,SAAS,wBAAwB;AAAA,IACpH;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA,WAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,MAAuC;AACzC,UAAM,YAAY,cAAc,gBAAgB;AAChD,cAAU,EAAE,6BAA6B,QAAQ,cAAc,wGAAwG;AAAA,EACzK;AACA,QAAM,SAAS,OAAO,OAAO,CAAC,GAAG,aAAa;AAC9C,SAAO,KAAK,UAAU,EAAE,QAAQ,SAAO;AACrC,QAAI,WAAW,GAAG,MAAM,QAAW;AACjC,aAAO,GAAG,IAAI,WAAW,GAAG;AAAA,IAC9B;AAAA,EACF,CAAC;AAGD,eAAa,QAAQ,cAAY;AAC/B,UAAM,YAAY,MAAM,QAAQ;AAChC,QAAI,WAAW;AACb,aAAO,QAAQ,IAAI;AAAA,IACrB;AAAA,EACF,CAAC;AACD,MAAI,OAAO,4BAA4B,aAAa;AAElD,WAAO,SAAS,OAAO,OAAO;AAAA,MAC5B,iBAAiB;AAAA,IACnB,GAAG,OAAO,MAAM;AAAA,EAClB;AAEA,QAAM,eAAe,QAAQ,MAAM,QAAQ,QAAQ,CAAC,YAAY,kBAAkB;AAChF,UAAM,WAAW,OAAO,KAAK,UAAU;AACvC,UAAM,cAAc,OAAO,KAAK,aAAa;AAC7C,WAAO,SAAS,WAAW,YAAY,UAAU,SAAS,KAAK,SAAO,WAAW,GAAG,MAAM,cAAc,GAAG,CAAC;AAAA,EAC9G,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,EACF,IAAU,mBAAW,oBAAmB;AACxC,QAAM,uBAA6B,gBAAQ,OAAO;AAAA,IAChD,WAAW;AAAA,IACX;AAAA,IACA,OAAO,QAAQ,SAAS;AAAA,EAC1B,IAAI,CAAC,eAAe,KAAK,KAAK,CAAC;AAC/B,MAAI,YAA+B,sBAAoB,kBAAU,MAAyB,sBAAc,qBAAa;AAAA,IACnH;AAAA,EACF,CAAC,GAAG,QAAQ;AACZ,QAAM,mBAAyB,gBAAQ,MAAM;AAC3C,QAAI,IAAI,IAAI,IAAI;AAChB,WAAO,QAAQ,KAAKC,eAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,4BAA4B,CAAC,KAAK,MAAM,KAAK,aAAa,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,4BAA4B,CAAC,KAAK,KAAK,aAAa,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,CAAC,IAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,qBAAqB,CAAC,CAAC;AAAA,EAC9a,GAAG,CAAC,cAAc,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,gBAAgB,CAAC;AACpF,MAAI,OAAO,KAAK,gBAAgB,EAAE,SAAS,GAAG;AAC5C,gBAA+B,sBAAc,gCAAwB,UAAU;AAAA,MAC7E,OAAO;AAAA,IACT,GAAG,SAAS;AAAA,EACd;AACA,MAAIF,SAAQ;AACV,gBAA+B,sBAAc,gBAAgB;AAAA,MAC3D,QAAQA;AAAA,MACR,aAAa;AAAA,IACf,GAAG,SAAS;AAAA,EACd;AACA,MAAI,iBAAiB,KAAK;AACxB,gBAA+B,sBAAc,gBAAY,UAAU;AAAA,MACjE,OAAO;AAAA,IACT,GAAG,SAAS;AAAA,EACd;AACA,MAAI,eAAe;AACjB,gBAA+B,sBAAc,qBAAqB;AAAA,MAChE,MAAM;AAAA,IACR,GAAG,SAAS;AAAA,EACd;AAEA,cAA+B,sBAAc,eAAe,MAAM,SAAS;AAE3E,QAAM,YAAkB,gBAAQ,MAAM;AACpC,UAAM,KAAK,eAAe,CAAC,GACzB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,IACJ,OAAOG,QAAO,IAAI,CAAC,aAAa,SAAS,cAAc,QAAQ,CAAC;AAClE,UAAM,WAAW,cAAc,CAAC,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,KAAK,YAAY,SAAS,IAAI;AAC7G,UAAM,mBAAmB,CAAC;AAC1B,WAAO,QAAQ,cAAc,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,eAAe,cAAc,MAAM;AAC5E,YAAM,cAAc,OAAO,OAAO,CAAC,GAAG,cAAc;AACpD,UAAI,eAAe,aAAa;AAC9B,YAAI,YAAY,cAAc,MAAM;AAClC,sBAAY,QAAQ;AAAA,QACtB,WAAW,MAAM,QAAQ,YAAY,SAAS,KAAK,OAAO,YAAY,cAAc,YAAY;AAC9F,sBAAY,QAAQ,YAAY,YAAY,SAAS;AAAA,QACvD;AACA,eAAO,YAAY;AAAA,MACrB;AACA,uBAAiB,aAAa,IAAI;AAAA,IACpC,CAAC;AACD,UAAM,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAgB,GAAG,KAAK;AAC5E,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,MAC5C,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU,OAAO,OAAO;AAAA,QACtB,UAAU;AAAA,MACZ,GAAG,gBAAgB;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,WAAW,CAAC;AAChB,MAAI,OAAO;AACT,gBAA+B,sBAAc,mBAAmB,UAAU;AAAA,MACxE,OAAO;AAAA,IACT,GAAG,SAAS;AAAA,EACd;AAEA,MAAI,aAAa,SAAS;AACxB,gBAA+B,sBAAc,eAAe,UAAU;AAAA,MACpE,OAAO,aAAa;AAAA,IACtB,GAAG,SAAS;AAAA,EACd;AAEA,MAAI,sBAAsB,QAAW;AACnC,gBAA+B,sBAAc,yBAAyB;AAAA,MACpE,UAAU;AAAA,IACZ,GAAG,SAAS;AAAA,EACd;AACA,SAA0B,sBAAc,cAAc,UAAU;AAAA,IAC9D,OAAO;AAAA,EACT,GAAG,SAAS;AACd;AACA,IAAM,iBAAiB,WAAS;AAC9B,QAAM,UAAgB,mBAAW,aAAa;AAC9C,QAAM,YAAkB,mBAAW,eAAa;AAChD,SAA0B,sBAAc,kBAAkB,OAAO,OAAO;AAAA,IACtE,eAAe;AAAA,IACf,cAAc;AAAA,EAChB,GAAG,KAAK,CAAC;AACX;AACA,eAAe,gBAAgB;AAC/B,eAAe,cAAc;AAC7B,eAAe,SAAS;AACxB,eAAe,YAAY;AAC3B,OAAO,eAAe,gBAAgB,eAAe;AAAA,EACnD,KAAK,MAAM;AACT,WAAwCJ,iBAAQ,OAAO,kBAAkB,0GAA0G,IAAI;AACvL,WAAO;AAAA,EACT;AACF,CAAC;AACD,IAAI,MAAuC;AACzC,iBAAe,cAAc;AAC/B;AACA,IAAO,0BAAQ;;;A+Cxaf,IAAAK,UAAuB;AAChB,IAAM,gBAAmC,sBAAc;AAAA,EAC5D,WAAW;AAAA,IACT,UAAU,MAAM;AAAA,IAChB,aAAa,MAAM;AAAA,EACrB;AACF,CAAC;;;ACND;;;ACUA,IAAAC,UAAuB;AACvB,IAAAC,gBAAwD;AAIxD,wBAAuB;AACvB;;;AChBO,IAAM,wBAAwB,CAAC,KAAK,YAAY;AAErD,MAAI,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,sBAAsB,aAAa;AAC3F,QAAI,iBAAiB,UAAU,OAAO;AAAA,EACxC,WAAW,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,iBAAiB,aAAa;AAC7F,QAAI,YAAY,OAAO;AAAA,EACzB;AACF;AACO,IAAM,2BAA2B,CAAC,KAAK,YAAY;AAExD,MAAI,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,yBAAyB,aAAa;AAC9F,QAAI,oBAAoB,UAAU,OAAO;AAAA,EAC3C,WAAW,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,oBAAoB,aAAa;AAChG,QAAI,eAAe,OAAO;AAAA,EAC5B;AACF;;;ACfA;;;ACAA;AAEA,IAAM,iBAAiB,WAAS;AAC9B,QAAM;AAAA,IACJ;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,MACd,SAAS;AAAA,MACT,MAAM;AAAA,MACN,eAAe;AAAA;AAAA,MAEf,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,CAAC,IAAI,YAAY,YAAY,GAAG;AAAA,QAC9B,eAAe;AAAA,QACf,CAAC,KAAK,YAAY,OAAO,YAAY,UAAU,GAAG;AAAA;AAAA,UAEhD,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,aAAa,YAAY,SAAS,GAAG;AAAA,QACnD,MAAM;AAAA,MACR;AAAA;AAAA,MAEA,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,MAC1B,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY,KAAK,YAAY;AAAA,MAC7B,YAAY;AAAA;AAAA;AAAA,MAGZ,CAAC,GAAG,MAAM,OAAO,GAAG;AAAA,QAClB,YAAY;AAAA,MACd;AAAA,IACF;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,MAC1B,SAAS;AAAA,MACT,OAAO;AAAA,MACP;AAAA,MACA,YAAY;AAAA,IACd;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,MAC3B,MAAM;AAAA,MACN,OAAO;AAAA;AAAA,MAEP,WAAW;AAAA,IACb;AAAA,EACF;AACF;AACO,IAAM,wBAAwB,WAAS;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,kBAAkB;AACxC,SAAO;AAAA;AAAA,IAEL,eAAe;AAAA,IACf,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,cAAc,gBAAgB;AAAA,IAC9B,eAAe,KAAK,aAAa;AAAA,IACjC,aAAa;AAAA,IACb,eAAe,GAAG,eAAe,MAAM,aAAa;AAAA,IACpD,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe,kBAAkB,YAAY;AAAA,IAC7C,WAAW;AAAA,IACX,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,EACrB;AACF;AAEO,IAAM,oBAAoB,CAAC,CAAC,eAAe,QAAQ,GAAG,CAAC,iBAAiB,UAAU,GAAG,CAAC,kBAAkB,WAAW,CAAC;AAC3H,IAAO,gBAAQ,cAAc,UAAU,WAAS,CAAC,eAAe,KAAK,CAAC,GAAG,uBAAuB;AAAA,EAC9F,kBAAkB;AACpB,CAAC;;;AD7GD,IAAM,gBAAgB,WAAS;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,MACd,UAAU;AAAA;AAAA,MAEV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY,OAAO,iBAAiB;AAAA,MACpC,iBAAiB;AAAA,QACf,eAAe;AAAA,MACjB;AAAA,MACA,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,CAAC,GAAG,YAAY,WAAW,GAAG;AAAA,QAC5B,QAAQ;AAAA;AAAA;AAAA;AAAA,QAIR,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,CAAC,GAAG,MAAM,QAAQ,MAAM,wBAAwB,GAAG;AAAA,UACjD,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,gBAAgB,YAAY,WAAW,GAAG;AAAA,QACzC,UAAU;AAAA,MACZ;AAAA,MACA,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,YAAY,KAAK,aAAa;AAAA,QAC9B,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY,OAAO,iBAAiB;AAAA,MACtC;AAAA,MACA,CAAC,GAAG,YAAY,qBAAqB,GAAG;AAAA,QACtC,UAAU;AAAA,QACV,KAAK;AAAA,QACL,gBAAgB,MAAM,KAAK,gBAAgB,EAAE,IAAI,EAAE,EAAE,MAAM;AAAA,QAC3D,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU,MAAM;AAAA,QAChB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,cAAc,KAAK,KAAK,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC;AAAA,QAC/D,QAAQ;AAAA,QACR,YAAY,cAAc,kBAAkB;AAAA,QAC5C,YAAY;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,YAAY,OAAO,kBAAkB;AAAA,UACrC,SAAS;AAAA,QACX;AAAA,QACA,kBAAkB;AAAA,UAChB,YAAY;AAAA,QACd;AAAA,QACA,WAAW;AAAA,UACT,kBAAkB,MAAM,KAAK,gBAAgB,EAAE,IAAI,EAAE,EAAE,MAAM;AAAA,UAC7D,cAAc,GAAG,KAAK,cAAc,CAAC,QAAQ,KAAK,cAAc,CAAC;AAAA,QACnE;AAAA,MACF;AAAA;AAAA,MAEA,WAAW;AAAA,QACT,YAAY;AAAA,QACZ,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,UAC3B,OAAO;AAAA,UACP,YAAY;AAAA,QACd;AAAA,QACA,CAAC,GAAG,YAAY,qBAAqB,GAAG;AAAA,UACtC,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ,aAAa,MAAM;AAAA;AAAA,UAE3B,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,gBAAQ,cAAc,CAAC,UAAU,OAAO,GAAG,WAAS,CAAC,cAAc,KAAK,CAAC,GAAG,uBAAuB;AAAA,EACxG,kBAAkB;AACpB,CAAC;;;AF9GD,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAYA,IAAM,kBAAkB;AAAA,EACtB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACP;AACA,IAAM,YAAY,WAAS,CAAC,OAAO,MAAM,OAAO,WAAW,KAAK,CAAC,KAAK,SAAS,KAAK;AAC7E,IAAM,eAAkC,sBAAc,CAAC,CAAC;AAC/D,IAAM,aAAc,uBAAM;AACxB,MAAI,IAAI;AACR,SAAO,CAAC,SAAS,OAAO;AACtB,SAAK;AACL,WAAO,GAAG,MAAM,GAAG,CAAC;AAAA,EACtB;AACF,GAAG;AACH,IAAM,QAA2B,mBAAW,CAAC,OAAO,QAAQ;AAC1D,QAAM;AAAA,IACF,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,IACnB,QAAQ;AAAA,IACR,QAAQ,CAAC;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,aAAaA,QAAO,OAAO,CAAC,aAAa,aAAa,WAAW,YAAY,oBAAoB,SAAS,SAAS,eAAe,gBAAgB,SAAS,kBAAkB,yBAAyB,cAAc,cAAc,cAAc,CAAC;AACnP,QAAM;AAAA,IACJ;AAAA,EACF,QAAI,0BAAW,aAAa;AAC5B,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,eAAe,QAAQ,MAAM,YAAY,gBAAgB;AACpG,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,KAAK;AACxC,+BAAU,MAAM;AACd,QAAI,eAAe,OAAO;AACxB,mBAAa,MAAM,SAAS;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,MAAM,SAAS,CAAC;AACpB,QAAM,qBAAqB,CAAC,OAAO,SAAS;AAC1C,QAAI,EAAE,eAAe,QAAQ;AAC3B,mBAAa,KAAK;AAAA,IACpB;AACA,mBAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,OAAO,IAAI;AAAA,EAChF;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,QAAI,0BAAW,aAAa;AAC5B,QAAM,YAAY,aAAa,gBAAgB,kBAAkB;AACjE,QAAM,CAAC,YAAY,QAAQ,SAAS,IAAI,cAAS,SAAS;AAE1D,QAAM,2BAAuB,sBAAO,IAAI;AACxC,uBAAqB,UAAU,SAAO;AACpC,aAAS,IAAI,OAAO;AACpB,qBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,IAAI,OAAO;AACpF,QAAI,cAAc,IAAI,SAAS;AAC7B,yBAAmB,IAAI,SAAS,YAAY;AAAA,IAC9C;AAAA,EACF;AACA,+BAAU,MAAM;AACd,aAAS,kBAAkBC,MAAK;AAC9B,UAAI;AACJ,cAAQ,KAAK,qBAAqB,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,sBAAsBA,IAAG;AAAA,IACnH;AACA,QAAI;AACJ,QAAI,QAAQ,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,gBAAgB,eAAe,cAAc,cAAc,iBAAiB;AAC7I,YAAM,OAAO,WAAW,0BAA0B,gBAAgB,UAAU,CAAC,GAAG;AAChF,4BAAsB,KAAK,iBAAiB;AAC5C,wBAAkB,GAAG;AAAA,IACvB;AACA,WAAO,MAAM;AACX,+BAAyB,KAAK,iBAAiB;AAAA,IACjD;AAAA,EACF,GAAG,CAAC,UAAU,CAAC;AACf,+BAAU,MAAM;AACd,UAAM,WAAW,WAAW,YAAY;AACxC,cAAU,SAAS,QAAQ;AAC3B,WAAO,MAAM,UAAU,YAAY,QAAQ;AAAA,EAC7C,GAAG,CAAC,CAAC;AACL,QAAM,SAAS,MAAM;AACnB,uBAAmB,CAAC,WAAW,cAAc;AAAA,EAC/C;AACA,QAAM,WAAW,KAAK,YAAY,CAAC,WAAW,CAAC;AAC/C,QAAM,WAAW,YAAY,iBAAiB;AAE9C,QAAM,aAAa,UAAU,QAAQ,IAAI,GAAG,QAAQ,OAAO,OAAO,QAAQ;AAE1E,QAAM,mBAAmB,WAAW,OAAO,kBAAkB,CAAC,CAAC,MAAM,IAAwB,sBAAc,QAAQ;AAAA,IACjH,SAAS;AAAA,IACT,eAAW,kBAAAC,SAAW,GAAG,SAAS,uBAAuB,GAAG,SAAS,uBAAuB,eAAe,UAAU,MAAM,EAAE;AAAA,IAC7H,OAAO;AAAA,EACT,GAAG,WAA8B,sBAAc,sBAAc,IAAI,CAAC,IAAK;AACvE,QAAM,cAAc,cAAc,UAAU,CAAC;AAC7C,QAAM,UAAU;AAAA,IACd,UAAU,cAAiC,sBAAc,uBAAe,IAAI,IAAuB,sBAAc,sBAAc,IAAI;AAAA,IACnI,WAAW,cAAiC,sBAAc,sBAAc,IAAI,IAAuB,sBAAc,uBAAe,IAAI;AAAA,EACtI;AACA,QAAM,SAAS,YAAY,cAAc;AACzC,QAAM,iBAAiB,QAAQ,MAAM;AACrC,QAAM,aAAa,YAAY,OAAO,oBAAwC,sBAAc,OAAO;AAAA,IACjG,WAAW,GAAG,SAAS;AAAA,IACvB,SAAS;AAAA,IACT,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF,GAAG,WAAW,cAAc,IAAK;AACjC,QAAM,WAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG;AAAA,IACvD,MAAM,OAAO,UAAU;AAAA,IACvB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,EACT,CAAC;AACD,QAAM,eAAW,kBAAAA,SAAW,WAAW,GAAG,SAAS,IAAI,KAAK,IAAI;AAAA,IAC9D,CAAC,GAAG,SAAS,YAAY,GAAG,CAAC,CAAC;AAAA,IAC9B,CAAC,GAAG,SAAS,cAAc,GAAG,eAAe,YAAY,QAAQ,CAAC;AAAA,IAClE,CAAC,GAAG,SAAS,QAAQ,GAAG,CAAC,CAAC;AAAA,IAC1B,CAAC,GAAG,SAAS,aAAa,GAAG,WAAW,UAAU,MAAM;AAAA,EAC1D,GAAG,WAAW,QAAQ,SAAS;AAC/B,QAAM,eAAqB,gBAAQ,OAAO;AAAA,IACxC,gBAAgB;AAAA,EAClB,IAAI,CAAC,SAAS,CAAC;AACf,SAAO,WAA8B,sBAAc,aAAa,UAAU;AAAA,IACxE,OAAO;AAAA,EACT,GAAsB,sBAAc,SAAS,OAAO,OAAO;AAAA,IACzD,WAAW;AAAA,EACb,GAAG,UAAU;AAAA,IACX,OAAO;AAAA,IACP;AAAA,EACF,CAAC,GAAsB,sBAAc,OAAO;AAAA,IAC1C,WAAW,GAAG,SAAS;AAAA,EACzB,GAAG,QAAQ,GAAG,eAAe,SAAS,mBAAmB,aAAa,IAAI,CAAC,CAAC;AAC9E,CAAC;AACD,IAAI,MAAuC;AACzC,QAAM,cAAc;AACtB;AACA,IAAO,gBAAQ;;;ADnKA,SAAR,YAA6B,QAAQ,UAAU,UAAU;AAC9D,MAAI,OAAO,aAAa,WAAW;AACjC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,QAAQ;AACjB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,QAAQ,QAAQ;AACnC,SAAO,WAAW,KAAK,UAAQ,KAAK,SAAS,aAAK;AACpD;;;AjDRA,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AASA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,oBAAkB;AACvB,UAAM,UAA6B,mBAAW,CAAC,OAAO,QAA4B,sBAAc,gBAAgB,OAAO,OAAO;AAAA,MAC5H;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,KAAK,CAAC,CAAE;AACX,QAAI,MAAuC;AACzC,cAAQ,cAAc;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,QAA2B,mBAAW,CAAC,OAAO,QAAQ;AAC1D,QAAM;AAAA,IACF,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,IAAI,OACJ,SAASA,QAAO,OAAO,CAAC,aAAa,aAAa,aAAa,SAAS,CAAC;AAC3E,QAAM;AAAA,IACJ;AAAA,EACF,IAAU,mBAAW,aAAa;AAClC,QAAM,YAAY,aAAa,UAAU,kBAAkB;AAC3D,QAAM,CAAC,SAAS,QAAQ,SAAS,IAAI,cAAS,SAAS;AACvD,QAAM,sBAAsB,YAAY,GAAG,SAAS,IAAI,SAAS,KAAK;AACtE,SAAO,QAA2B,sBAAc,SAAS,OAAO,OAAO;AAAA,IACrE,eAAW,mBAAAC,SAAW,sBAAsB,qBAAqB,WAAW,QAAQ,SAAS;AAAA,IAC7F;AAAA,EACF,GAAG,MAAM,CAAC,CAAC;AACb,CAAC;AACD,IAAM,cAAiC,mBAAW,CAAC,OAAO,QAAQ;AAChE,QAAM;AAAA,IACJ;AAAA,EACF,IAAU,mBAAW,aAAa;AAClC,QAAM,CAAC,QAAQ,SAAS,IAAU,iBAAS,CAAC,CAAC;AAC7C,QAAM;AAAA,IACF,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACF,IAAI,OACJ,SAASD,QAAO,OAAO,CAAC,aAAa,aAAa,iBAAiB,YAAY,YAAY,WAAW,OAAO,CAAC;AAChH,QAAM,cAAc,KAAK,QAAQ,CAAC,WAAW,CAAC;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,EACT,IAAI,mBAAmB,QAAQ;AAC/B,QAAM,YAAY,aAAa,UAAU,kBAAkB;AAC3D,QAAM,iBAAiB,YAAY,QAAQ,UAAU,QAAQ;AAC7D,QAAM,CAAC,YAAY,QAAQ,SAAS,IAAI,cAAS,SAAS;AAC1D,QAAM,kBAAc,mBAAAC,SAAW,WAAW;AAAA,IACxC,CAAC,GAAG,SAAS,YAAY,GAAG;AAAA,IAC5B,CAAC,GAAG,SAAS,MAAM,GAAG,cAAc;AAAA,EACtC,GAAG,kBAAkB,WAAW,eAAe,QAAQ,SAAS;AAChE,QAAM,eAAqB,gBAAQ,OAAO;AAAA,IACxC,WAAW;AAAA,MACT,UAAU,QAAM;AACd,kBAAU,UAAQ,CAAC,EAAE,OAAO,mBAAmB,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,MAC7D;AAAA,MACA,aAAa,QAAM;AACjB,kBAAU,UAAQ,KAAK,OAAO,eAAa,cAAc,EAAE,CAAC;AAAA,MAC9D;AAAA,IACF;AAAA,EACF,IAAI,CAAC,CAAC;AACN,SAAO,WAA8B,sBAAc,cAAc,UAAU;AAAA,IACzE,OAAO;AAAA,EACT,GAAsB,sBAAc,KAAK,OAAO,OAAO;AAAA,IACrD;AAAA,IACA,WAAW;AAAA,IACX,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,KAAK;AAAA,EAC7D,GAAG,WAAW,GAAG,QAAQ,CAAC,CAAC;AAC7B,CAAC;AACD,IAAM,SAAS,UAAU;AAAA,EACvB,SAAS;AAAA,EACT,aAAa;AACf,CAAC,EAAE,WAAW;AACd,IAAM,SAAS,UAAU;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,aAAa;AACf,CAAC,EAAE,KAAK;AACR,IAAM,SAAS,UAAU;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,aAAa;AACf,CAAC,EAAE,KAAK;AACR,IAAM,UAAU,UAAU;AAAA,EACxB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,aAAa;AACf,CAAC,EAAE,KAAK;AAER,IAAO,iBAAQ;", "names": ["React", "import_classnames", "React", "warning_default", "React", "locale", "en_US_default", "locale", "en_US_default", "locale", "en_US_default", "en_US_default", "en_US_default", "en_US_default", "locale", "import_react", "React", "locale", "en_US_default", "locale", "warning", "import_react", "init_es", "init_es", "generateColorPalettes", "generateNeutralColorPalettes", "init_es", "init_es", "React", "React", "init_es", "globalPrefixCls", "warning_default", "React", "import_react", "React", "init_es", "import_react", "version_default", "init_es", "init_es", "getAlphaColor", "__rest", "React", "version_default", "import_react", "init_es", "React", "warning", "_a", "_b", "React", "init_es", "React", "warning", "__rest", "warning_default", "warning_default", "locale", "statistic", "en_US_default", "__rest", "React", "React", "import_react", "__rest", "mql", "classNames", "__rest", "classNames"]}