{"version": 3, "sources": ["../../web-vitals/dist/web-vitals.js"], "sourcesContent": ["var e,t,n,i,r=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:\"v2-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12)}},a=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if(\"first-input\"===e&&!(\"PerformanceEventTiming\"in self))return;var n=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return n.observe({type:e,buffered:!0}),n}}catch(e){}},o=function(e,t){var n=function n(i){\"pagehide\"!==i.type&&\"hidden\"!==document.visibilityState||(e(i),t&&(removeEventListener(\"visibilitychange\",n,!0),removeEventListener(\"pagehide\",n,!0)))};addEventListener(\"visibilitychange\",n,!0),addEventListener(\"pagehide\",n,!0)},u=function(e){addEventListener(\"pageshow\",(function(t){t.persisted&&e(t)}),!0)},c=function(e,t,n){var i;return function(r){t.value>=0&&(r||n)&&(t.delta=t.value-(i||0),(t.delta||void 0===i)&&(i=t.value,e(t)))}},f=-1,s=function(){return\"hidden\"===document.visibilityState?0:1/0},m=function(){o((function(e){var t=e.timeStamp;f=t}),!0)},v=function(){return f<0&&(f=s(),m(),u((function(){setTimeout((function(){f=s(),m()}),0)}))),{get firstHiddenTime(){return f}}},d=function(e,t){var n,i=v(),o=r(\"FCP\"),f=function(e){\"first-contentful-paint\"===e.name&&(m&&m.disconnect(),e.startTime<i.firstHiddenTime&&(o.value=e.startTime,o.entries.push(e),n(!0)))},s=window.performance&&performance.getEntriesByName&&performance.getEntriesByName(\"first-contentful-paint\")[0],m=s?null:a(\"paint\",f);(s||m)&&(n=c(e,o,t),s&&f(s),u((function(i){o=r(\"FCP\"),n=c(e,o,t),requestAnimationFrame((function(){requestAnimationFrame((function(){o.value=performance.now()-i.timeStamp,n(!0)}))}))})))},p=!1,l=-1,h=function(e,t){p||(d((function(e){l=e.value})),p=!0);var n,i=function(t){l>-1&&e(t)},f=r(\"CLS\",0),s=0,m=[],v=function(e){if(!e.hadRecentInput){var t=m[0],i=m[m.length-1];s&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(s+=e.value,m.push(e)):(s=e.value,m=[e]),s>f.value&&(f.value=s,f.entries=m,n())}},h=a(\"layout-shift\",v);h&&(n=c(i,f,t),o((function(){h.takeRecords().map(v),n(!0)})),u((function(){s=0,l=-1,f=r(\"CLS\",0),n=c(i,f,t)})))},T={passive:!0,capture:!0},y=new Date,g=function(i,r){e||(e=r,t=i,n=new Date,w(removeEventListener),E())},E=function(){if(t>=0&&t<n-y){var r={entryType:\"first-input\",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+t};i.forEach((function(e){e(r)})),i=[]}},S=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,t){var n=function(){g(e,t),r()},i=function(){r()},r=function(){removeEventListener(\"pointerup\",n,T),removeEventListener(\"pointercancel\",i,T)};addEventListener(\"pointerup\",n,T),addEventListener(\"pointercancel\",i,T)}(t,e):g(t,e)}},w=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(t){return e(t,S,T)}))},L=function(n,f){var s,m=v(),d=r(\"FID\"),p=function(e){e.startTime<m.firstHiddenTime&&(d.value=e.processingStart-e.startTime,d.entries.push(e),s(!0))},l=a(\"first-input\",p);s=c(n,d,f),l&&o((function(){l.takeRecords().map(p),l.disconnect()}),!0),l&&u((function(){var a;d=r(\"FID\"),s=c(n,d,f),i=[],t=-1,e=null,w(addEventListener),a=p,i.push(a),E()}))},b={},F=function(e,t){var n,i=v(),f=r(\"LCP\"),s=function(e){var t=e.startTime;t<i.firstHiddenTime&&(f.value=t,f.entries.push(e),n())},m=a(\"largest-contentful-paint\",s);if(m){n=c(e,f,t);var d=function(){b[f.id]||(m.takeRecords().map(s),m.disconnect(),b[f.id]=!0,n(!0))};[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,d,{once:!0,capture:!0})})),o(d,!0),u((function(i){f=r(\"LCP\"),n=c(e,f,t),requestAnimationFrame((function(){requestAnimationFrame((function(){f.value=performance.now()-i.timeStamp,b[f.id]=!0,n(!0)}))}))}))}},P=function(e){var t,n=r(\"TTFB\");t=function(){try{var t=performance.getEntriesByType(\"navigation\")[0]||function(){var e=performance.timing,t={entryType:\"navigation\",startTime:0};for(var n in e)\"navigationStart\"!==n&&\"toJSON\"!==n&&(t[n]=Math.max(e[n]-e.navigationStart,0));return t}();if(n.value=n.delta=t.responseStart,n.value<0||n.value>performance.now())return;n.entries=[t],e(n)}catch(e){}},\"complete\"===document.readyState?setTimeout(t,0):addEventListener(\"load\",(function(){return setTimeout(t,0)}))};export{h as getCLS,d as getFCP,L as getFID,F as getLCP,P as getTTFB};\n"], "mappings": ";;;AAAA,IAAI;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAU;AAAV,IAAY,IAAE,SAASA,IAAEC,IAAE;AAAC,SAAM,EAAC,MAAKD,IAAE,OAAM,WAASC,KAAE,KAAGA,IAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,IAAG,MAAM,OAAO,KAAK,IAAI,GAAE,GAAG,EAAE,OAAO,KAAK,MAAM,gBAAc,KAAK,OAAO,CAAC,IAAE,IAAI,EAAC;AAAC;AAAxK,IAA0K,IAAE,SAASD,IAAEC,IAAE;AAAC,MAAG;AAAC,QAAG,oBAAoB,oBAAoB,SAASD,EAAC,GAAE;AAAC,UAAG,kBAAgBA,MAAG,EAAE,4BAA2B,MAAM;AAAO,UAAIE,KAAE,IAAI,oBAAqB,SAASF,IAAE;AAAC,eAAOA,GAAE,WAAW,EAAE,IAAIC,EAAC;AAAA,MAAC,CAAE;AAAE,aAAOC,GAAE,QAAQ,EAAC,MAAKF,IAAE,UAAS,KAAE,CAAC,GAAEE;AAAA,IAAC;AAAA,EAAC,SAAOF,IAAE;AAAA,EAAC;AAAC;AAArb,IAAub,IAAE,SAASA,IAAEC,IAAE;AAAC,MAAIC,KAAE,SAASA,GAAEC,IAAE;AAAC,mBAAaA,GAAE,QAAM,aAAW,SAAS,oBAAkBH,GAAEG,EAAC,GAAEF,OAAI,oBAAoB,oBAAmBC,IAAE,IAAE,GAAE,oBAAoB,YAAWA,IAAE,IAAE;AAAA,EAAG;AAAE,mBAAiB,oBAAmBA,IAAE,IAAE,GAAE,iBAAiB,YAAWA,IAAE,IAAE;AAAC;AAA/rB,IAAisB,IAAE,SAASF,IAAE;AAAC,mBAAiB,YAAY,SAASC,IAAE;AAAC,IAAAA,GAAE,aAAWD,GAAEC,EAAC;AAAA,EAAC,GAAG,IAAE;AAAC;AAA/wB,IAAixB,IAAE,SAASD,IAAEC,IAAEC,IAAE;AAAC,MAAIC;AAAE,SAAO,SAASC,IAAE;AAAC,IAAAH,GAAE,SAAO,MAAIG,MAAGF,QAAKD,GAAE,QAAMA,GAAE,SAAOE,MAAG,KAAIF,GAAE,SAAO,WAASE,QAAKA,KAAEF,GAAE,OAAMD,GAAEC,EAAC;AAAA,EAAG;AAAC;AAAj5B,IAAm5B,IAAE;AAAr5B,IAAw5B,IAAE,WAAU;AAAC,SAAM,aAAW,SAAS,kBAAgB,IAAE,IAAE;AAAC;AAAp9B,IAAs9B,IAAE,WAAU;AAAC,IAAG,SAASD,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAU,QAAEC;AAAA,EAAC,GAAG,IAAE;AAAC;AAA7gC,IAA+gC,IAAE,WAAU;AAAC,SAAO,IAAE,MAAI,IAAE,EAAE,GAAE,EAAE,GAAE,EAAG,WAAU;AAAC,eAAY,WAAU;AAAC,UAAE,EAAE,GAAE,EAAE;AAAA,IAAC,GAAG,CAAC;AAAA,EAAC,CAAE,IAAG,EAAC,IAAI,kBAAiB;AAAC,WAAO;AAAA,EAAC,EAAC;AAAC;AAA5oC,IAA8oC,IAAE,SAASD,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,EAAE,GAAEE,KAAE,EAAE,KAAK,GAAEC,KAAE,SAASN,IAAE;AAAC,iCAA2BA,GAAE,SAAOO,MAAGA,GAAE,WAAW,GAAEP,GAAE,YAAUG,GAAE,oBAAkBE,GAAE,QAAML,GAAE,WAAUK,GAAE,QAAQ,KAAKL,EAAC,GAAEE,GAAE,IAAE;AAAA,EAAG,GAAEM,KAAE,OAAO,eAAa,YAAY,oBAAkB,YAAY,iBAAiB,wBAAwB,EAAE,CAAC,GAAED,KAAEC,KAAE,OAAK,EAAE,SAAQF,EAAC;AAAE,GAACE,MAAGD,QAAKL,KAAE,EAAEF,IAAEK,IAAEJ,EAAC,GAAEO,MAAGF,GAAEE,EAAC,GAAE,EAAG,SAASL,IAAE;AAAC,IAAAE,KAAE,EAAE,KAAK,GAAEH,KAAE,EAAEF,IAAEK,IAAEJ,EAAC,GAAE,sBAAuB,WAAU;AAAC,4BAAuB,WAAU;AAAC,QAAAI,GAAE,QAAM,YAAY,IAAI,IAAEF,GAAE,WAAUD,GAAE,IAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAE;AAAtoD,IAAwoD,IAAE;AAA1oD,IAA6oD,IAAE;AAA/oD,IAAkpD,IAAE,SAASF,IAAEC,IAAE;AAAC,QAAI,EAAG,SAASD,IAAE;AAAC,QAAEA,GAAE;AAAA,EAAK,CAAE,GAAE,IAAE;AAAI,MAAIE,IAAEC,KAAE,SAASF,IAAE;AAAC,QAAE,MAAID,GAAEC,EAAC;AAAA,EAAC,GAAEK,KAAE,EAAE,OAAM,CAAC,GAAEE,KAAE,GAAED,KAAE,CAAC,GAAEE,KAAE,SAAST,IAAE;AAAC,QAAG,CAACA,GAAE,gBAAe;AAAC,UAAIC,KAAEM,GAAE,CAAC,GAAEJ,KAAEI,GAAEA,GAAE,SAAO,CAAC;AAAE,MAAAC,MAAGR,GAAE,YAAUG,GAAE,YAAU,OAAKH,GAAE,YAAUC,GAAE,YAAU,OAAKO,MAAGR,GAAE,OAAMO,GAAE,KAAKP,EAAC,MAAIQ,KAAER,GAAE,OAAMO,KAAE,CAACP,EAAC,IAAGQ,KAAEF,GAAE,UAAQA,GAAE,QAAME,IAAEF,GAAE,UAAQC,IAAEL,GAAE;AAAA,IAAE;AAAA,EAAC,GAAEQ,KAAE,EAAE,gBAAeD,EAAC;AAAE,EAAAC,OAAIR,KAAE,EAAEC,IAAEG,IAAEL,EAAC,GAAE,EAAG,WAAU;AAAC,IAAAS,GAAE,YAAY,EAAE,IAAID,EAAC,GAAEP,GAAE,IAAE;AAAA,EAAC,CAAE,GAAE,EAAG,WAAU;AAAC,IAAAM,KAAE,GAAE,IAAE,IAAGF,KAAE,EAAE,OAAM,CAAC,GAAEJ,KAAE,EAAEC,IAAEG,IAAEL,EAAC;AAAA,EAAC,CAAE;AAAE;AAAhlE,IAAklE,IAAE,EAAC,SAAQ,MAAG,SAAQ,KAAE;AAA1mE,IAA4mE,IAAE,oBAAI;AAAlnE,IAAunE,IAAE,SAASE,IAAEC,IAAE;AAAC,QAAI,IAAEA,IAAE,IAAED,IAAE,IAAE,oBAAI,QAAK,EAAE,mBAAmB,GAAE,EAAE;AAAE;AAAzrE,IAA2rE,IAAE,WAAU;AAAC,MAAG,KAAG,KAAG,IAAE,IAAE,GAAE;AAAC,QAAIC,KAAE,EAAC,WAAU,eAAc,MAAK,EAAE,MAAK,QAAO,EAAE,QAAO,YAAW,EAAE,YAAW,WAAU,EAAE,WAAU,iBAAgB,EAAE,YAAU,EAAC;AAAE,MAAE,QAAS,SAASJ,IAAE;AAAC,MAAAA,GAAEI,EAAC;AAAA,IAAC,CAAE,GAAE,IAAE,CAAC;AAAA,EAAC;AAAC;AAAp4E,IAAs4E,IAAE,SAASJ,IAAE;AAAC,MAAGA,GAAE,YAAW;AAAC,QAAIC,MAAGD,GAAE,YAAU,OAAK,oBAAI,SAAK,YAAY,IAAI,KAAGA,GAAE;AAAU,qBAAeA,GAAE,OAAK,SAASA,IAAEC,IAAE;AAAC,UAAIC,KAAE,WAAU;AAAC,UAAEF,IAAEC,EAAC,GAAEG,GAAE;AAAA,MAAC,GAAED,KAAE,WAAU;AAAC,QAAAC,GAAE;AAAA,MAAC,GAAEA,KAAE,WAAU;AAAC,4BAAoB,aAAYF,IAAE,CAAC,GAAE,oBAAoB,iBAAgBC,IAAE,CAAC;AAAA,MAAC;AAAE,uBAAiB,aAAYD,IAAE,CAAC,GAAE,iBAAiB,iBAAgBC,IAAE,CAAC;AAAA,IAAC,EAAEF,IAAED,EAAC,IAAE,EAAEC,IAAED,EAAC;AAAA,EAAC;AAAC;AAAzuF,IAA2uF,IAAE,SAASA,IAAE;AAAC,GAAC,aAAY,WAAU,cAAa,aAAa,EAAE,QAAS,SAASC,IAAE;AAAC,WAAOD,GAAEC,IAAE,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAn1F,IAAq1F,IAAE,SAASC,IAAEI,IAAE;AAAC,MAAIE,IAAED,KAAE,EAAE,GAAEI,KAAE,EAAE,KAAK,GAAEC,KAAE,SAASZ,IAAE;AAAC,IAAAA,GAAE,YAAUO,GAAE,oBAAkBI,GAAE,QAAMX,GAAE,kBAAgBA,GAAE,WAAUW,GAAE,QAAQ,KAAKX,EAAC,GAAEQ,GAAE,IAAE;AAAA,EAAE,GAAEK,KAAE,EAAE,eAAcD,EAAC;AAAE,EAAAJ,KAAE,EAAEN,IAAES,IAAEL,EAAC,GAAEO,MAAG,EAAG,WAAU;AAAC,IAAAA,GAAE,YAAY,EAAE,IAAID,EAAC,GAAEC,GAAE,WAAW;AAAA,EAAC,GAAG,IAAE,GAAEA,MAAG,EAAG,WAAU;AAAC,QAAIC;AAAE,IAAAH,KAAE,EAAE,KAAK,GAAEH,KAAE,EAAEN,IAAES,IAAEL,EAAC,GAAE,IAAE,CAAC,GAAE,IAAE,IAAG,IAAE,MAAK,EAAE,gBAAgB,GAAEQ,KAAEF,IAAE,EAAE,KAAKE,EAAC,GAAE,EAAE;AAAA,EAAC,CAAE;AAAC;AAA7qG,IAA+qG,IAAE,CAAC;AAAlrG,IAAorG,IAAE,SAASd,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,EAAE,GAAEG,KAAE,EAAE,KAAK,GAAEE,KAAE,SAASR,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAU,IAAAC,KAAEE,GAAE,oBAAkBG,GAAE,QAAML,IAAEK,GAAE,QAAQ,KAAKN,EAAC,GAAEE,GAAE;AAAA,EAAE,GAAEK,KAAE,EAAE,4BAA2BC,EAAC;AAAE,MAAGD,IAAE;AAAC,IAAAL,KAAE,EAAEF,IAAEM,IAAEL,EAAC;AAAE,QAAIU,KAAE,WAAU;AAAC,QAAEL,GAAE,EAAE,MAAIC,GAAE,YAAY,EAAE,IAAIC,EAAC,GAAED,GAAE,WAAW,GAAE,EAAED,GAAE,EAAE,IAAE,MAAGJ,GAAE,IAAE;AAAA,IAAE;AAAE,KAAC,WAAU,OAAO,EAAE,QAAS,SAASF,IAAE;AAAC,uBAAiBA,IAAEW,IAAE,EAAC,MAAK,MAAG,SAAQ,KAAE,CAAC;AAAA,IAAC,CAAE,GAAE,EAAEA,IAAE,IAAE,GAAE,EAAG,SAASR,IAAE;AAAC,MAAAG,KAAE,EAAE,KAAK,GAAEJ,KAAE,EAAEF,IAAEM,IAAEL,EAAC,GAAE,sBAAuB,WAAU;AAAC,8BAAuB,WAAU;AAAC,UAAAK,GAAE,QAAM,YAAY,IAAI,IAAEH,GAAE,WAAU,EAAEG,GAAE,EAAE,IAAE,MAAGJ,GAAE,IAAE;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAlsH,IAAosH,IAAE,SAASF,IAAE;AAAC,MAAIC,IAAEC,KAAE,EAAE,MAAM;AAAE,EAAAD,KAAE,WAAU;AAAC,QAAG;AAAC,UAAIA,KAAE,YAAY,iBAAiB,YAAY,EAAE,CAAC,KAAG,WAAU;AAAC,YAAID,KAAE,YAAY,QAAOC,KAAE,EAAC,WAAU,cAAa,WAAU,EAAC;AAAE,iBAAQC,MAAKF,GAAE,uBAAoBE,MAAG,aAAWA,OAAID,GAAEC,EAAC,IAAE,KAAK,IAAIF,GAAEE,EAAC,IAAEF,GAAE,iBAAgB,CAAC;AAAG,eAAOC;AAAA,MAAC,EAAE;AAAE,UAAGC,GAAE,QAAMA,GAAE,QAAMD,GAAE,eAAcC,GAAE,QAAM,KAAGA,GAAE,QAAM,YAAY,IAAI,EAAE;AAAO,MAAAA,GAAE,UAAQ,CAACD,EAAC,GAAED,GAAEE,EAAC;AAAA,IAAC,SAAOF,IAAE;AAAA,IAAC;AAAA,EAAC,GAAE,eAAa,SAAS,aAAW,WAAWC,IAAE,CAAC,IAAE,iBAAiB,QAAQ,WAAU;AAAC,WAAO,WAAWA,IAAE,CAAC;AAAA,EAAC,CAAE;AAAC;", "names": ["e", "t", "n", "i", "r", "o", "f", "m", "s", "v", "h", "d", "p", "l", "a"]}